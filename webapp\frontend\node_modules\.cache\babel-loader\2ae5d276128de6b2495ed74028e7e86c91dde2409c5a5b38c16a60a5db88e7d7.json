{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport { Box, Typography, Paper, Grid, Card, CardContent, Button, Chip, Alert, CircularProgress, Divider, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useParams } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const {\n    cantiereId\n  } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Import certificazione service\n        const certificazioneService = await import('../../services/certificazioneService');\n\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Carica statistiche certificazioni\n        const certificazioniPromise = certificazioneService.default.getCertificazioni(cantiereId).catch(err => {\n          console.error('Error loading certificazioni:', err);\n          return [];\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, certificazioniData] = await Promise.all([progressPromise, boqPromise, certificazioniPromise]);\n\n        // Aggiungi statistiche certificazioni ai dati del progress report\n        if (progressData.content && certificazioniData) {\n          const totaleCavi = progressData.content.totale_cavi || 0;\n          const caviCertificati = certificazioniData.length;\n          const percentualeCertificazione = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n\n          // Calcola certificazioni di oggi\n          const oggi = new Date().toDateString();\n          const certificazioniOggi = certificazioniData.filter(cert => new Date(cert.data_certificazione).toDateString() === oggi).length;\n          progressData.content.certificazioni = {\n            totale: caviCertificati,\n            percentuale: percentualeCertificazione,\n            oggi: certificazioniOggi,\n            rimanenti: totaleCavi - caviCertificati\n          };\n        }\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCA Report Avanzamento Lavori\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      sx: {\n        mb: 5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Totali\",\n          value: data.metri_totali,\n          unit: \"m\",\n          subtitle: \"Lunghezza complessiva del progetto\",\n          gradient: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Posati\",\n          value: data.metri_posati,\n          unit: \"m\",\n          subtitle: `${data.percentuale_avanzamento}% completato`,\n          gradient: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n          progress: data.percentuale_avanzamento,\n          trend: data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down',\n          trendValue: `${data.percentuale_avanzamento}%`,\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Rimanenti\",\n          value: data.metri_da_posare,\n          unit: \"m\",\n          subtitle: `${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`,\n          gradient: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Media/Giorno\",\n          value: data.media_giornaliera || 0,\n          unit: \"m\",\n          subtitle: data.giorni_stimati ? `${data.giorni_stimati} giorni lavorativi rimasti` : data.media_giornaliera > 0 ? 'Calcolo in corso' : 'Nessuna posa recente',\n          gradient: \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n          size: \"large\",\n          tooltip: data.giorni_lavorativi_effettivi ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.` : 'Media giornaliera basata sui giorni di lavoro effettivo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      sx: {\n        mb: 5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                sx: {\n                  color: '#3498db',\n                  mr: 1,\n                  fontSize: 28\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Stato Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#f8f9fa',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#2c3e50',\n                      mb: 1\n                    },\n                    children: data.totale_cavi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Cavi Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#e8f5e8',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#27ae60',\n                      mb: 1\n                    },\n                    children: data.cavi_posati\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: [\"Cavi Posati (\", data.percentuale_cavi, \"%)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Progresso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.percentuale_cavi, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  bgcolor: '#9c27b0',\n                  borderRadius: '50%',\n                  p: 1,\n                  mr: 2,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"\\uD83D\\uDD12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Stato Certificazioni Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), data.certificazioni ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                sx: {\n                  mb: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 2,\n                      bgcolor: '#e8f5e8',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      sx: {\n                        fontWeight: 700,\n                        color: '#27ae60',\n                        mb: 1\n                      },\n                      children: data.certificazioni.totale\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Certificati\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 2,\n                      bgcolor: '#fff3cd',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      sx: {\n                        fontWeight: 700,\n                        color: '#856404',\n                        mb: 1\n                      },\n                      children: data.certificazioni.rimanenti\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Da Certificare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700,\n                    color: '#9c27b0',\n                    mb: 1\n                  },\n                  children: [data.certificazioni.percentuale, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 1\n                  },\n                  children: \"Completamento Certificazioni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#999',\n                    fontSize: '0.75rem'\n                  },\n                  children: [data.certificazioni.oggi, \" certificazioni completate oggi\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: `${data.certificazioni.percentuale}%`,\n                    height: '100%',\n                    bgcolor: '#9c27b0',\n                    transition: 'width 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 2,\n                bgcolor: '#f8f9fa',\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Nessuna certificazione disponibile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(DateRangeIcon, {\n            sx: {\n              color: '#9b59b6',\n              mr: 1,\n              fontSize: 28\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"\\uD83D\\uDCC8 Attivit\\xE0 Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: data.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                border: '1px solid #e0e0e0',\n                borderRadius: 2,\n                bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                transition: 'all 0.2s',\n                '&:hover': {\n                  bgcolor: '#f5f5f5',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666',\n                  mb: 1\n                },\n                children: posa.data\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50'\n                },\n                children: [posa.metri, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 21\n              }, this), index === 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Pi\\xF9 recente\",\n                size: \"small\",\n                sx: {\n                  mt: 1,\n                  bgcolor: '#3498db',\n                  color: 'white',\n                  fontSize: '0.7rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), data.posa_recente.length > 5 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Accordion, {\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 49\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#3498db'\n                },\n                children: [\"Mostra tutti i \", data.posa_recente.length, \" record\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n                data: data.posa_recente.map(posa => ({\n                  data: posa.data,\n                  metri: `${posa.metri}m`\n                })),\n                columns: [{\n                  field: 'data',\n                  headerName: 'Data',\n                  width: 200\n                }, {\n                  field: 'metri',\n                  headerName: 'Metri Posati',\n                  width: 150,\n                  align: 'right'\n                }],\n                pagination: true,\n                pageSize: 10\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n        sx: {\n          color: '#8e44ad',\n          mr: 1,\n          fontSize: 28\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 590,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCB Bill of Quantities - Distinta Materiali\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 581,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 5,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(BoqChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 598,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 579,\n    columnNumber: 5\n  }, this);\n  const renderPosaPeriodoReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'warning.main'\n        },\n        children: \"Report Posa per Periodo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'warning.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.totale_metri_periodo, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [data.data_inizio, \" - \", data.data_fine]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'info.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: data.giorni_attivi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Giorni Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'success.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.media_giornaliera, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Giorno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'primary.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [Math.round(data.totale_metri_periodo / data.giorni_attivi * 7), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Settimana\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 636,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 674,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Dettaglio Posa Giornaliera\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.posa_giornaliera || [],\n        columns: [{\n          field: 'data',\n          headerName: 'Data',\n          width: 200\n        }, {\n          field: 'metri',\n          headerName: 'Metri Posati',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri}m`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 612,\n    columnNumber: 5\n  }, this);\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: dialogType === 'posa-periodo' ? 'Report Posa per Periodo' : 'Genera Report'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 701,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 706,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 714,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 11\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 711,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 704,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 757,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 755,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 700,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"report-main-container report-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 772,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 779,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 778,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            color: '#2c3e50',\n            mb: 2,\n            textAlign: 'center'\n          },\n          children: \"\\uD83C\\uDFAF Seleziona il tipo di report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 787,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: `report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`,\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('progress'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#3498db',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 805,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Avanzamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Panoramica lavori\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('boq'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#8e44ad',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Bill of Quantities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Distinta materiali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('posa-periodo'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#9b59b6',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Analisi temporale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: '400px'\n        },\n        children: [selectedReportType === 'progress' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 877,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 887,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 875,\n              columnNumber: 19\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 874,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"progress\",\n            title: \"Caricamento Report Avanzamento...\",\n            description: \"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"progress\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getProgressReport(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  progress: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying progress report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 872,\n          columnNumber: 13\n        }, this), selectedReportType === 'boq' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 939,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 950,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 19\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"boq\",\n            title: \"Caricamento Bill of Quantities...\",\n            description: \"Stiamo elaborando la distinta materiali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"boq\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  boq: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying BOQ report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 13\n        }, this), selectedReportType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.posaPeriodo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1005,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1004,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1015,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 19\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1002,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"action-required\",\n            reportType: \"posa-periodo\",\n            title: \"Seleziona un Periodo\",\n            description: \"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttivit\\xE0 del team.\",\n            actionLabel: \"Seleziona Periodo\",\n            onAction: () => {\n              setDialogType('posa-periodo');\n              // Set default date range (last month to today)\n              const today = new Date();\n              const lastMonth = new Date();\n              lastMonth.setMonth(today.getMonth() - 1);\n              setFormData({\n                ...formData,\n                data_inizio: lastMonth.toISOString().split('T')[0],\n                data_fine: today.toISOString().split('T')[0]\n              });\n              setOpenDialog(true);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 869,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 784,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 770,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"OtD3yym0uagQsYqqzydzEKYfeDo=\", false, function () {\n  return [useParams];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useParams", "AdminHomeButton", "reportService", "FilterableTable", "EmptyState", "MetricCard", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "cantiereId", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedReportType", "setSelectedReportType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobinaSpecifica", "posaPeriodo", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllReports", "certificazioneService", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "certificazioniPromise", "default", "getCertificazioni", "progressData", "boqData", "certificazioniData", "Promise", "all", "totaleCavi", "totale_cavi", "caviCertificati", "length", "percentualeCertificazione", "Math", "round", "oggi", "Date", "toDateString", "certificazioniOggi", "filter", "cert", "data_certificazione", "certificazioni", "totale", "percentuale", "<PERSON><PERSON><PERSON>", "generateReportWithFormat", "reportType", "format", "response", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleGenerateReport", "handleCloseDialog", "renderProgressReport", "data", "children", "sx", "display", "justifyContent", "alignItems", "mb", "p", "bgcolor", "borderRadius", "border", "variant", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "control", "checked", "onChange", "e", "target", "label", "mr", "container", "spacing", "item", "xs", "sm", "md", "title", "value", "metri_totali", "unit", "subtitle", "gradient", "size", "metri_posati", "percentuale_avanzamento", "trend", "trendValue", "metri_da_posare", "toFixed", "media_giornaliera", "giorni_stimati", "tooltip", "giorni_lavorativi_effettivi", "width", "height", "fontSize", "textAlign", "cavi_posati", "percentuale_cavi", "mt", "overflow", "transition", "posa_recente", "slice", "map", "posa", "index", "transform", "boxShadow", "metri", "expandIcon", "columns", "field", "headerName", "align", "pagination", "pageSize", "renderBoqReport", "renderPosaPeriodoReport", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "dataType", "renderCell", "row", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "type", "InputLabelProps", "shrink", "onClick", "disabled", "startIcon", "className", "my", "cursor", "flexDirection", "minHeight", "description", "onRetry", "then", "finally", "actionLabel", "onAction", "today", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useParams } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\n\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\n\nconst ReportCaviPageNew = () => {\n  const { cantiereId } = useParams();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Import certificazione service\n        const certificazioneService = await import('../../services/certificazioneService');\n\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        // Carica statistiche certificazioni\n        const certificazioniPromise = certificazioneService.default.getCertificazioni(cantiereId)\n          .catch(err => {\n            console.error('Error loading certificazioni:', err);\n            return [];\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, certificazioniData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          certificazioniPromise\n        ]);\n\n        // Aggiungi statistiche certificazioni ai dati del progress report\n        if (progressData.content && certificazioniData) {\n          const totaleCavi = progressData.content.totale_cavi || 0;\n          const caviCertificati = certificazioniData.length;\n          const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n          // Calcola certificazioni di oggi\n          const oggi = new Date().toDateString();\n          const certificazioniOggi = certificazioniData.filter(cert =>\n            new Date(cert.data_certificazione).toDateString() === oggi\n          ).length;\n\n          progressData.content.certificazioni = {\n            totale: caviCertificati,\n            percentuale: percentualeCertificazione,\n            oggi: certificazioniOggi,\n            rimanenti: totaleCavi - caviCertificati\n          };\n        }\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n\n\n  const renderProgressReport = (data) => (\n    <Box>\n      {/* Header con controlli migliorato */}\n      <Box sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📊 Report Avanzamento Lavori\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Metriche Principali - Cards Moderne con MetricCard */}\n      <Grid container spacing={4} sx={{ mb: 5 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Totali\"\n            value={data.metri_totali}\n            unit=\"m\"\n            subtitle=\"Lunghezza complessiva del progetto\"\n            gradient=\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\"\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Posati\"\n            value={data.metri_posati}\n            unit=\"m\"\n            subtitle={`${data.percentuale_avanzamento}% completato`}\n            gradient=\"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\"\n            progress={data.percentuale_avanzamento}\n            trend={data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down'}\n            trendValue={`${data.percentuale_avanzamento}%`}\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Rimanenti\"\n            value={data.metri_da_posare}\n            unit=\"m\"\n            subtitle={`${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`}\n            gradient=\"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\"\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Media/Giorno\"\n            value={data.media_giornaliera || 0}\n            unit=\"m\"\n            subtitle={\n              data.giorni_stimati\n                ? `${data.giorni_stimati} giorni lavorativi rimasti`\n                : (data.media_giornaliera > 0\n                    ? 'Calcolo in corso'\n                    : 'Nessuna posa recente')\n            }\n            gradient=\"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\"\n            size=\"large\"\n            tooltip={\n              data.giorni_lavorativi_effettivi\n                ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.`\n                : 'Media giornaliera basata sui giorni di lavoro effettivo'\n            }\n          />\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4, width: '100%' }}>\n          <ProgressChart data={data} />\n        </Box>\n      )}\n\n      {/* Dettagli Performance - Cards Informative */}\n      <Grid container spacing={4} sx={{ mb: 5 }}>\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <CableIcon sx={{ color: '#3498db', mr: 1, fontSize: 28 }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Cavi\n                </Typography>\n              </Box>\n              <Grid container spacing={2}>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                      {data.totale_cavi}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                      {data.cavi_posati}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Posati ({data.percentuale_cavi}%)\n                    </Typography>\n                  </Box>\n                </Grid>\n              </Grid>\n              <Box sx={{ mt: 2 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body2\">Progresso</Typography>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                    {data.percentuale_cavi}%\n                  </Typography>\n                </Box>\n                <Box sx={{\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                }}>\n                  <Box sx={{\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }} />\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                <Box sx={{\n                  bgcolor: '#9c27b0',\n                  borderRadius: '50%',\n                  p: 1,\n                  mr: 2,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  <Typography variant=\"h6\" sx={{ color: 'white', fontWeight: 'bold' }}>\n                    🔒\n                  </Typography>\n                </Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Certificazioni Cavi\n                </Typography>\n              </Box>\n\n              {data.certificazioni ? (\n                <>\n                  <Grid container spacing={2} sx={{ mb: 3 }}>\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                        <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                          {data.certificazioni.totale}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                          Certificati\n                        </Typography>\n                      </Box>\n                    </Grid>\n\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>\n                        <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#856404', mb: 1 }}>\n                          {data.certificazioni.rimanenti}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                          Da Certificare\n                        </Typography>\n                      </Box>\n                    </Grid>\n                  </Grid>\n\n                  <Box sx={{ textAlign: 'center', mb: 2 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#9c27b0', mb: 1 }}>\n                      {data.certificazioni.percentuale}%\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      Completamento Certificazioni\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#999', fontSize: '0.75rem' }}>\n                      {data.certificazioni.oggi} certificazioni completate oggi\n                    </Typography>\n                  </Box>\n\n                  {/* Progress bar certificazioni */}\n                  <Box sx={{\n                    width: '100%',\n                    height: 8,\n                    bgcolor: '#e0e0e0',\n                    borderRadius: 4,\n                    overflow: 'hidden'\n                  }}>\n                    <Box sx={{\n                      width: `${data.certificazioni.percentuale}%`,\n                      height: '100%',\n                      bgcolor: '#9c27b0',\n                      transition: 'width 0.3s ease'\n                    }} />\n                  </Box>\n                </>\n              ) : (\n                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                  <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                    Nessuna certificazione disponibile\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n\n\n      {/* Attività Recente - Design Migliorato */}\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Card sx={{ border: '1px solid #e0e0e0' }}>\n          <CardContent sx={{ p: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <DateRangeIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                📈 Attività Recente\n              </Typography>\n            </Box>\n\n            {/* Mostra solo gli ultimi 5 record in formato card per mobile-friendly */}\n            <Grid container spacing={2}>\n              {data.posa_recente.slice(0, 5).map((posa, index) => (\n                <Grid item xs={12} sm={6} md={4} key={index}>\n                  <Box sx={{\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    borderRadius: 2,\n                    bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                    transition: 'all 0.2s',\n                    '&:hover': {\n                      bgcolor: '#f5f5f5',\n                      transform: 'translateY(-2px)',\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                    }\n                  }}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      {posa.data}\n                    </Typography>\n                    <Typography variant=\"h5\" sx={{ fontWeight: 700, color: '#2c3e50' }}>\n                      {posa.metri}m\n                    </Typography>\n                    {index === 0 && (\n                      <Chip\n                        label=\"Più recente\"\n                        size=\"small\"\n                        sx={{\n                          mt: 1,\n                          bgcolor: '#3498db',\n                          color: 'white',\n                          fontSize: '0.7rem'\n                        }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n\n            {/* Link per vedere tutti i dati se ce ne sono di più */}\n            {data.posa_recente.length > 5 && (\n              <Box sx={{ mt: 3, textAlign: 'center' }}>\n                <Accordion>\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Typography variant=\"body2\" sx={{ color: '#3498db' }}>\n                      Mostra tutti i {data.posa_recente.length} record\n                    </Typography>\n                  </AccordionSummary>\n                  <AccordionDetails>\n                    <FilterableTable\n                      data={data.posa_recente.map(posa => ({\n                        data: posa.data,\n                        metri: `${posa.metri}m`\n                      }))}\n                      columns={[\n                        { field: 'data', headerName: 'Data', width: 200 },\n                        { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                      ]}\n                      pagination={true}\n                      pageSize={10}\n                    />\n                  </AccordionDetails>\n                </Accordion>\n              </Box>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n\n  const renderBoqReport = (data) => (\n    <Box>\n      {/* Header migliorato */}\n      <Box sx={{\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      }}>\n        <ListIcon sx={{ color: '#8e44ad', mr: 1, fontSize: 28 }} />\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📋 Bill of Quantities - Distinta Materiali\n        </Typography>\n      </Box>\n\n      {/* Grafici BOQ se disponibili */}\n      {showCharts && (\n        <Box sx={{ mb: 5, width: '100%' }}>\n          <BoqChart data={data} />\n        </Box>\n      )}\n\n\n    </Box>\n  );\n\n\n\n\n\n  const renderPosaPeriodoReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n          Report Posa per Periodo\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Statistiche Periodo - Layout Orizzontale */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.totale_metri_periodo}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Totali</Typography>\n            <Typography variant=\"caption\">{data.data_inizio} - {data.data_fine}</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.giorni_attivi}\n            </Typography>\n            <Typography variant=\"body1\">Giorni Attivi</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.media_giornaliera}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Giorno</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Settimana</Typography>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <TimelineChart data={data} />\n        </Box>\n      )}\n\n      {/* Posa Giornaliera */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Dettaglio Posa Giornaliera\n        </Typography>\n        <FilterableTable\n          data={data.posa_giornaliera || []}\n          columns={[\n            { field: 'data', headerName: 'Data', width: 200 },\n            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri}m` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {dialogType === 'posa-periodo' ? 'Report Posa per Periodo' : 'Genera Report'}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box className=\"report-main-container report-fade-in\">\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports Navigation */}\n      <Box sx={{ mt: 3 }}>\n        {/* Report Navigation - Design Compatto */}\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50', mb: 2, textAlign: 'center' }}>\n            🎯 Seleziona il tipo di report\n          </Typography>\n          <Grid container spacing={2}>\n            {/* Report Avanzamento */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                className={`report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`}\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('progress')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <AssessmentIcon sx={{ fontSize: 32, color: '#3498db', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Avanzamento\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Panoramica lavori\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Bill of Quantities */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('boq')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <ListIcon sx={{ fontSize: 32, color: '#8e44ad', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Bill of Quantities\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Distinta materiali\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n\n\n            {/* Posa per Periodo */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('posa-periodo')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <TimelineIcon sx={{ fontSize: 32, color: '#9b59b6', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Posa per Periodo\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Analisi temporale\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Report Content */}\n        <Box sx={{ minHeight: '400px' }}>\n          {/* Progress Report */}\n          {selectedReportType === 'progress' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.progress ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderProgressReport(reportsData.progress)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"progress\"\n                  title=\"Caricamento Report Avanzamento...\"\n                  description=\"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"progress\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Bill of Quantities */}\n          {selectedReportType === 'boq' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.boq ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBoqReport(reportsData.boq)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"boq\"\n                  title=\"Caricamento Bill of Quantities...\"\n                  description=\"Stiamo elaborando la distinta materiali\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"boq\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n\n\n          {/* Posa per Periodo Report */}\n          {selectedReportType === 'posa-periodo' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.posaPeriodo ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n                </Box>\n              ) : (\n                <EmptyState\n                  type=\"action-required\"\n                  reportType=\"posa-periodo\"\n                  title=\"Seleziona un Periodo\"\n                  description=\"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttività del team.\"\n                  actionLabel=\"Seleziona Periodo\"\n                  onAction={() => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  }}\n                />\n              )}\n            </Paper>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,0BAA0B;AACjC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EAEXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EAEPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAE5BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EAEtBC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,oCAAoC;;AAG3D;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAW,CAAC,GAAGf,SAAS,CAAC,CAAC;EAElC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmE,KAAK,EAAEC,QAAQ,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1E,QAAQ,CAAC,UAAU,CAAC;EACxE,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvC6E,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAAC;IAC7CmF,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwF,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjCvB,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAMwB,qBAAqB,GAAG,MAAM,MAAM,CAAC,sCAAsC,CAAC;;QAElF;QACA,MAAMC,eAAe,GAAGxC,aAAa,CAACyC,iBAAiB,CAAC5B,UAAU,EAAE,OAAO,CAAC,CACzE6B,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC5B,KAAK,CAAC,gCAAgC,EAAE2B,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAG9C,aAAa,CAAC+C,mBAAmB,CAAClC,UAAU,EAAE,OAAO,CAAC,CACtE6B,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC5B,KAAK,CAAC,2BAA2B,EAAE2B,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAMG,qBAAqB,GAAGT,qBAAqB,CAACU,OAAO,CAACC,iBAAiB,CAACrC,UAAU,CAAC,CACtF6B,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC5B,KAAK,CAAC,+BAA+B,EAAE2B,GAAG,CAAC;UACnD,OAAO,EAAE;QACX,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACQ,YAAY,EAAEC,OAAO,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpEf,eAAe,EACfM,UAAU,EACVE,qBAAqB,CACtB,CAAC;;QAEF;QACA,IAAIG,YAAY,CAACN,OAAO,IAAIQ,kBAAkB,EAAE;UAC9C,MAAMG,UAAU,GAAGL,YAAY,CAACN,OAAO,CAACY,WAAW,IAAI,CAAC;UACxD,MAAMC,eAAe,GAAGL,kBAAkB,CAACM,MAAM;UACjD,MAAMC,yBAAyB,GAAGJ,UAAU,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAEJ,eAAe,GAAGF,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;UAEvG;UACA,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;UACtC,MAAMC,kBAAkB,GAAGb,kBAAkB,CAACc,MAAM,CAACC,IAAI,IACvD,IAAIJ,IAAI,CAACI,IAAI,CAACC,mBAAmB,CAAC,CAACJ,YAAY,CAAC,CAAC,KAAKF,IACxD,CAAC,CAACJ,MAAM;UAERR,YAAY,CAACN,OAAO,CAACyB,cAAc,GAAG;YACpCC,MAAM,EAAEb,eAAe;YACvBc,WAAW,EAAEZ,yBAAyB;YACtCG,IAAI,EAAEG,kBAAkB;YACxBO,SAAS,EAAEjB,UAAU,GAAGE;UAC1B,CAAC;QACH;;QAEA;QACA3B,cAAc,CAAC;UACbC,QAAQ,EAAEmB,YAAY,CAACN,OAAO;UAC9BZ,GAAG,EAAEmB,OAAO,CAACP,OAAO;UACpBX,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIgB,YAAY,CAACN,OAAO,IAAIO,OAAO,CAACP,OAAO,EAAE;UAC3C5B,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO0B,GAAG,EAAE;QACZ;QACAC,OAAO,CAAC5B,KAAK,CAAC,mCAAmC,EAAE2B,GAAG,CAAC;QACvD1B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIF,UAAU,EAAE;MACdyB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACzB,UAAU,CAAC,CAAC;;EAIhB;EACA,MAAM6D,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF7D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI4D,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAM7E,aAAa,CAACyC,iBAAiB,CAAC5B,UAAU,EAAE+D,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAM7E,aAAa,CAAC+C,mBAAmB,CAAClC,UAAU,EAAE+D,MAAM,CAAC;UACtE;QAEF,KAAK,cAAc;UACjB,IAAI,CAACpD,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDX,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA4D,QAAQ,GAAG,MAAM7E,aAAa,CAAC8E,uBAAuB,CACpDjE,UAAU,EACVW,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClBgD,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAIG,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIH,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtE5C,cAAc,CAACiD,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACL,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAAChC;UACpF,CAAC,CAAC,CAAC;QACL;MACF,CAAC,MAAM;QACL;QACA,IAAIgC,QAAQ,CAACI,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACN,QAAQ,CAACI,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOtC,GAAG,EAAE;MACZC,OAAO,CAAC5B,KAAK,CAAC,sCAAsC,EAAE2B,GAAG,CAAC;MAC1D1B,QAAQ,CAAC0B,GAAG,CAACyC,MAAM,IAAIzC,GAAG,CAAC0C,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAID,MAAMuE,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMZ,wBAAwB,CAACtD,UAAU,EAAEI,QAAQ,CAACE,OAAO,CAAC;IAC5DP,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMoE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpE,aAAa,CAAC,KAAK,CAAC;IACpBF,QAAQ,CAAC,IAAI,CAAC;IACdQ,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAID,MAAM2D,oBAAoB,GAAIC,IAAI,iBAChCjF,OAAA,CAACzD,GAAG;IAAA2I,QAAA,gBAEFlF,OAAA,CAACzD,GAAG;MAAC4I,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAT,QAAA,gBACAlF,OAAA,CAACxD,UAAU;QAACoJ,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAEpE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblG,OAAA,CAACjC,gBAAgB;QACfoI,OAAO,eACLnG,OAAA,CAAClC,MAAM;UACLsI,OAAO,EAAExE,UAAW;UACpByE,QAAQ,EAAGC,CAAC,IAAKzE,aAAa,CAACyE,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDN,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDM,KAAK,eACHxG,OAAA,CAACzD,GAAG;UAAC4I,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjDlF,OAAA,CAACX,aAAa;YAAC8F,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNlG,OAAA,CAACtD,IAAI;MAACgK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxClF,OAAA,CAACtD,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BlF,OAAA,CAACL,UAAU;UACTqH,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAACiC,YAAa;UACzBC,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAC,oCAAoC;UAC7CC,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPlG,OAAA,CAACtD,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BlF,OAAA,CAACL,UAAU;UACTqH,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAACsC,YAAa;UACzBJ,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAGnC,IAAI,CAACuC,uBAAuB,cAAe;UACxDH,QAAQ,EAAC,mDAAmD;UAC5D7F,QAAQ,EAAEyD,IAAI,CAACuC,uBAAwB;UACvCC,KAAK,EAAExC,IAAI,CAACuC,uBAAuB,GAAG,EAAE,GAAG,IAAI,GAAGvC,IAAI,CAACuC,uBAAuB,GAAG,EAAE,GAAG,MAAM,GAAG,MAAO;UACtGE,UAAU,EAAE,GAAGzC,IAAI,CAACuC,uBAAuB,GAAI;UAC/CF,IAAI,EAAC;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPlG,OAAA,CAACtD,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BlF,OAAA,CAACL,UAAU;UACTqH,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAEhC,IAAI,CAAC0C,eAAgB;UAC5BR,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAG,CAAC,GAAG,GAAGnC,IAAI,CAACuC,uBAAuB,EAAEI,OAAO,CAAC,CAAC,CAAC,iBAAkB;UAC9EP,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPlG,OAAA,CAACtD,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BlF,OAAA,CAACL,UAAU;UACTqH,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAAC4C,iBAAiB,IAAI,CAAE;UACnCV,IAAI,EAAC,GAAG;UACRC,QAAQ,EACNnC,IAAI,CAAC6C,cAAc,GACf,GAAG7C,IAAI,CAAC6C,cAAc,4BAA4B,GACjD7C,IAAI,CAAC4C,iBAAiB,GAAG,CAAC,GACvB,kBAAkB,GAClB,sBACT;UACDR,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC,OAAO;UACZS,OAAO,EACL9C,IAAI,CAAC+C,2BAA2B,GAC5B,gBAAgB/C,IAAI,CAAC+C,2BAA2B,oFAAoF,GACpI;QACL;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNtE,UAAU,iBACT5B,OAAA,CAACzD,GAAG;MAAC4I,EAAE,EAAE;QAAEI,EAAE,EAAE,CAAC;QAAE0C,KAAK,EAAE;MAAO,CAAE;MAAA/C,QAAA,eAChClF,OAAA,CAACJ,aAAa;QAACqF,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDlG,OAAA,CAACtD,IAAI;MAACgK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxClF,OAAA,CAACtD,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBlF,OAAA,CAACrD,IAAI;UAACwI,EAAE,EAAE;YAAE+C,MAAM,EAAE,MAAM;YAAEvC,MAAM,EAAE;UAAoB,CAAE;UAAAT,QAAA,eACxDlF,OAAA,CAACpD,WAAW;YAACuI,EAAE,EAAE;cAAEK,CAAC,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACxBlF,OAAA,CAACzD,GAAG;cAAC4I,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACxDlF,OAAA,CAACjB,SAAS;gBAACoG,EAAE,EAAE;kBAAEW,KAAK,EAAE,SAAS;kBAAEW,EAAE,EAAE,CAAC;kBAAE0B,QAAQ,EAAE;gBAAG;cAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5DlG,OAAA,CAACxD,UAAU;gBAACoJ,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,EAAC;cAEpE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlG,OAAA,CAACtD,IAAI;cAACgK,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAzB,QAAA,gBACzBlF,OAAA,CAACtD,IAAI;gBAACkK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACflF,OAAA,CAACzD,GAAG;kBAAC4I,EAAE,EAAE;oBAAEiD,SAAS,EAAE,QAAQ;oBAAE5C,CAAC,EAAE,CAAC;oBAAEC,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAR,QAAA,gBAC1ElF,OAAA,CAACxD,UAAU;oBAACoJ,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE,GAAG;sBAAEC,KAAK,EAAE,SAAS;sBAAEP,EAAE,EAAE;oBAAE,CAAE;oBAAAL,QAAA,EACvED,IAAI,CAAChC;kBAAW;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACblG,OAAA,CAACxD,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO,CAAE;oBAAAZ,QAAA,EAAC;kBAEnD;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPlG,OAAA,CAACtD,IAAI;gBAACkK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACflF,OAAA,CAACzD,GAAG;kBAAC4I,EAAE,EAAE;oBAAEiD,SAAS,EAAE,QAAQ;oBAAE5C,CAAC,EAAE,CAAC;oBAAEC,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAR,QAAA,gBAC1ElF,OAAA,CAACxD,UAAU;oBAACoJ,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE,GAAG;sBAAEC,KAAK,EAAE,SAAS;sBAAEP,EAAE,EAAE;oBAAE,CAAE;oBAAAL,QAAA,EACvED,IAAI,CAACoD;kBAAW;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACblG,OAAA,CAACxD,UAAU;oBAACoJ,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO,CAAE;oBAAAZ,QAAA,GAAC,eACpC,EAACD,IAAI,CAACqD,gBAAgB,EAAC,IACtC;kBAAA;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACPlG,OAAA,CAACzD,GAAG;cAAC4I,EAAE,EAAE;gBAAEoD,EAAE,EAAE;cAAE,CAAE;cAAArD,QAAA,gBACjBlF,OAAA,CAACzD,GAAG;gBAAC4I,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACnElF,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAAS;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDlG,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE;kBAAI,CAAE;kBAAAX,QAAA,GACjDD,IAAI,CAACqD,gBAAgB,EAAC,GACzB;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNlG,OAAA,CAACzD,GAAG;gBAAC4I,EAAE,EAAE;kBACP8C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,CAAC;kBACTzC,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,CAAC;kBACf8C,QAAQ,EAAE;gBACZ,CAAE;gBAAAtD,QAAA,eACAlF,OAAA,CAACzD,GAAG;kBAAC4I,EAAE,EAAE;oBACP8C,KAAK,EAAE,GAAGhD,IAAI,CAACqD,gBAAgB,GAAG;oBAClCJ,MAAM,EAAE,MAAM;oBACdzC,OAAO,EAAE,SAAS;oBAClBgD,UAAU,EAAE;kBACd;gBAAE;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPlG,OAAA,CAACtD,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBlF,OAAA,CAACrD,IAAI;UAACwI,EAAE,EAAE;YAAE+C,MAAM,EAAE,MAAM;YAAEvC,MAAM,EAAE;UAAoB,CAAE;UAAAT,QAAA,eACxDlF,OAAA,CAACpD,WAAW;YAACuI,EAAE,EAAE;cAAEK,CAAC,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACxBlF,OAAA,CAACzD,GAAG;cAAC4I,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACxDlF,OAAA,CAACzD,GAAG;gBAAC4I,EAAE,EAAE;kBACPM,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,KAAK;kBACnBF,CAAC,EAAE,CAAC;kBACJiB,EAAE,EAAE,CAAC;kBACLrB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE;gBAClB,CAAE;gBAAAH,QAAA,eACAlF,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,OAAO;oBAAED,UAAU,EAAE;kBAAO,CAAE;kBAAAX,QAAA,EAAC;gBAErE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNlG,OAAA,CAACxD,UAAU;gBAACoJ,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,EAAC;cAEpE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAELjB,IAAI,CAACnB,cAAc,gBAClB9D,OAAA,CAAAE,SAAA;cAAAgF,QAAA,gBACElF,OAAA,CAACtD,IAAI;gBAACgK,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAACxB,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACxClF,OAAA,CAACtD,IAAI;kBAACkK,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAA3B,QAAA,eACflF,OAAA,CAACzD,GAAG;oBAAC4I,EAAE,EAAE;sBAAEiD,SAAS,EAAE,QAAQ;sBAAE5C,CAAC,EAAE,CAAC;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAAR,QAAA,gBAC1ElF,OAAA,CAACxD,UAAU;sBAACoJ,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEU,UAAU,EAAE,GAAG;wBAAEC,KAAK,EAAE,SAAS;wBAAEP,EAAE,EAAE;sBAAE,CAAE;sBAAAL,QAAA,EACvED,IAAI,CAACnB,cAAc,CAACC;oBAAM;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACblG,OAAA,CAACxD,UAAU;sBAACoJ,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEW,KAAK,EAAE;sBAAO,CAAE;sBAAAZ,QAAA,EAAC;oBAEnD;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAEPlG,OAAA,CAACtD,IAAI;kBAACkK,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAA3B,QAAA,eACflF,OAAA,CAACzD,GAAG;oBAAC4I,EAAE,EAAE;sBAAEiD,SAAS,EAAE,QAAQ;sBAAE5C,CAAC,EAAE,CAAC;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAAR,QAAA,gBAC1ElF,OAAA,CAACxD,UAAU;sBAACoJ,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEU,UAAU,EAAE,GAAG;wBAAEC,KAAK,EAAE,SAAS;wBAAEP,EAAE,EAAE;sBAAE,CAAE;sBAAAL,QAAA,EACvED,IAAI,CAACnB,cAAc,CAACG;oBAAS;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACblG,OAAA,CAACxD,UAAU;sBAACoJ,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEW,KAAK,EAAE;sBAAO,CAAE;sBAAAZ,QAAA,EAAC;oBAEnD;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEPlG,OAAA,CAACzD,GAAG;gBAAC4I,EAAE,EAAE;kBAAEiD,SAAS,EAAE,QAAQ;kBAAE7C,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACtClF,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE,CAAE;kBAAAL,QAAA,GACvED,IAAI,CAACnB,cAAc,CAACE,WAAW,EAAC,GACnC;gBAAA;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACblG,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAE,CAAE;kBAAAL,QAAA,EAAC;gBAE1D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACblG,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,SAAS;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEqC,QAAQ,EAAE;kBAAU,CAAE;kBAAAjD,QAAA,GACtED,IAAI,CAACnB,cAAc,CAACP,IAAI,EAAC,iCAC5B;gBAAA;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNlG,OAAA,CAACzD,GAAG;gBAAC4I,EAAE,EAAE;kBACP8C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,CAAC;kBACTzC,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,CAAC;kBACf8C,QAAQ,EAAE;gBACZ,CAAE;gBAAAtD,QAAA,eACAlF,OAAA,CAACzD,GAAG;kBAAC4I,EAAE,EAAE;oBACP8C,KAAK,EAAE,GAAGhD,IAAI,CAACnB,cAAc,CAACE,WAAW,GAAG;oBAC5CkE,MAAM,EAAE,MAAM;oBACdzC,OAAO,EAAE,SAAS;oBAClBgD,UAAU,EAAE;kBACd;gBAAE;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,eACN,CAAC,gBAEHlG,OAAA,CAACzD,GAAG;cAAC4I,EAAE,EAAE;gBAAEiD,SAAS,EAAE,QAAQ;gBAAE5C,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAR,QAAA,eAC1ElF,OAAA,CAACxD,UAAU;gBAACoJ,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAKNjB,IAAI,CAACyD,YAAY,IAAIzD,IAAI,CAACyD,YAAY,CAACvF,MAAM,GAAG,CAAC,iBAChDnD,OAAA,CAACrD,IAAI;MAACwI,EAAE,EAAE;QAAEQ,MAAM,EAAE;MAAoB,CAAE;MAAAT,QAAA,eACxClF,OAAA,CAACpD,WAAW;QAACuI,EAAE,EAAE;UAAEK,CAAC,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACxBlF,OAAA,CAACzD,GAAG;UAAC4I,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,gBACxDlF,OAAA,CAACnB,aAAa;YAACsG,EAAE,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEW,EAAE,EAAE,CAAC;cAAE0B,QAAQ,EAAE;YAAG;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChElG,OAAA,CAACxD,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAZ,QAAA,EAAC;UAEpE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNlG,OAAA,CAACtD,IAAI;UAACgK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,EACxBD,IAAI,CAACyD,YAAY,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7C9I,OAAA,CAACtD,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC9BlF,OAAA,CAACzD,GAAG;cAAC4I,EAAE,EAAE;gBACPK,CAAC,EAAE,CAAC;gBACJG,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,CAAC;gBACfD,OAAO,EAAEqD,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;gBAC5CL,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE;kBACThD,OAAO,EAAE,SAAS;kBAClBsD,SAAS,EAAE,kBAAkB;kBAC7BC,SAAS,EAAE;gBACb;cACF,CAAE;cAAA9D,QAAA,gBACAlF,OAAA,CAACxD,UAAU;gBAACoJ,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE,MAAM;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EACtD2D,IAAI,CAAC5D;cAAI;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACblG,OAAA,CAACxD,UAAU;gBAACoJ,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,GAChE2D,IAAI,CAACI,KAAK,EAAC,GACd;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ4C,KAAK,KAAK,CAAC,iBACV9I,OAAA,CAAClD,IAAI;gBACH0J,KAAK,EAAC,gBAAa;gBACnBc,IAAI,EAAC,OAAO;gBACZnC,EAAE,EAAE;kBACFoD,EAAE,EAAE,CAAC;kBACL9C,OAAO,EAAE,SAAS;kBAClBK,KAAK,EAAE,OAAO;kBACdqC,QAAQ,EAAE;gBACZ;cAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GA/B8B4C,KAAK;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGNjB,IAAI,CAACyD,YAAY,CAACvF,MAAM,GAAG,CAAC,iBAC3BnD,OAAA,CAACzD,GAAG;UAAC4I,EAAE,EAAE;YAAEoD,EAAE,EAAE,CAAC;YAAEH,SAAS,EAAE;UAAS,CAAE;UAAAlD,QAAA,eACtClF,OAAA,CAACrC,SAAS;YAAAuH,QAAA,gBACRlF,OAAA,CAACpC,gBAAgB;cAACsL,UAAU,eAAElJ,OAAA,CAACb,cAAc;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAhB,QAAA,eAC/ClF,OAAA,CAACxD,UAAU;gBAACoJ,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,GAAC,iBACrC,EAACD,IAAI,CAACyD,YAAY,CAACvF,MAAM,EAAC,SAC3C;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACnBlG,OAAA,CAACnC,gBAAgB;cAAAqH,QAAA,eACflF,OAAA,CAACP,eAAe;gBACdwF,IAAI,EAAEA,IAAI,CAACyD,YAAY,CAACE,GAAG,CAACC,IAAI,KAAK;kBACnC5D,IAAI,EAAE4D,IAAI,CAAC5D,IAAI;kBACfgE,KAAK,EAAE,GAAGJ,IAAI,CAACI,KAAK;gBACtB,CAAC,CAAC,CAAE;gBACJE,OAAO,EAAE,CACP;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEpB,KAAK,EAAE;gBAAI,CAAC,EACjD;kBAAEmB,KAAK,EAAE,OAAO;kBAAEC,UAAU,EAAE,cAAc;kBAAEpB,KAAK,EAAE,GAAG;kBAAEqB,KAAK,EAAE;gBAAQ,CAAC,CAC1E;gBACFC,UAAU,EAAE,IAAK;gBACjBC,QAAQ,EAAE;cAAG;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMuD,eAAe,GAAIxE,IAAI,iBAC3BjF,OAAA,CAACzD,GAAG;IAAA2I,QAAA,gBAEFlF,OAAA,CAACzD,GAAG;MAAC4I,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAT,QAAA,gBACAlF,OAAA,CAAC3B,QAAQ;QAAC8G,EAAE,EAAE;UAAEW,KAAK,EAAE,SAAS;UAAEW,EAAE,EAAE,CAAC;UAAE0B,QAAQ,EAAE;QAAG;MAAE;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DlG,OAAA,CAACxD,UAAU;QAACoJ,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAEpE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGLtE,UAAU,iBACT5B,OAAA,CAACzD,GAAG;MAAC4I,EAAE,EAAE;QAAEI,EAAE,EAAE,CAAC;QAAE0C,KAAK,EAAE;MAAO,CAAE;MAAA/C,QAAA,eAChClF,OAAA,CAACH,QAAQ;QAACoF,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGE,CACN;EAMD,MAAMwD,uBAAuB,GAAIzE,IAAI,iBACnCjF,OAAA,CAACzD,GAAG;IAAA2I,QAAA,gBAEFlF,OAAA,CAACzD,GAAG;MAAC4I,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzFlF,OAAA,CAACxD,UAAU;QAACoJ,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAZ,QAAA,EAAC;MAEzE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblG,OAAA,CAACjC,gBAAgB;QACfoI,OAAO,eACLnG,OAAA,CAAClC,MAAM;UACLsI,OAAO,EAAExE,UAAW;UACpByE,QAAQ,EAAGC,CAAC,IAAKzE,aAAa,CAACyE,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDN,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDM,KAAK,eACHxG,OAAA,CAACzD,GAAG;UAAC4I,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjDlF,OAAA,CAACX,aAAa;YAAC8F,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNlG,OAAA,CAACtD,IAAI;MAACgK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxClF,OAAA,CAACtD,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBlF,OAAA,CAACvD,KAAK;UAAC0I,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE4C,SAAS,EAAE,QAAQ;YAAE3C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChFlF,OAAA,CAACxD,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxDD,IAAI,CAAC0E,oBAAoB,EAAC,GAC7B;UAAA;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblG,OAAA,CAACxD,UAAU;YAACoJ,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrDlG,OAAA,CAACxD,UAAU;YAACoJ,OAAO,EAAC,SAAS;YAAAV,QAAA,GAAED,IAAI,CAAC9D,WAAW,EAAC,KAAG,EAAC8D,IAAI,CAAC7D,SAAS;UAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPlG,OAAA,CAACtD,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBlF,OAAA,CAACvD,KAAK;UAAC0I,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE4C,SAAS,EAAE,QAAQ;YAAE3C,OAAO,EAAE,WAAW;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAC7ElF,OAAA,CAACxD,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,EACxDD,IAAI,CAAC2E;UAAa;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACblG,OAAA,CAACxD,UAAU;YAACoJ,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAa;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPlG,OAAA,CAACtD,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBlF,OAAA,CAACvD,KAAK;UAAC0I,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE4C,SAAS,EAAE,QAAQ;YAAE3C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChFlF,OAAA,CAACxD,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxDD,IAAI,CAAC4C,iBAAiB,EAAC,GAC1B;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblG,OAAA,CAACxD,UAAU;YAACoJ,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPlG,OAAA,CAACtD,IAAI;QAACkK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBlF,OAAA,CAACvD,KAAK;UAAC0I,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE4C,SAAS,EAAE,QAAQ;YAAE3C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChFlF,OAAA,CAACxD,UAAU;YAACoJ,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxD7B,IAAI,CAACC,KAAK,CAAC2B,IAAI,CAAC0E,oBAAoB,GAAG1E,IAAI,CAAC2E,aAAa,GAAG,CAAC,CAAC,EAAC,GAClE;UAAA;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblG,OAAA,CAACxD,UAAU;YAACoJ,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAe;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNtE,UAAU,iBACT5B,OAAA,CAACzD,GAAG;MAAC4I,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACjBlF,OAAA,CAACF,aAAa;QAACmF,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDlG,OAAA,CAACvD,KAAK;MAAC0I,EAAE,EAAE;QAAEK,CAAC,EAAE;MAAE,CAAE;MAAAN,QAAA,gBAClBlF,OAAA,CAACxD,UAAU;QAACoJ,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEI,EAAE,EAAE,CAAC;UAAEM,UAAU,EAAE;QAAI,CAAE;QAAAX,QAAA,EAAC;MAEzD;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblG,OAAA,CAACP,eAAe;QACdwF,IAAI,EAAEA,IAAI,CAAC4E,gBAAgB,IAAI,EAAG;QAClCV,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,MAAM;UAAEpB,KAAK,EAAE;QAAI,CAAC,EACjD;UAAEmB,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,cAAc;UAAEpB,KAAK,EAAE,GAAG;UAAEqB,KAAK,EAAE,OAAO;UAAEQ,QAAQ,EAAE,QAAQ;UAC1FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACf,KAAK;QAAI,CAAC,CACxC;QACFO,QAAQ,EAAE;MAAG;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAID,MAAM+D,YAAY,GAAGA,CAAA,kBACnBjK,OAAA,CAAC9C,MAAM;IAACyH,IAAI,EAAEjE,UAAW;IAACwJ,OAAO,EAAEnF,iBAAkB;IAACoF,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAlF,QAAA,gBAC3ElF,OAAA,CAAC7C,WAAW;MAAA+H,QAAA,EACTtE,UAAU,KAAK,cAAc,GAAG,yBAAyB,GAAG;IAAe;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eACdlG,OAAA,CAAC5C,aAAa;MAAA8H,QAAA,GACX1E,KAAK,iBACJR,OAAA,CAACjD,KAAK;QAACsN,QAAQ,EAAC,OAAO;QAAClF,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,EACnC1E;MAAK;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDlG,OAAA,CAACtD,IAAI;QAACgK,SAAS;QAACC,OAAO,EAAE,CAAE;QAACxB,EAAE,EAAE;UAAEoD,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,gBACxClF,OAAA,CAACtD,IAAI;UAACkK,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA3B,QAAA,eAChBlF,OAAA,CAAC1C,WAAW;YAAC8M,SAAS;YAAAlF,QAAA,gBACpBlF,OAAA,CAACzC,UAAU;cAAA2H,QAAA,EAAC;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChClG,OAAA,CAACxC,MAAM;cACLyJ,KAAK,EAAEjG,QAAQ,CAACE,OAAQ;cACxBsF,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAKrF,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAEoF,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cAAA/B,QAAA,gBAEvElF,OAAA,CAACvC,QAAQ;gBAACwJ,KAAK,EAAC,OAAO;gBAAA/B,QAAA,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvDlG,OAAA,CAACvC,QAAQ;gBAACwJ,KAAK,EAAC,KAAK;gBAAA/B,QAAA,EAAC;cAAY;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7ClG,OAAA,CAACvC,QAAQ;gBAACwJ,KAAK,EAAC,OAAO;gBAAA/B,QAAA,EAAC;cAAc;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAINtF,UAAU,KAAK,cAAc,iBAC5BZ,OAAA,CAAAE,SAAA;UAAAgF,QAAA,gBACElF,OAAA,CAACtD,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACflF,OAAA,CAACtC,SAAS;cACR0M,SAAS;cACTE,IAAI,EAAC,MAAM;cACX9D,KAAK,EAAC,aAAa;cACnBS,KAAK,EAAEjG,QAAQ,CAACG,WAAY;cAC5BkF,QAAQ,EAAGC,CAAC,IAAKrF,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEmF,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cAC3EsD,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPlG,OAAA,CAACtD,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACflF,OAAA,CAACtC,SAAS;cACR0M,SAAS;cACTE,IAAI,EAAC,MAAM;cACX9D,KAAK,EAAC,WAAW;cACjBS,KAAK,EAAEjG,QAAQ,CAACI,SAAU;cAC1BiF,QAAQ,EAAGC,CAAC,IAAKrF,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAEkF,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cACzEsD,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChBlG,OAAA,CAAC3C,aAAa;MAAA6H,QAAA,gBACZlF,OAAA,CAACnD,MAAM;QAAC4N,OAAO,EAAE1F,iBAAkB;QAAAG,QAAA,EAAC;MAAO;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpDlG,OAAA,CAACnD,MAAM;QACL4N,OAAO,EAAE3F,oBAAqB;QAC9Bc,OAAO,EAAC,WAAW;QACnB8E,QAAQ,EAAEpK,OAAQ;QAClBqK,SAAS,EAAErK,OAAO,gBAAGN,OAAA,CAAChD,gBAAgB;UAACsK,IAAI,EAAE;QAAG;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlG,OAAA,CAACvB,cAAc;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAhB,QAAA,EAExE5E,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACElG,OAAA,CAACzD,GAAG;IAACqO,SAAS,EAAC,sCAAsC;IAAA1F,QAAA,gBAEnDlF,OAAA,CAACzD,GAAG;MAAC4I,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACpFlF,OAAA,CAACT,eAAe;QAAAwG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGL5F,OAAO,iBACNN,OAAA,CAACzD,GAAG;MAAC4I,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEwF,EAAE,EAAE;MAAE,CAAE;MAAA3F,QAAA,eAC5DlF,OAAA,CAAChD,gBAAgB;QAAA+I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDlG,OAAA,CAACzD,GAAG;MAAC4I,EAAE,EAAE;QAAEoD,EAAE,EAAE;MAAE,CAAE;MAAArD,QAAA,gBAEjBlF,OAAA,CAACzD,GAAG;QAAC4I,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACjBlF,OAAA,CAACxD,UAAU;UAACoJ,OAAO,EAAC,IAAI;UAACT,EAAE,EAAE;YAAEU,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE,SAAS;YAAEP,EAAE,EAAE,CAAC;YAAE6C,SAAS,EAAE;UAAS,CAAE;UAAAlD,QAAA,EAAC;QAEhG;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblG,OAAA,CAACtD,IAAI;UAACgK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,gBAEzBlF,OAAA,CAACtD,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7BlF,OAAA,CAACrD,IAAI;cACHiO,SAAS,EAAE,eAAe9J,kBAAkB,KAAK,UAAU,GAAG,sBAAsB,GAAG,EAAE,EAAG;cAC5FqE,EAAE,EAAE;gBACF+C,MAAM,EAAE,OAAO;gBACf4C,MAAM,EAAE,SAAS;gBACjBnF,MAAM,EAAE7E,kBAAkB,KAAK,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;gBACrF2E,OAAO,EAAE3E,kBAAkB,KAAK,UAAU,GAAG,SAAS,GAAG,OAAO;gBAChE2H,UAAU,EAAE;cACd,CAAE;cACFgC,OAAO,EAAEA,CAAA,KAAM1J,qBAAqB,CAAC,UAAU,CAAE;cAAAmE,QAAA,eAEjDlF,OAAA,CAACpD,WAAW;gBAACuI,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE4C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE9C,OAAO,EAAE,MAAM;kBAAE2F,aAAa,EAAE,QAAQ;kBAAE1F,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjIlF,OAAA,CAAC/B,cAAc;kBAACkH,EAAE,EAAE;oBAAEgD,QAAQ,EAAE,EAAE;oBAAErC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjElG,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE4C,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACblG,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEqC,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPlG,OAAA,CAACtD,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7BlF,OAAA,CAACrD,IAAI;cACHwI,EAAE,EAAE;gBACF+C,MAAM,EAAE,OAAO;gBACf4C,MAAM,EAAE,SAAS;gBACjBnF,MAAM,EAAE7E,kBAAkB,KAAK,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;gBAChF2E,OAAO,EAAE3E,kBAAkB,KAAK,KAAK,GAAG,SAAS,GAAG,OAAO;gBAC3D2H,UAAU,EAAE;cACd,CAAE;cACFgC,OAAO,EAAEA,CAAA,KAAM1J,qBAAqB,CAAC,KAAK,CAAE;cAAAmE,QAAA,eAE5ClF,OAAA,CAACpD,WAAW;gBAACuI,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE4C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE9C,OAAO,EAAE,MAAM;kBAAE2F,aAAa,EAAE,QAAQ;kBAAE1F,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjIlF,OAAA,CAAC3B,QAAQ;kBAAC8G,EAAE,EAAE;oBAAEgD,QAAQ,EAAE,EAAE;oBAAErC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3DlG,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE4C,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACblG,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEqC,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAKPlG,OAAA,CAACtD,IAAI;YAACkK,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7BlF,OAAA,CAACrD,IAAI;cACHwI,EAAE,EAAE;gBACF+C,MAAM,EAAE,OAAO;gBACf4C,MAAM,EAAE,SAAS;gBACjBnF,MAAM,EAAE7E,kBAAkB,KAAK,cAAc,GAAG,mBAAmB,GAAG,mBAAmB;gBACzF2E,OAAO,EAAE3E,kBAAkB,KAAK,cAAc,GAAG,SAAS,GAAG,OAAO;gBACpE2H,UAAU,EAAE;cACd,CAAE;cACFgC,OAAO,EAAEA,CAAA,KAAM1J,qBAAqB,CAAC,cAAc,CAAE;cAAAmE,QAAA,eAErDlF,OAAA,CAACpD,WAAW;gBAACuI,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE4C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE9C,OAAO,EAAE,MAAM;kBAAE2F,aAAa,EAAE,QAAQ;kBAAE1F,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjIlF,OAAA,CAAC7B,YAAY;kBAACgH,EAAE,EAAE;oBAAEgD,QAAQ,EAAE,EAAE;oBAAErC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/DlG,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE4C,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACblG,OAAA,CAACxD,UAAU;kBAACoJ,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEqC,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlG,OAAA,CAACzD,GAAG;QAAC4I,EAAE,EAAE;UAAE6F,SAAS,EAAE;QAAQ,CAAE;QAAA9F,QAAA,GAE7BpE,kBAAkB,KAAK,UAAU,iBAChCd,OAAA,CAACvD,KAAK;UAAC0I,EAAE,EAAE;YAAEK,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,EACjB5D,WAAW,CAACE,QAAQ,gBACnBxB,OAAA,CAACzD,GAAG;YAAA2I,QAAA,gBACFlF,OAAA,CAACzD,GAAG;cAAC4I,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9DlF,OAAA,CAACnD,MAAM;gBACL8N,SAAS,eAAE3K,OAAA,CAACzB,YAAY;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuE,OAAO,EAAEA,CAAA,KAAMvG,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;gBAC3D0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlG,OAAA,CAACnD,MAAM;gBACL8N,SAAS,eAAE3K,OAAA,CAACzB,YAAY;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuE,OAAO,EAAEA,CAAA,KAAMvG,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;gBAC7D0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLlB,oBAAoB,CAAC1D,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJ5F,OAAO,gBACTN,OAAA,CAACN,UAAU;YACT4K,IAAI,EAAC,SAAS;YACdnG,UAAU,EAAC,UAAU;YACrB6C,KAAK,EAAC,mCAAmC;YACzCiE,WAAW,EAAC;UAAsD;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,gBAEFlG,OAAA,CAACN,UAAU;YACT4K,IAAI,EAAC,OAAO;YACZnG,UAAU,EAAC,UAAU;YACrB6C,KAAK,EAAC,wBAAwB;YAC9BiE,WAAW,EAAC,mFAAmF;YAC/FC,OAAO,EAAEA,CAAA,KAAM;cACb3K,UAAU,CAAC,IAAI,CAAC;cAChBf,aAAa,CAACyC,iBAAiB,CAAC5B,UAAU,EAAE,OAAO,CAAC,CACjD8K,IAAI,CAAClG,IAAI,IAAI;gBACZ1D,cAAc,CAACiD,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACPhD,QAAQ,EAAEyD,IAAI,CAAC5C;gBACjB,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAAC5B,KAAK,CAAC,iCAAiC,EAAE2B,GAAG,CAAC;cACvD,CAAC,CAAC,CACDiJ,OAAO,CAAC,MAAM;gBACb7K,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGApF,kBAAkB,KAAK,KAAK,iBAC3Bd,OAAA,CAACvD,KAAK;UAAC0I,EAAE,EAAE;YAAEK,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,EACjB5D,WAAW,CAACG,GAAG,gBACdzB,OAAA,CAACzD,GAAG;YAAA2I,QAAA,gBACFlF,OAAA,CAACzD,GAAG;cAAC4I,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9DlF,OAAA,CAACnD,MAAM;gBACL8N,SAAS,eAAE3K,OAAA,CAACzB,YAAY;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuE,OAAO,EAAEA,CAAA,KAAMvG,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtD0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlG,OAAA,CAACnD,MAAM;gBACL8N,SAAS,eAAE3K,OAAA,CAACzB,YAAY;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuE,OAAO,EAAEA,CAAA,KAAMvG,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxD0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLuD,eAAe,CAACnI,WAAW,CAACG,GAAG,CAAC;UAAA;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJ5F,OAAO,gBACTN,OAAA,CAACN,UAAU;YACT4K,IAAI,EAAC,SAAS;YACdnG,UAAU,EAAC,KAAK;YAChB6C,KAAK,EAAC,mCAAmC;YACzCiE,WAAW,EAAC;UAAyC;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,gBAEFlG,OAAA,CAACN,UAAU;YACT4K,IAAI,EAAC,OAAO;YACZnG,UAAU,EAAC,KAAK;YAChB6C,KAAK,EAAC,wBAAwB;YAC9BiE,WAAW,EAAC,gFAAgF;YAC5FC,OAAO,EAAEA,CAAA,KAAM;cACb3K,UAAU,CAAC,IAAI,CAAC;cAChBf,aAAa,CAAC+C,mBAAmB,CAAClC,UAAU,EAAE,OAAO,CAAC,CACnD8K,IAAI,CAAClG,IAAI,IAAI;gBACZ1D,cAAc,CAACiD,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACP/C,GAAG,EAAEwD,IAAI,CAAC5C;gBACZ,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAAC5B,KAAK,CAAC,4BAA4B,EAAE2B,GAAG,CAAC;cAClD,CAAC,CAAC,CACDiJ,OAAO,CAAC,MAAM;gBACb7K,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAKApF,kBAAkB,KAAK,cAAc,iBACpCd,OAAA,CAACvD,KAAK;UAAC0I,EAAE,EAAE;YAAEK,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,EACjB5D,WAAW,CAACK,WAAW,gBACtB3B,OAAA,CAACzD,GAAG;YAAA2I,QAAA,gBACFlF,OAAA,CAACzD,GAAG;cAAC4I,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9DlF,OAAA,CAACnD,MAAM;gBACL8N,SAAS,eAAE3K,OAAA,CAACzB,YAAY;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuE,OAAO,EAAEA,CAAA,KAAMvG,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/D0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlG,OAAA,CAACnD,MAAM;gBACL8N,SAAS,eAAE3K,OAAA,CAACzB,YAAY;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuE,OAAO,EAAEA,CAAA,KAAMvG,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjE0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLwD,uBAAuB,CAACpI,WAAW,CAACK,WAAW,CAAC;UAAA;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,gBAENlG,OAAA,CAACN,UAAU;YACT4K,IAAI,EAAC,iBAAiB;YACtBnG,UAAU,EAAC,cAAc;YACzB6C,KAAK,EAAC,sBAAsB;YAC5BiE,WAAW,EAAC,8GAA2G;YACvHI,WAAW,EAAC,mBAAmB;YAC/BC,QAAQ,EAAEA,CAAA,KAAM;cACdzK,aAAa,CAAC,cAAc,CAAC;cAC7B;cACA,MAAM0K,KAAK,GAAG,IAAI/H,IAAI,CAAC,CAAC;cACxB,MAAMgI,SAAS,GAAG,IAAIhI,IAAI,CAAC,CAAC;cAC5BgI,SAAS,CAACC,QAAQ,CAACF,KAAK,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;cAExCzK,WAAW,CAAC;gBACV,GAAGD,QAAQ;gBACXG,WAAW,EAAEqK,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClDxK,SAAS,EAAEmK,KAAK,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC;cACFjL,aAAa,CAAC,IAAI,CAAC;YACrB;UAAE;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL+D,YAAY,CAAC,CAAC;EAAA;IAAAlE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC9F,EAAA,CAt+BID,iBAAiB;EAAA,QACEb,SAAS;AAAA;AAAAuM,EAAA,GAD5B1L,iBAAiB;AAw+BvB,eAAeA,iBAAiB;AAAC,IAAA0L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}