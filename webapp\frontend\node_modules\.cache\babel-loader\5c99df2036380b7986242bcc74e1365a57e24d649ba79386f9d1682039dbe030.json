{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\";\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box, CssBaseline } from '@mui/material';\nimport TopNavbar from '../components/TopNavbar';\nimport HomePage from './HomePage';\nimport AdminPage from './AdminPage';\nimport UserPage from './UserPage';\nimport CaviPage from './CaviPage';\nimport UserExpirationChecker from '../components/admin/UserExpirationChecker';\n\n// Importa le nuove pagine per i cavi\nimport VisualizzaCaviPage from './cavi/VisualizzaCaviPage';\nimport ParcoCaviPage from './cavi/ParcoCaviPage';\nimport ReportCaviPageNew from './cavi/ReportCaviPageNew';\nimport CertificazioneCaviPage from './cavi/CertificazioneCaviPage';\nimport CertificazioneCEI64_8Page from './cavi/CertificazioneCEI64_8Page';\nimport CertificazioniPageDebug from './CertificazioniPageDebug';\nimport GestioneComandeePage from './cavi/GestioneComandeePage';\nimport TestCaviPage from './cavi/TestCaviPage';\nimport TestBobinePage from './TestBobinePage';\n\n// Importa le pagine per Parco Cavi\nimport VisualizzaBobinePage from './cavi/parco/VisualizzaBobinePage';\nimport CreaBobinaPage from './cavi/parco/CreaBobinaPage';\nimport ParcoCaviModificaBobinaPage from './cavi/parco/ModificaBobinaPage';\nimport EliminaBobinaPage from './cavi/parco/EliminaBobinaPage';\n\n// Importa le pagine per Posa e Collegamenti - OBSOLETE: Funzionalità migrate ai popup\n// import InserisciMetriPage from './cavi/posa/InserisciMetriPage';\n// import MetriPosatiSemplificatoPage from './cavi/posa/MetriPosatiSemplificatoPage';\n// import PosaCaviModificaBobinaPage from './cavi/posa/ModificaBobinaPage';\n\n// Importa la pagina per il cantiere specifico\nimport CantierePage from './cantieri/CantierePage';\n\n// Importa le pagine per le comande\nimport ComandePage from './comande/ComandePage';\nimport TestComande from '../components/comande/TestComande';\nimport AccessoRapidoComanda from '../components/comande/AccessoRapidoComanda';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(UserExpirationChecker, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TopNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3,\n        width: '100%',\n        backgroundColor: '#f5f5f5',\n        // Sfondo grigio chiaro per l'area principale\n        minHeight: 'calc(100vh - 40px)',\n        // Altezza minima per coprire l'intera viewport meno l'altezza della navbar\n        overflowX: 'hidden',\n        // Previene scrollbar orizzontale\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri\",\n          element: /*#__PURE__*/_jsxDEV(UserPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId\",\n          element: /*#__PURE__*/_jsxDEV(CantierePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId/certificazioni\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioniPageDebug, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 73\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi\",\n          element: /*#__PURE__*/_jsxDEV(CaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/visualizza\",\n          element: /*#__PURE__*/_jsxDEV(VisualizzaCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard/cavi/visualizza\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco\",\n          element: /*#__PURE__*/_jsxDEV(ParcoCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/excel\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard/cavi/visualizza\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/report\",\n          element: /*#__PURE__*/_jsxDEV(ReportCaviPageNew, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/:cantiereId/report\",\n          element: /*#__PURE__*/_jsxDEV(ReportCaviPageNew, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 61\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/visualizza\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 68\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/filtra\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/crea\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 62\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/modifica\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 66\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/dettagli\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 66\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/pdf\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 61\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/elimina\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 65\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/strumenti\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 67\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione-cei/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8Page, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 71\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione-cei/rapporti\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8Page, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 70\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione-cei/prove\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8Page, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 67\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione-cei/non-conformita\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8Page, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 76\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione-cei/strumenti\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8Page, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 71\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/comande\",\n          element: /*#__PURE__*/_jsxDEV(GestioneComandeePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId/comande\",\n          element: /*#__PURE__*/_jsxDEV(ComandePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 66\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/test/comande\",\n          element: /*#__PURE__*/_jsxDEV(TestComande, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/accesso-rapido-comanda\",\n          element: /*#__PURE__*/_jsxDEV(AccessoRapidoComanda, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 60\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/test\",\n          element: /*#__PURE__*/_jsxDEV(TestCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/test-bobine/:cantiereId\",\n          element: /*#__PURE__*/_jsxDEV(TestBobinePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 66\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/visualizza\",\n          element: /*#__PURE__*/_jsxDEV(VisualizzaBobinePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/crea\",\n          element: /*#__PURE__*/_jsxDEV(CreaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/modifica\",\n          element: /*#__PURE__*/_jsxDEV(ParcoCaviModificaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/elimina\",\n          element: /*#__PURE__*/_jsxDEV(EliminaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 56\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/storico\",\n          element: /*#__PURE__*/_jsxDEV(StoricoUtilizzoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 56\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "Box", "CssBaseline", "TopNavbar", "HomePage", "AdminPage", "UserPage", "CaviPage", "UserExpirationChecker", "VisualizzaCaviPage", "ParcoCaviPage", "ReportCaviPageNew", "CertificazioneCaviPage", "CertificazioneCEI64_8Page", "CertificazioniPageDebug", "GestioneComandeePage", "TestCaviPage", "TestBobinePage", "VisualizzaBobinePage", "CreaBobinaPage", "ParcoCaviModificaBobinaPage", "EliminaBobinaPage", "CantierePage", "ComandePage", "TestComande", "AccessoRapidoComanda", "jsxDEV", "_jsxDEV", "Dashboard", "sx", "display", "flexDirection", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "flexGrow", "p", "width", "backgroundColor", "minHeight", "overflowX", "path", "element", "to", "replace", "StoricoUtilizzoPage", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box, CssBaseline } from '@mui/material';\n\n\nimport TopNavbar from '../components/TopNavbar';\nimport HomePage from './HomePage';\nimport AdminPage from './AdminPage';\nimport UserPage from './UserPage';\nimport CaviPage from './CaviPage';\nimport UserExpirationChecker from '../components/admin/UserExpirationChecker';\n\n// Importa le nuove pagine per i cavi\nimport VisualizzaCaviPage from './cavi/VisualizzaCaviPage';\nimport ParcoCaviPage from './cavi/ParcoCaviPage';\n\nimport ReportCaviPageNew from './cavi/ReportCaviPageNew';\nimport CertificazioneCaviPage from './cavi/CertificazioneCaviPage';\nimport CertificazioneCEI64_8Page from './cavi/CertificazioneCEI64_8Page';\nimport CertificazioniPageDebug from './CertificazioniPageDebug';\nimport GestioneComandeePage from './cavi/GestioneComandeePage';\nimport TestCaviPage from './cavi/TestCaviPage';\nimport TestBobinePage from './TestBobinePage';\n\n// Importa le pagine per Parco Cavi\nimport VisualizzaBobinePage from './cavi/parco/VisualizzaBobinePage';\nimport CreaBobinaPage from './cavi/parco/CreaBobinaPage';\nimport ParcoCaviModificaBobinaPage from './cavi/parco/ModificaBobinaPage';\nimport EliminaBobinaPage from './cavi/parco/EliminaBobinaPage';\n\n\n// Importa le pagine per Posa e Collegamenti - OBSOLETE: Funzionalità migrate ai popup\n// import InserisciMetriPage from './cavi/posa/InserisciMetriPage';\n// import MetriPosatiSemplificatoPage from './cavi/posa/MetriPosatiSemplificatoPage';\n// import PosaCaviModificaBobinaPage from './cavi/posa/ModificaBobinaPage';\n\n\n// Importa la pagina per il cantiere specifico\nimport CantierePage from './cantieri/CantierePage';\n\n// Importa le pagine per le comande\nimport ComandePage from './comande/ComandePage';\nimport TestComande from '../components/comande/TestComande';\nimport AccessoRapidoComanda from '../components/comande/AccessoRapidoComanda';\n\nconst Dashboard = () => {\n\n  return (\n    <Box sx={{ display: 'flex', flexDirection: 'column' }}>\n      {/* Componente invisibile che verifica gli utenti scaduti */}\n      <UserExpirationChecker />\n      <CssBaseline />\n      <TopNavbar />\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: '100%',\n          backgroundColor: '#f5f5f5', // Sfondo grigio chiaro per l'area principale\n          minHeight: 'calc(100vh - 40px)', // Altezza minima per coprire l'intera viewport meno l'altezza della navbar\n          overflowX: 'hidden', // Previene scrollbar orizzontale\n          display: 'flex',\n          flexDirection: 'column'\n        }}\n      >\n        <Routes>\n            <Route path=\"/\" element={<HomePage />} />\n            <Route path=\"/admin\" element={<AdminPage />} />\n            <Route path=\"/cantieri\" element={<UserPage />} />\n            <Route path=\"/cantieri/:cantiereId\" element={<CantierePage />} />\n            <Route path=\"/cantieri/:cantiereId/certificazioni\" element={<CertificazioniPageDebug />} />\n\n            {/* Route per la gestione cavi */}\n            <Route path=\"/cavi\" element={<CaviPage />} />\n            <Route path=\"/cavi/visualizza\" element={<VisualizzaCaviPage />} />\n            <Route path=\"/cavi/posa\" element={<Navigate to=\"/dashboard/cavi/visualizza\" replace />} />\n            <Route path=\"/cavi/parco\" element={<ParcoCaviPage />} />\n            <Route path=\"/cavi/excel\" element={<Navigate to=\"/dashboard/cavi/visualizza\" replace />} />\n            <Route path=\"/cavi/report\" element={<ReportCaviPageNew />} />\n            <Route path=\"/cavi/:cantiereId/report\" element={<ReportCaviPageNew />} />\n\n            <Route path=\"/cavi/certificazione\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/visualizza\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/filtra\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/crea\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/modifica\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/dettagli\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/pdf\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/elimina\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/strumenti\" element={<CertificazioneCaviPage />} />\n\n            {/* Route per Certificazione CEI 64-8 */}\n            <Route path=\"/cavi/certificazione-cei/dashboard\" element={<CertificazioneCEI64_8Page />} />\n            <Route path=\"/cavi/certificazione-cei/rapporti\" element={<CertificazioneCEI64_8Page />} />\n            <Route path=\"/cavi/certificazione-cei/prove\" element={<CertificazioneCEI64_8Page />} />\n            <Route path=\"/cavi/certificazione-cei/non-conformita\" element={<CertificazioneCEI64_8Page />} />\n            <Route path=\"/cavi/certificazione-cei/strumenti\" element={<CertificazioneCEI64_8Page />} />\n\n            <Route path=\"/cavi/comande\" element={<GestioneComandeePage />} />\n            <Route path=\"/cantieri/:cantiereId/comande\" element={<ComandePage />} />\n            <Route path=\"/test/comande\" element={<TestComande />} />\n            <Route path=\"/accesso-rapido-comanda\" element={<AccessoRapidoComanda />} />\n            <Route path=\"/cavi/test\" element={<TestCaviPage />} />\n\n            {/* Route per la pagina di test delle bobine */}\n            <Route path=\"/cavi/test-bobine/:cantiereId\" element={<TestBobinePage />} />\n\n            {/* Route per Parco Cavi */}\n            <Route path=\"/cavi/parco/visualizza\" element={<VisualizzaBobinePage />} />\n            <Route path=\"/cavi/parco/crea\" element={<CreaBobinaPage />} />\n            <Route path=\"/cavi/parco/modifica\" element={<ParcoCaviModificaBobinaPage />} />\n            <Route path=\"/cavi/parco/elimina\" element={<EliminaBobinaPage />} />\n            <Route path=\"/cavi/parco/storico\" element={<StoricoUtilizzoPage />} />\n\n            {/* Route per Posa e Collegamenti - OBSOLETE: Funzionalità migrate ai popup */}\n            {/* <Route path=\"/cavi/posa/inserisci-metri\" element={<InserisciMetriPage />} /> */}\n            {/* <Route path=\"/cavi/posa/metri-posati-semplificato\" element={<MetriPosatiSemplificatoPage />} /> */}\n            {/* <Route path=\"/cavi/posa/modifica-bobina\" element={<PosaCaviModificaBobinaPage />} /> */}\n            {/* <Route path=\"/cantieri/:cantiereId/cavi/posa/modifica-bobina/:cavoId?\" element={<PosaCaviModificaBobinaPage />} /> */}\n\n            {/* Altre route verranno aggiunte man mano che vengono implementate */}\n          </Routes>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,GAAG,EAAEC,WAAW,QAAQ,eAAe;AAGhD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,qBAAqB,MAAM,2CAA2C;;AAE7E;AACA,OAAOC,kBAAkB,MAAM,2BAA2B;AAC1D,OAAOC,aAAa,MAAM,sBAAsB;AAEhD,OAAOC,iBAAiB,MAAM,0BAA0B;AACxD,OAAOC,sBAAsB,MAAM,+BAA+B;AAClE,OAAOC,yBAAyB,MAAM,kCAAkC;AACxE,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,oBAAoB,MAAM,6BAA6B;AAC9D,OAAOC,YAAY,MAAM,qBAAqB;AAC9C,OAAOC,cAAc,MAAM,kBAAkB;;AAE7C;AACA,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,2BAA2B,MAAM,iCAAiC;AACzE,OAAOC,iBAAiB,MAAM,gCAAgC;;AAG9D;AACA;AACA;AACA;;AAGA;AACA,OAAOC,YAAY,MAAM,yBAAyB;;AAElD;AACA,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,WAAW,MAAM,mCAAmC;AAC3D,OAAOC,oBAAoB,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAEtB,oBACED,OAAA,CAAC1B,GAAG;IAAC4B,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpDL,OAAA,CAACnB,qBAAqB;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzBT,OAAA,CAACzB,WAAW;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfT,OAAA,CAACxB,SAAS;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACbT,OAAA,CAAC1B,GAAG;MACFoC,SAAS,EAAC,MAAM;MAChBR,EAAE,EAAE;QACFS,QAAQ,EAAE,CAAC;QACXC,CAAC,EAAE,CAAC;QACJC,KAAK,EAAE,MAAM;QACbC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,oBAAoB;QAAE;QACjCC,SAAS,EAAE,QAAQ;QAAE;QACrBb,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE;MACjB,CAAE;MAAAC,QAAA,eAEFL,OAAA,CAAC7B,MAAM;QAAAkC,QAAA,gBACHL,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,GAAG;UAACC,OAAO,eAAElB,OAAA,CAACvB,QAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAElB,OAAA,CAACtB,SAAS;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,WAAW;UAACC,OAAO,eAAElB,OAAA,CAACrB,QAAQ;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,uBAAuB;UAACC,OAAO,eAAElB,OAAA,CAACL,YAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,sCAAsC;UAACC,OAAO,eAAElB,OAAA,CAACb,uBAAuB;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG3FT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,OAAO;UAACC,OAAO,eAAElB,OAAA,CAACpB,QAAQ;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,kBAAkB;UAACC,OAAO,eAAElB,OAAA,CAAClB,kBAAkB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,YAAY;UAACC,OAAO,eAAElB,OAAA,CAAC3B,QAAQ;YAAC8C,EAAE,EAAC,4BAA4B;YAACC,OAAO;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1FT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,aAAa;UAACC,OAAO,eAAElB,OAAA,CAACjB,aAAa;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,aAAa;UAACC,OAAO,eAAElB,OAAA,CAAC3B,QAAQ;YAAC8C,EAAE,EAAC,4BAA4B;YAACC,OAAO;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3FT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,cAAc;UAACC,OAAO,eAAElB,OAAA,CAAChB,iBAAiB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAElB,OAAA,CAAChB,iBAAiB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEzET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAElB,OAAA,CAACf,sBAAsB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1ET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,iCAAiC;UAACC,OAAO,eAAElB,OAAA,CAACf,sBAAsB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrFT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,6BAA6B;UAACC,OAAO,eAAElB,OAAA,CAACf,sBAAsB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjFT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,2BAA2B;UAACC,OAAO,eAAElB,OAAA,CAACf,sBAAsB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/ET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,+BAA+B;UAACC,OAAO,eAAElB,OAAA,CAACf,sBAAsB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,+BAA+B;UAACC,OAAO,eAAElB,OAAA,CAACf,sBAAsB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAElB,OAAA,CAACf,sBAAsB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9ET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,8BAA8B;UAACC,OAAO,eAAElB,OAAA,CAACf,sBAAsB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClFT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,gCAAgC;UAACC,OAAO,eAAElB,OAAA,CAACf,sBAAsB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGpFT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,oCAAoC;UAACC,OAAO,eAAElB,OAAA,CAACd,yBAAyB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3FT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,mCAAmC;UAACC,OAAO,eAAElB,OAAA,CAACd,yBAAyB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1FT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,gCAAgC;UAACC,OAAO,eAAElB,OAAA,CAACd,yBAAyB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvFT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,yCAAyC;UAACC,OAAO,eAAElB,OAAA,CAACd,yBAAyB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChGT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,oCAAoC;UAACC,OAAO,eAAElB,OAAA,CAACd,yBAAyB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE3FT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,eAAe;UAACC,OAAO,eAAElB,OAAA,CAACZ,oBAAoB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,+BAA+B;UAACC,OAAO,eAAElB,OAAA,CAACJ,WAAW;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,eAAe;UAACC,OAAO,eAAElB,OAAA,CAACH,WAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,yBAAyB;UAACC,OAAO,eAAElB,OAAA,CAACF,oBAAoB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3ET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,YAAY;UAACC,OAAO,eAAElB,OAAA,CAACX,YAAY;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtDT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,+BAA+B;UAACC,OAAO,eAAElB,OAAA,CAACV,cAAc;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG3ET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,wBAAwB;UAACC,OAAO,eAAElB,OAAA,CAACT,oBAAoB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1ET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,kBAAkB;UAACC,OAAO,eAAElB,OAAA,CAACR,cAAc;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DT,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAElB,OAAA,CAACP,2BAA2B;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/ET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,qBAAqB;UAACC,OAAO,eAAElB,OAAA,CAACN,iBAAiB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpET,OAAA,CAAC5B,KAAK;UAAC6C,IAAI,EAAC,qBAAqB;UAACC,OAAO,eAAElB,OAAA,CAACqB,mBAAmB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAShE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,EAAA,GAjFIrB,SAAS;AAmFf,eAAeA,SAAS;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}