{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport { Box, Typography, Paper, Grid, Card, CardContent, Button, Chip, Alert, CircularProgress, Divider, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useParams } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const {\n    cantiereId\n  } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Import certificazione service\n        const certificazioneService = await import('../../services/certificazioneService');\n\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Carica statistiche certificazioni\n        const certificazioniPromise = certificazioneService.default.getCertificazioni(cantiereId).catch(err => {\n          console.error('Error loading certificazioni:', err);\n          return [];\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, certificazioniData] = await Promise.all([progressPromise, boqPromise, certificazioniPromise]);\n\n        // Aggiungi statistiche certificazioni ai dati del progress report\n        if (progressData.content && certificazioniData) {\n          const totaleCavi = progressData.content.totale_cavi || 0;\n          const caviCertificati = certificazioniData.length;\n          const percentualeCertificazione = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n\n          // Calcola certificazioni di oggi\n          const oggi = new Date().toDateString();\n          const certificazioniOggi = certificazioniData.filter(cert => new Date(cert.data_certificazione).toDateString() === oggi).length;\n          progressData.content.certificazioni = {\n            totale: caviCertificati,\n            percentuale: percentualeCertificazione,\n            oggi: certificazioniOggi,\n            rimanenti: totaleCavi - caviCertificati\n          };\n        }\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 3,\n        bgcolor: '#f8f9fa',\n        borderRadius: 0,\n        borderBottom: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCA Report Avanzamento Lavori\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        sx: {\n          mb: 5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(MetricCard, {\n            title: \"Metri Totali\",\n            value: data.metri_totali,\n            unit: \"m\",\n            subtitle: \"Lunghezza complessiva del progetto\",\n            gradient: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(MetricCard, {\n            title: \"Metri Posati\",\n            value: data.metri_posati,\n            unit: \"m\",\n            subtitle: `${data.percentuale_avanzamento}% completato`,\n            gradient: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n            progress: data.percentuale_avanzamento,\n            trend: data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down',\n            trendValue: `${data.percentuale_avanzamento}%`,\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(MetricCard, {\n            title: \"Metri Rimanenti\",\n            value: data.metri_da_posare,\n            unit: \"m\",\n            subtitle: `${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`,\n            gradient: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(MetricCard, {\n            title: \"Media/Giorno\",\n            value: data.media_giornaliera || 0,\n            unit: \"m\",\n            subtitle: data.giorni_stimati ? `${data.giorni_stimati} giorni lavorativi rimasti` : data.media_giornaliera > 0 ? 'Calcolo in corso' : 'Nessuna posa recente',\n            gradient: \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n            size: \"large\",\n            tooltip: data.giorni_lavorativi_effettivi ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.` : 'Media giornaliera basata sui giorni di lavoro effettivo'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n        className: \"report-progress-container\",\n        sx: {\n          mb: 4,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n          data: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        sx: {\n          mb: 5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              height: '100%',\n              border: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                  sx: {\n                    color: '#3498db',\n                    mr: 1,\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    color: '#2c3e50'\n                  },\n                  children: \"Stato Cavi\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 2,\n                      bgcolor: '#f8f9fa',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      sx: {\n                        fontWeight: 700,\n                        color: '#2c3e50',\n                        mb: 1\n                      },\n                      children: data.totale_cavi\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Cavi Totali\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 2,\n                      bgcolor: '#e8f5e8',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      sx: {\n                        fontWeight: 700,\n                        color: '#27ae60',\n                        mb: 1\n                      },\n                      children: data.cavi_posati\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: [\"Cavi Posati (\", data.percentuale_cavi, \"%)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    mb: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"Progresso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 600\n                    },\n                    children: [data.percentuale_cavi, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100%',\n                    height: 8,\n                    bgcolor: '#e0e0e0',\n                    borderRadius: 4,\n                    overflow: 'hidden'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: `${data.percentuale_cavi}%`,\n                      height: '100%',\n                      bgcolor: '#27ae60',\n                      transition: 'width 0.3s ease'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              height: '100%',\n              border: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  mb: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    bgcolor: '#9c27b0',\n                    borderRadius: '50%',\n                    p: 1,\n                    mr: 2,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: 'white',\n                      fontWeight: 'bold'\n                    },\n                    children: \"\\uD83D\\uDD12\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    color: '#2c3e50'\n                  },\n                  children: \"Stato Certificazioni Cavi\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 15\n              }, this), data.certificazioni ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  sx: {\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        textAlign: 'center',\n                        p: 2,\n                        bgcolor: '#e8f5e8',\n                        borderRadius: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h4\",\n                        sx: {\n                          fontWeight: 700,\n                          color: '#27ae60',\n                          mb: 1\n                        },\n                        children: data.certificazioni.totale\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 434,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          color: '#666'\n                        },\n                        children: \"Certificati\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 437,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        textAlign: 'center',\n                        p: 2,\n                        bgcolor: '#fff3cd',\n                        borderRadius: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h4\",\n                        sx: {\n                          fontWeight: 700,\n                          color: '#856404',\n                          mb: 1\n                        },\n                        children: data.certificazioni.rimanenti\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 445,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          color: '#666'\n                        },\n                        children: \"Da Certificare\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 448,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#9c27b0',\n                      mb: 1\n                    },\n                    children: [data.certificazioni.percentuale, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666',\n                      mb: 1\n                    },\n                    children: \"Completamento Certificazioni\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#999',\n                      fontSize: '0.75rem'\n                    },\n                    children: [data.certificazioni.oggi, \" certificazioni completate oggi\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100%',\n                    height: 8,\n                    bgcolor: '#e0e0e0',\n                    borderRadius: 4,\n                    overflow: 'hidden'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: `${data.certificazioni.percentuale}%`,\n                      height: '100%',\n                      bgcolor: '#9c27b0',\n                      transition: 'width 0.3s ease'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  p: 2,\n                  bgcolor: '#f8f9fa',\n                  borderRadius: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666'\n                  },\n                  children: \"Nessuna certificazione disponibile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 7\n      }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          border: '1px solid #e0e0e0'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(DateRangeIcon, {\n              sx: {\n                color: '#9b59b6',\n                mr: 1,\n                fontSize: 28\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: \"\\uD83D\\uDCC8 Attivit\\xE0 Recente\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: data.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 2,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 2,\n                  bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                  transition: 'all 0.2s',\n                  '&:hover': {\n                    bgcolor: '#f5f5f5',\n                    transform: 'translateY(-2px)',\n                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 1\n                  },\n                  children: posa.data\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  sx: {\n                    fontWeight: 700,\n                    color: '#2c3e50'\n                  },\n                  children: [posa.metri, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this), index === 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Pi\\xF9 recente\",\n                  size: \"small\",\n                  sx: {\n                    mt: 1,\n                    bgcolor: '#3498db',\n                    color: 'white',\n                    fontSize: '0.7rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this), data.posa_recente.length > 5 && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Accordion, {\n              children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n                expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 49\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#3498db'\n                  },\n                  children: [\"Mostra tutti i \", data.posa_recente.length, \" record\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n                children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n                  data: data.posa_recente.map(posa => ({\n                    data: posa.data,\n                    metri: `${posa.metri}m`\n                  })),\n                  columns: [{\n                    field: 'data',\n                    headerName: 'Data',\n                    width: 200\n                  }, {\n                    field: 'metri',\n                    headerName: 'Metri Posati',\n                    width: 150,\n                    align: 'right'\n                  }],\n                  pagination: true,\n                  pageSize: 10\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n        sx: {\n          color: '#8e44ad',\n          mr: 1,\n          fontSize: 28\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCB Bill of Quantities - Distinta Materiali\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 5,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(BoqChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 581,\n    columnNumber: 5\n  }, this);\n  const renderPosaPeriodoReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'warning.main'\n        },\n        children: \"Report Posa per Periodo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      sx: {\n        mb: 5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'warning.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.totale_metri_periodo, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [data.data_inizio, \" - \", data.data_fine]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'info.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: data.giorni_attivi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Giorni Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'success.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.media_giornaliera, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Giorno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'primary.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [Math.round(data.totale_metri_periodo / data.giorni_attivi * 7), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Settimana\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 664,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 5,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Dettaglio Posa Giornaliera\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.posa_giornaliera || [],\n        columns: [{\n          field: 'data',\n          headerName: 'Data',\n          width: 200\n        }, {\n          field: 'metri',\n          headerName: 'Metri Posati',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri}m`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 686,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 614,\n    columnNumber: 5\n  }, this);\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: dialogType === 'posa-periodo' ? 'Report Posa per Periodo' : 'Genera Report'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 703,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 706,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 702,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"report-main-container report-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 775,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 774,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 781,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 780,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            color: '#2c3e50',\n            mb: 2,\n            textAlign: 'center'\n          },\n          children: \"\\uD83C\\uDFAF Seleziona il tipo di report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 789,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: `report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`,\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('progress'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#3498db',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Avanzamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Panoramica lavori\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 794,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('boq'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#8e44ad',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Bill of Quantities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Distinta materiali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('posa-periodo'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#9b59b6',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 857,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Analisi temporale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: '400px',\n          width: '100%'\n        },\n        children: [selectedReportType === 'progress' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 0,\n            width: '100%'\n          },\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2,\n                p: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 879,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 19\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"progress\",\n            title: \"Caricamento Report Avanzamento...\",\n            description: \"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"progress\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getProgressReport(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  progress: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying progress report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 908,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 874,\n          columnNumber: 13\n        }, this), selectedReportType === 'boq' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 0,\n            width: '100%'\n          },\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2,\n                p: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 942,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 941,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 952,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 940,\n              columnNumber: 19\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"boq\",\n            title: \"Caricamento Bill of Quantities...\",\n            description: \"Stiamo elaborando la distinta materiali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"boq\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  boq: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying BOQ report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 971,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 937,\n          columnNumber: 13\n        }, this), selectedReportType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.posaPeriodo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1017,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1016,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 19\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"action-required\",\n            reportType: \"posa-periodo\",\n            title: \"Seleziona un Periodo\",\n            description: \"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttivit\\xE0 del team.\",\n            actionLabel: \"Seleziona Periodo\",\n            onAction: () => {\n              setDialogType('posa-periodo');\n              // Set default date range (last month to today)\n              const today = new Date();\n              const lastMonth = new Date();\n              lastMonth.setMonth(today.getMonth() - 1);\n              setFormData({\n                ...formData,\n                data_inizio: lastMonth.toISOString().split('T')[0],\n                data_fine: today.toISOString().split('T')[0]\n              });\n              setOpenDialog(true);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 871,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 786,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 772,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"OtD3yym0uagQsYqqzydzEKYfeDo=\", false, function () {\n  return [useParams];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useParams", "AdminHomeButton", "reportService", "FilterableTable", "EmptyState", "MetricCard", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "cantiereId", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedReportType", "setSelectedReportType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobinaSpecifica", "posaPeriodo", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllReports", "certificazioneService", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "certificazioniPromise", "default", "getCertificazioni", "progressData", "boqData", "certificazioniData", "Promise", "all", "totaleCavi", "totale_cavi", "caviCertificati", "length", "percentualeCertificazione", "Math", "round", "oggi", "Date", "toDateString", "certificazioniOggi", "filter", "cert", "data_certificazione", "certificazioni", "totale", "percentuale", "<PERSON><PERSON><PERSON>", "generateReportWithFormat", "reportType", "format", "response", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleGenerateReport", "handleCloseDialog", "renderProgressReport", "data", "sx", "width", "children", "display", "justifyContent", "alignItems", "mb", "p", "bgcolor", "borderRadius", "borderBottom", "variant", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "control", "checked", "onChange", "e", "target", "label", "mr", "container", "spacing", "item", "xs", "sm", "md", "title", "value", "metri_totali", "unit", "subtitle", "gradient", "size", "metri_posati", "percentuale_avanzamento", "trend", "trendValue", "metri_da_posare", "toFixed", "media_giornaliera", "giorni_stimati", "tooltip", "giorni_lavorativi_effettivi", "className", "height", "border", "fontSize", "textAlign", "cavi_posati", "percentuale_cavi", "mt", "overflow", "transition", "posa_recente", "slice", "map", "posa", "index", "transform", "boxShadow", "metri", "expandIcon", "columns", "field", "headerName", "align", "pagination", "pageSize", "renderBoqReport", "renderPosaPeriodoReport", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "dataType", "renderCell", "row", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "type", "InputLabelProps", "shrink", "onClick", "disabled", "startIcon", "my", "cursor", "flexDirection", "minHeight", "description", "onRetry", "then", "finally", "actionLabel", "onAction", "today", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useParams } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\n\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\n\nconst ReportCaviPageNew = () => {\n  const { cantiereId } = useParams();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Import certificazione service\n        const certificazioneService = await import('../../services/certificazioneService');\n\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        // Carica statistiche certificazioni\n        const certificazioniPromise = certificazioneService.default.getCertificazioni(cantiereId)\n          .catch(err => {\n            console.error('Error loading certificazioni:', err);\n            return [];\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, certificazioniData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          certificazioniPromise\n        ]);\n\n        // Aggiungi statistiche certificazioni ai dati del progress report\n        if (progressData.content && certificazioniData) {\n          const totaleCavi = progressData.content.totale_cavi || 0;\n          const caviCertificati = certificazioniData.length;\n          const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n          // Calcola certificazioni di oggi\n          const oggi = new Date().toDateString();\n          const certificazioniOggi = certificazioniData.filter(cert =>\n            new Date(cert.data_certificazione).toDateString() === oggi\n          ).length;\n\n          progressData.content.certificazioni = {\n            totale: caviCertificati,\n            percentuale: percentualeCertificazione,\n            oggi: certificazioniOggi,\n            rimanenti: totaleCavi - caviCertificati\n          };\n        }\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n\n\n  const renderProgressReport = (data) => (\n    <Box sx={{ width: '100%' }}>\n      {/* Header con controlli migliorato */}\n      <Box sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 3,\n        bgcolor: '#f8f9fa',\n        borderRadius: 0,\n        borderBottom: '1px solid #e0e0e0'\n      }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📊 Report Avanzamento Lavori\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Metriche Principali - Cards Moderne con MetricCard */}\n      <Box sx={{ p: 3 }}>\n        <Grid container spacing={4} sx={{ mb: 5 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Totali\"\n            value={data.metri_totali}\n            unit=\"m\"\n            subtitle=\"Lunghezza complessiva del progetto\"\n            gradient=\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\"\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Posati\"\n            value={data.metri_posati}\n            unit=\"m\"\n            subtitle={`${data.percentuale_avanzamento}% completato`}\n            gradient=\"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\"\n            progress={data.percentuale_avanzamento}\n            trend={data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down'}\n            trendValue={`${data.percentuale_avanzamento}%`}\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Rimanenti\"\n            value={data.metri_da_posare}\n            unit=\"m\"\n            subtitle={`${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`}\n            gradient=\"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\"\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Media/Giorno\"\n            value={data.media_giornaliera || 0}\n            unit=\"m\"\n            subtitle={\n              data.giorni_stimati\n                ? `${data.giorni_stimati} giorni lavorativi rimasti`\n                : (data.media_giornaliera > 0\n                    ? 'Calcolo in corso'\n                    : 'Nessuna posa recente')\n            }\n            gradient=\"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\"\n            size=\"large\"\n            tooltip={\n              data.giorni_lavorativi_effettivi\n                ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.`\n                : 'Media giornaliera basata sui giorni di lavoro effettivo'\n            }\n          />\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box className=\"report-progress-container\" sx={{ mb: 4, width: '100%' }}>\n          <ProgressChart data={data} />\n        </Box>\n      )}\n\n      {/* Dettagli Performance - Cards Informative */}\n      <Grid container spacing={4} sx={{ mb: 5 }}>\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <CableIcon sx={{ color: '#3498db', mr: 1, fontSize: 28 }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Cavi\n                </Typography>\n              </Box>\n              <Grid container spacing={2}>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                      {data.totale_cavi}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                      {data.cavi_posati}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Posati ({data.percentuale_cavi}%)\n                    </Typography>\n                  </Box>\n                </Grid>\n              </Grid>\n              <Box sx={{ mt: 2 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body2\">Progresso</Typography>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                    {data.percentuale_cavi}%\n                  </Typography>\n                </Box>\n                <Box sx={{\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                }}>\n                  <Box sx={{\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }} />\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                <Box sx={{\n                  bgcolor: '#9c27b0',\n                  borderRadius: '50%',\n                  p: 1,\n                  mr: 2,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  <Typography variant=\"h6\" sx={{ color: 'white', fontWeight: 'bold' }}>\n                    🔒\n                  </Typography>\n                </Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Certificazioni Cavi\n                </Typography>\n              </Box>\n\n              {data.certificazioni ? (\n                <>\n                  <Grid container spacing={2} sx={{ mb: 3 }}>\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                        <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                          {data.certificazioni.totale}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                          Certificati\n                        </Typography>\n                      </Box>\n                    </Grid>\n\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>\n                        <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#856404', mb: 1 }}>\n                          {data.certificazioni.rimanenti}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                          Da Certificare\n                        </Typography>\n                      </Box>\n                    </Grid>\n                  </Grid>\n\n                  <Box sx={{ textAlign: 'center', mb: 2 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#9c27b0', mb: 1 }}>\n                      {data.certificazioni.percentuale}%\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      Completamento Certificazioni\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#999', fontSize: '0.75rem' }}>\n                      {data.certificazioni.oggi} certificazioni completate oggi\n                    </Typography>\n                  </Box>\n\n                  {/* Progress bar certificazioni */}\n                  <Box sx={{\n                    width: '100%',\n                    height: 8,\n                    bgcolor: '#e0e0e0',\n                    borderRadius: 4,\n                    overflow: 'hidden'\n                  }}>\n                    <Box sx={{\n                      width: `${data.certificazioni.percentuale}%`,\n                      height: '100%',\n                      bgcolor: '#9c27b0',\n                      transition: 'width 0.3s ease'\n                    }} />\n                  </Box>\n                </>\n              ) : (\n                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                  <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                    Nessuna certificazione disponibile\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n\n\n      {/* Attività Recente - Design Migliorato */}\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Card sx={{ border: '1px solid #e0e0e0' }}>\n          <CardContent sx={{ p: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <DateRangeIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                📈 Attività Recente\n              </Typography>\n            </Box>\n\n            {/* Mostra solo gli ultimi 5 record in formato card per mobile-friendly */}\n            <Grid container spacing={2}>\n              {data.posa_recente.slice(0, 5).map((posa, index) => (\n                <Grid item xs={12} sm={6} md={4} key={index}>\n                  <Box sx={{\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    borderRadius: 2,\n                    bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                    transition: 'all 0.2s',\n                    '&:hover': {\n                      bgcolor: '#f5f5f5',\n                      transform: 'translateY(-2px)',\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                    }\n                  }}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      {posa.data}\n                    </Typography>\n                    <Typography variant=\"h5\" sx={{ fontWeight: 700, color: '#2c3e50' }}>\n                      {posa.metri}m\n                    </Typography>\n                    {index === 0 && (\n                      <Chip\n                        label=\"Più recente\"\n                        size=\"small\"\n                        sx={{\n                          mt: 1,\n                          bgcolor: '#3498db',\n                          color: 'white',\n                          fontSize: '0.7rem'\n                        }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n\n            {/* Link per vedere tutti i dati se ce ne sono di più */}\n            {data.posa_recente.length > 5 && (\n              <Box sx={{ mt: 3, textAlign: 'center' }}>\n                <Accordion>\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Typography variant=\"body2\" sx={{ color: '#3498db' }}>\n                      Mostra tutti i {data.posa_recente.length} record\n                    </Typography>\n                  </AccordionSummary>\n                  <AccordionDetails>\n                    <FilterableTable\n                      data={data.posa_recente.map(posa => ({\n                        data: posa.data,\n                        metri: `${posa.metri}m`\n                      }))}\n                      columns={[\n                        { field: 'data', headerName: 'Data', width: 200 },\n                        { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                      ]}\n                      pagination={true}\n                      pageSize={10}\n                    />\n                  </AccordionDetails>\n                </Accordion>\n              </Box>\n            )}\n          </CardContent>\n        </Card>\n      )}\n      </Box>\n    </Box>\n  );\n\n  const renderBoqReport = (data) => (\n    <Box>\n      {/* Header migliorato */}\n      <Box sx={{\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      }}>\n        <ListIcon sx={{ color: '#8e44ad', mr: 1, fontSize: 28 }} />\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📋 Bill of Quantities - Distinta Materiali\n        </Typography>\n      </Box>\n\n      {/* Grafici BOQ se disponibili */}\n      {showCharts && (\n        <Box sx={{ mb: 5, width: '100%' }}>\n          <BoqChart data={data} />\n        </Box>\n      )}\n\n\n    </Box>\n  );\n\n\n\n\n\n  const renderPosaPeriodoReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n          Report Posa per Periodo\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Statistiche Periodo - Layout Orizzontale */}\n      <Grid container spacing={4} sx={{ mb: 5 }}>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.totale_metri_periodo}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Totali</Typography>\n            <Typography variant=\"caption\">{data.data_inizio} - {data.data_fine}</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.giorni_attivi}\n            </Typography>\n            <Typography variant=\"body1\">Giorni Attivi</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.media_giornaliera}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Giorno</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Settimana</Typography>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 5, width: '100%' }}>\n          <TimelineChart data={data} />\n        </Box>\n      )}\n\n      {/* Posa Giornaliera */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Dettaglio Posa Giornaliera\n        </Typography>\n        <FilterableTable\n          data={data.posa_giornaliera || []}\n          columns={[\n            { field: 'data', headerName: 'Data', width: 200 },\n            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri}m` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {dialogType === 'posa-periodo' ? 'Report Posa per Periodo' : 'Genera Report'}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box className=\"report-main-container report-fade-in\">\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports Navigation */}\n      <Box sx={{ mt: 3 }}>\n        {/* Report Navigation - Design Compatto */}\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50', mb: 2, textAlign: 'center' }}>\n            🎯 Seleziona il tipo di report\n          </Typography>\n          <Grid container spacing={2}>\n            {/* Report Avanzamento */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                className={`report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`}\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('progress')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <AssessmentIcon sx={{ fontSize: 32, color: '#3498db', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Avanzamento\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Panoramica lavori\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Bill of Quantities */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('boq')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <ListIcon sx={{ fontSize: 32, color: '#8e44ad', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Bill of Quantities\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Distinta materiali\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n\n\n            {/* Posa per Periodo */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('posa-periodo')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <TimelineIcon sx={{ fontSize: 32, color: '#9b59b6', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Posa per Periodo\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Analisi temporale\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Report Content */}\n        <Box sx={{ minHeight: '400px', width: '100%' }}>\n          {/* Progress Report */}\n          {selectedReportType === 'progress' && (\n            <Paper sx={{ p: 0, width: '100%' }}>\n              {reportsData.progress ? (\n                <Box sx={{ width: '100%' }}>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2, p: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderProgressReport(reportsData.progress)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"progress\"\n                  title=\"Caricamento Report Avanzamento...\"\n                  description=\"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"progress\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Bill of Quantities */}\n          {selectedReportType === 'boq' && (\n            <Paper sx={{ p: 0, width: '100%' }}>\n              {reportsData.boq ? (\n                <Box sx={{ width: '100%' }}>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2, p: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBoqReport(reportsData.boq)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"boq\"\n                  title=\"Caricamento Bill of Quantities...\"\n                  description=\"Stiamo elaborando la distinta materiali\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"boq\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n\n\n          {/* Posa per Periodo Report */}\n          {selectedReportType === 'posa-periodo' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.posaPeriodo ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n                </Box>\n              ) : (\n                <EmptyState\n                  type=\"action-required\"\n                  reportType=\"posa-periodo\"\n                  title=\"Seleziona un Periodo\"\n                  description=\"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttività del team.\"\n                  actionLabel=\"Seleziona Periodo\"\n                  onAction={() => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  }}\n                />\n              )}\n            </Paper>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,0BAA0B;AACjC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EAEXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EAEPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAE5BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EAEtBC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,oCAAoC;;AAG3D;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAW,CAAC,GAAGf,SAAS,CAAC,CAAC;EAElC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmE,KAAK,EAAEC,QAAQ,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1E,QAAQ,CAAC,UAAU,CAAC;EACxE,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvC6E,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAAC;IAC7CmF,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwF,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjCvB,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAMwB,qBAAqB,GAAG,MAAM,MAAM,CAAC,sCAAsC,CAAC;;QAElF;QACA,MAAMC,eAAe,GAAGxC,aAAa,CAACyC,iBAAiB,CAAC5B,UAAU,EAAE,OAAO,CAAC,CACzE6B,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC5B,KAAK,CAAC,gCAAgC,EAAE2B,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAG9C,aAAa,CAAC+C,mBAAmB,CAAClC,UAAU,EAAE,OAAO,CAAC,CACtE6B,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC5B,KAAK,CAAC,2BAA2B,EAAE2B,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAMG,qBAAqB,GAAGT,qBAAqB,CAACU,OAAO,CAACC,iBAAiB,CAACrC,UAAU,CAAC,CACtF6B,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC5B,KAAK,CAAC,+BAA+B,EAAE2B,GAAG,CAAC;UACnD,OAAO,EAAE;QACX,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACQ,YAAY,EAAEC,OAAO,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpEf,eAAe,EACfM,UAAU,EACVE,qBAAqB,CACtB,CAAC;;QAEF;QACA,IAAIG,YAAY,CAACN,OAAO,IAAIQ,kBAAkB,EAAE;UAC9C,MAAMG,UAAU,GAAGL,YAAY,CAACN,OAAO,CAACY,WAAW,IAAI,CAAC;UACxD,MAAMC,eAAe,GAAGL,kBAAkB,CAACM,MAAM;UACjD,MAAMC,yBAAyB,GAAGJ,UAAU,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAEJ,eAAe,GAAGF,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;UAEvG;UACA,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;UACtC,MAAMC,kBAAkB,GAAGb,kBAAkB,CAACc,MAAM,CAACC,IAAI,IACvD,IAAIJ,IAAI,CAACI,IAAI,CAACC,mBAAmB,CAAC,CAACJ,YAAY,CAAC,CAAC,KAAKF,IACxD,CAAC,CAACJ,MAAM;UAERR,YAAY,CAACN,OAAO,CAACyB,cAAc,GAAG;YACpCC,MAAM,EAAEb,eAAe;YACvBc,WAAW,EAAEZ,yBAAyB;YACtCG,IAAI,EAAEG,kBAAkB;YACxBO,SAAS,EAAEjB,UAAU,GAAGE;UAC1B,CAAC;QACH;;QAEA;QACA3B,cAAc,CAAC;UACbC,QAAQ,EAAEmB,YAAY,CAACN,OAAO;UAC9BZ,GAAG,EAAEmB,OAAO,CAACP,OAAO;UACpBX,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIgB,YAAY,CAACN,OAAO,IAAIO,OAAO,CAACP,OAAO,EAAE;UAC3C5B,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO0B,GAAG,EAAE;QACZ;QACAC,OAAO,CAAC5B,KAAK,CAAC,mCAAmC,EAAE2B,GAAG,CAAC;QACvD1B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIF,UAAU,EAAE;MACdyB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACzB,UAAU,CAAC,CAAC;;EAIhB;EACA,MAAM6D,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF7D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI4D,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAM7E,aAAa,CAACyC,iBAAiB,CAAC5B,UAAU,EAAE+D,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAM7E,aAAa,CAAC+C,mBAAmB,CAAClC,UAAU,EAAE+D,MAAM,CAAC;UACtE;QAEF,KAAK,cAAc;UACjB,IAAI,CAACpD,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDX,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA4D,QAAQ,GAAG,MAAM7E,aAAa,CAAC8E,uBAAuB,CACpDjE,UAAU,EACVW,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClBgD,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAIG,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIH,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtE5C,cAAc,CAACiD,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACL,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAAChC;UACpF,CAAC,CAAC,CAAC;QACL;MACF,CAAC,MAAM;QACL;QACA,IAAIgC,QAAQ,CAACI,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACN,QAAQ,CAACI,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOtC,GAAG,EAAE;MACZC,OAAO,CAAC5B,KAAK,CAAC,sCAAsC,EAAE2B,GAAG,CAAC;MAC1D1B,QAAQ,CAAC0B,GAAG,CAACyC,MAAM,IAAIzC,GAAG,CAAC0C,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAID,MAAMuE,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMZ,wBAAwB,CAACtD,UAAU,EAAEI,QAAQ,CAACE,OAAO,CAAC;IAC5DP,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMoE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpE,aAAa,CAAC,KAAK,CAAC;IACpBF,QAAQ,CAAC,IAAI,CAAC;IACdQ,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAID,MAAM2D,oBAAoB,GAAIC,IAAI,iBAChCjF,OAAA,CAACzD,GAAG;IAAC2I,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAEzBpF,OAAA,CAACzD,GAAG;MAAC2I,EAAE,EAAE;QACPG,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE;MAChB,CAAE;MAAAR,QAAA,gBACApF,OAAA,CAACxD,UAAU;QAACqJ,OAAO,EAAC,IAAI;QAACX,EAAE,EAAE;UAAEY,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAX,QAAA,EAAC;MAEpE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnG,OAAA,CAACjC,gBAAgB;QACfqI,OAAO,eACLpG,OAAA,CAAClC,MAAM;UACLuI,OAAO,EAAEzE,UAAW;UACpB0E,QAAQ,EAAGC,CAAC,IAAK1E,aAAa,CAAC0E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDN,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDM,KAAK,eACHzG,OAAA,CAACzD,GAAG;UAAC2I,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjDpF,OAAA,CAACX,aAAa;YAAC6F,EAAE,EAAE;cAAEwB,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNnG,OAAA,CAACzD,GAAG;MAAC2I,EAAE,EAAE;QAAEO,CAAC,EAAE;MAAE,CAAE;MAAAL,QAAA,gBAChBpF,OAAA,CAACtD,IAAI;QAACiK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC1B,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAC1CpF,OAAA,CAACtD,IAAI;UAACmK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9BpF,OAAA,CAACL,UAAU;YACTsH,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAEjC,IAAI,CAACkC,YAAa;YACzBC,IAAI,EAAC,GAAG;YACRC,QAAQ,EAAC,oCAAoC;YAC7CC,QAAQ,EAAC,mDAAmD;YAC5DC,IAAI,EAAC;UAAO;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPnG,OAAA,CAACtD,IAAI;UAACmK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9BpF,OAAA,CAACL,UAAU;YACTsH,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAEjC,IAAI,CAACuC,YAAa;YACzBJ,IAAI,EAAC,GAAG;YACRC,QAAQ,EAAE,GAAGpC,IAAI,CAACwC,uBAAuB,cAAe;YACxDH,QAAQ,EAAC,mDAAmD;YAC5D9F,QAAQ,EAAEyD,IAAI,CAACwC,uBAAwB;YACvCC,KAAK,EAAEzC,IAAI,CAACwC,uBAAuB,GAAG,EAAE,GAAG,IAAI,GAAGxC,IAAI,CAACwC,uBAAuB,GAAG,EAAE,GAAG,MAAM,GAAG,MAAO;YACtGE,UAAU,EAAE,GAAG1C,IAAI,CAACwC,uBAAuB,GAAI;YAC/CF,IAAI,EAAC;UAAO;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPnG,OAAA,CAACtD,IAAI;UAACmK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9BpF,OAAA,CAACL,UAAU;YACTsH,KAAK,EAAC,iBAAiB;YACvBC,KAAK,EAAEjC,IAAI,CAAC2C,eAAgB;YAC5BR,IAAI,EAAC,GAAG;YACRC,QAAQ,EAAE,GAAG,CAAC,GAAG,GAAGpC,IAAI,CAACwC,uBAAuB,EAAEI,OAAO,CAAC,CAAC,CAAC,iBAAkB;YAC9EP,QAAQ,EAAC,mDAAmD;YAC5DC,IAAI,EAAC;UAAO;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPnG,OAAA,CAACtD,IAAI;UAACmK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAC9BpF,OAAA,CAACL,UAAU;YACTsH,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAEjC,IAAI,CAAC6C,iBAAiB,IAAI,CAAE;YACnCV,IAAI,EAAC,GAAG;YACRC,QAAQ,EACNpC,IAAI,CAAC8C,cAAc,GACf,GAAG9C,IAAI,CAAC8C,cAAc,4BAA4B,GACjD9C,IAAI,CAAC6C,iBAAiB,GAAG,CAAC,GACvB,kBAAkB,GAClB,sBACT;YACDR,QAAQ,EAAC,mDAAmD;YAC5DC,IAAI,EAAC,OAAO;YACZS,OAAO,EACL/C,IAAI,CAACgD,2BAA2B,GAC5B,gBAAgBhD,IAAI,CAACgD,2BAA2B,oFAAoF,GACpI;UACL;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGNvE,UAAU,iBACT5B,OAAA,CAACzD,GAAG;QAAC2L,SAAS,EAAC,2BAA2B;QAAChD,EAAE,EAAE;UAAEM,EAAE,EAAE,CAAC;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAC,QAAA,eACtEpF,OAAA,CAACJ,aAAa;UAACqF,IAAI,EAAEA;QAAK;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACN,eAGDnG,OAAA,CAACtD,IAAI;QAACiK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC1B,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACxCpF,OAAA,CAACtD,IAAI;UAACmK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACvBpF,OAAA,CAACrD,IAAI;YAACuI,EAAE,EAAE;cAAEiD,MAAM,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAoB,CAAE;YAAAhD,QAAA,eACxDpF,OAAA,CAACpD,WAAW;cAACsI,EAAE,EAAE;gBAAEO,CAAC,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACxBpF,OAAA,CAACzD,GAAG;gBAAC2I,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACxDpF,OAAA,CAACjB,SAAS;kBAACmG,EAAE,EAAE;oBAAEa,KAAK,EAAE,SAAS;oBAAEW,EAAE,EAAE,CAAC;oBAAE2B,QAAQ,EAAE;kBAAG;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5DnG,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,IAAI;kBAACX,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAX,QAAA,EAAC;gBAEpE;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNnG,OAAA,CAACtD,IAAI;gBAACiK,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAxB,QAAA,gBACzBpF,OAAA,CAACtD,IAAI;kBAACmK,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAA1B,QAAA,eACfpF,OAAA,CAACzD,GAAG;oBAAC2I,EAAE,EAAE;sBAAEoD,SAAS,EAAE,QAAQ;sBAAE7C,CAAC,EAAE,CAAC;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAAP,QAAA,gBAC1EpF,OAAA,CAACxD,UAAU;sBAACqJ,OAAO,EAAC,IAAI;sBAACX,EAAE,EAAE;wBAAEY,UAAU,EAAE,GAAG;wBAAEC,KAAK,EAAE,SAAS;wBAAEP,EAAE,EAAE;sBAAE,CAAE;sBAAAJ,QAAA,EACvEH,IAAI,CAAChC;oBAAW;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACbnG,OAAA,CAACxD,UAAU;sBAACqJ,OAAO,EAAC,OAAO;sBAACX,EAAE,EAAE;wBAAEa,KAAK,EAAE;sBAAO,CAAE;sBAAAX,QAAA,EAAC;oBAEnD;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACPnG,OAAA,CAACtD,IAAI;kBAACmK,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAA1B,QAAA,eACfpF,OAAA,CAACzD,GAAG;oBAAC2I,EAAE,EAAE;sBAAEoD,SAAS,EAAE,QAAQ;sBAAE7C,CAAC,EAAE,CAAC;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAAP,QAAA,gBAC1EpF,OAAA,CAACxD,UAAU;sBAACqJ,OAAO,EAAC,IAAI;sBAACX,EAAE,EAAE;wBAAEY,UAAU,EAAE,GAAG;wBAAEC,KAAK,EAAE,SAAS;wBAAEP,EAAE,EAAE;sBAAE,CAAE;sBAAAJ,QAAA,EACvEH,IAAI,CAACsD;oBAAW;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACbnG,OAAA,CAACxD,UAAU;sBAACqJ,OAAO,EAAC,OAAO;sBAACX,EAAE,EAAE;wBAAEa,KAAK,EAAE;sBAAO,CAAE;sBAAAX,QAAA,GAAC,eACpC,EAACH,IAAI,CAACuD,gBAAgB,EAAC,IACtC;oBAAA;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACPnG,OAAA,CAACzD,GAAG;gBAAC2I,EAAE,EAAE;kBAAEuD,EAAE,EAAE;gBAAE,CAAE;gBAAArD,QAAA,gBACjBpF,OAAA,CAACzD,GAAG;kBAAC2I,EAAE,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE,eAAe;oBAAEE,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,gBACnEpF,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,OAAO;oBAAAT,QAAA,EAAC;kBAAS;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClDnG,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,OAAO;oBAACX,EAAE,EAAE;sBAAEY,UAAU,EAAE;oBAAI,CAAE;oBAAAV,QAAA,GACjDH,IAAI,CAACuD,gBAAgB,EAAC,GACzB;kBAAA;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNnG,OAAA,CAACzD,GAAG;kBAAC2I,EAAE,EAAE;oBACPC,KAAK,EAAE,MAAM;oBACbgD,MAAM,EAAE,CAAC;oBACTzC,OAAO,EAAE,SAAS;oBAClBC,YAAY,EAAE,CAAC;oBACf+C,QAAQ,EAAE;kBACZ,CAAE;kBAAAtD,QAAA,eACApF,OAAA,CAACzD,GAAG;oBAAC2I,EAAE,EAAE;sBACPC,KAAK,EAAE,GAAGF,IAAI,CAACuD,gBAAgB,GAAG;sBAClCL,MAAM,EAAE,MAAM;sBACdzC,OAAO,EAAE,SAAS;sBAClBiD,UAAU,EAAE;oBACd;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPnG,OAAA,CAACtD,IAAI;UAACmK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACvBpF,OAAA,CAACrD,IAAI;YAACuI,EAAE,EAAE;cAAEiD,MAAM,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAoB,CAAE;YAAAhD,QAAA,eACxDpF,OAAA,CAACpD,WAAW;cAACsI,EAAE,EAAE;gBAAEO,CAAC,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACxBpF,OAAA,CAACzD,GAAG;gBAAC2I,EAAE,EAAE;kBAAEG,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACxDpF,OAAA,CAACzD,GAAG;kBAAC2I,EAAE,EAAE;oBACPQ,OAAO,EAAE,SAAS;oBAClBC,YAAY,EAAE,KAAK;oBACnBF,CAAC,EAAE,CAAC;oBACJiB,EAAE,EAAE,CAAC;oBACLrB,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAAF,QAAA,eACApF,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,IAAI;oBAACX,EAAE,EAAE;sBAAEa,KAAK,EAAE,OAAO;sBAAED,UAAU,EAAE;oBAAO,CAAE;oBAAAV,QAAA,EAAC;kBAErE;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNnG,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,IAAI;kBAACX,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAX,QAAA,EAAC;gBAEpE;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,EAELlB,IAAI,CAACnB,cAAc,gBAClB9D,OAAA,CAAAE,SAAA;gBAAAkF,QAAA,gBACEpF,OAAA,CAACtD,IAAI;kBAACiK,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAC1B,EAAE,EAAE;oBAAEM,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,gBACxCpF,OAAA,CAACtD,IAAI;oBAACmK,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAA1B,QAAA,eACfpF,OAAA,CAACzD,GAAG;sBAAC2I,EAAE,EAAE;wBAAEoD,SAAS,EAAE,QAAQ;wBAAE7C,CAAC,EAAE,CAAC;wBAAEC,OAAO,EAAE,SAAS;wBAAEC,YAAY,EAAE;sBAAE,CAAE;sBAAAP,QAAA,gBAC1EpF,OAAA,CAACxD,UAAU;wBAACqJ,OAAO,EAAC,IAAI;wBAACX,EAAE,EAAE;0BAAEY,UAAU,EAAE,GAAG;0BAAEC,KAAK,EAAE,SAAS;0BAAEP,EAAE,EAAE;wBAAE,CAAE;wBAAAJ,QAAA,EACvEH,IAAI,CAACnB,cAAc,CAACC;sBAAM;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eACbnG,OAAA,CAACxD,UAAU;wBAACqJ,OAAO,EAAC,OAAO;wBAACX,EAAE,EAAE;0BAAEa,KAAK,EAAE;wBAAO,CAAE;wBAAAX,QAAA,EAAC;sBAEnD;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAEPnG,OAAA,CAACtD,IAAI;oBAACmK,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAA1B,QAAA,eACfpF,OAAA,CAACzD,GAAG;sBAAC2I,EAAE,EAAE;wBAAEoD,SAAS,EAAE,QAAQ;wBAAE7C,CAAC,EAAE,CAAC;wBAAEC,OAAO,EAAE,SAAS;wBAAEC,YAAY,EAAE;sBAAE,CAAE;sBAAAP,QAAA,gBAC1EpF,OAAA,CAACxD,UAAU;wBAACqJ,OAAO,EAAC,IAAI;wBAACX,EAAE,EAAE;0BAAEY,UAAU,EAAE,GAAG;0BAAEC,KAAK,EAAE,SAAS;0BAAEP,EAAE,EAAE;wBAAE,CAAE;wBAAAJ,QAAA,EACvEH,IAAI,CAACnB,cAAc,CAACG;sBAAS;wBAAA+B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eACbnG,OAAA,CAACxD,UAAU;wBAACqJ,OAAO,EAAC,OAAO;wBAACX,EAAE,EAAE;0BAAEa,KAAK,EAAE;wBAAO,CAAE;wBAAAX,QAAA,EAAC;sBAEnD;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEPnG,OAAA,CAACzD,GAAG;kBAAC2I,EAAE,EAAE;oBAAEoD,SAAS,EAAE,QAAQ;oBAAE9C,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,gBACtCpF,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,IAAI;oBAACX,EAAE,EAAE;sBAAEY,UAAU,EAAE,GAAG;sBAAEC,KAAK,EAAE,SAAS;sBAAEP,EAAE,EAAE;oBAAE,CAAE;oBAAAJ,QAAA,GACvEH,IAAI,CAACnB,cAAc,CAACE,WAAW,EAAC,GACnC;kBAAA;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbnG,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,OAAO;oBAACX,EAAE,EAAE;sBAAEa,KAAK,EAAE,MAAM;sBAAEP,EAAE,EAAE;oBAAE,CAAE;oBAAAJ,QAAA,EAAC;kBAE1D;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbnG,OAAA,CAACxD,UAAU;oBAACqJ,OAAO,EAAC,SAAS;oBAACX,EAAE,EAAE;sBAAEa,KAAK,EAAE,MAAM;sBAAEsC,QAAQ,EAAE;oBAAU,CAAE;oBAAAjD,QAAA,GACtEH,IAAI,CAACnB,cAAc,CAACP,IAAI,EAAC,iCAC5B;kBAAA;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAGNnG,OAAA,CAACzD,GAAG;kBAAC2I,EAAE,EAAE;oBACPC,KAAK,EAAE,MAAM;oBACbgD,MAAM,EAAE,CAAC;oBACTzC,OAAO,EAAE,SAAS;oBAClBC,YAAY,EAAE,CAAC;oBACf+C,QAAQ,EAAE;kBACZ,CAAE;kBAAAtD,QAAA,eACApF,OAAA,CAACzD,GAAG;oBAAC2I,EAAE,EAAE;sBACPC,KAAK,EAAE,GAAGF,IAAI,CAACnB,cAAc,CAACE,WAAW,GAAG;sBAC5CmE,MAAM,EAAE,MAAM;sBACdzC,OAAO,EAAE,SAAS;sBAClBiD,UAAU,EAAE;oBACd;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,eACN,CAAC,gBAEHnG,OAAA,CAACzD,GAAG;gBAAC2I,EAAE,EAAE;kBAAEoD,SAAS,EAAE,QAAQ;kBAAE7C,CAAC,EAAE,CAAC;kBAAEC,OAAO,EAAE,SAAS;kBAAEC,YAAY,EAAE;gBAAE,CAAE;gBAAAP,QAAA,eAC1EpF,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,OAAO;kBAACX,EAAE,EAAE;oBAAEa,KAAK,EAAE;kBAAO,CAAE;kBAAAX,QAAA,EAAC;gBAEnD;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAKNlB,IAAI,CAAC2D,YAAY,IAAI3D,IAAI,CAAC2D,YAAY,CAACzF,MAAM,GAAG,CAAC,iBAChDnD,OAAA,CAACrD,IAAI;QAACuI,EAAE,EAAE;UAAEkD,MAAM,EAAE;QAAoB,CAAE;QAAAhD,QAAA,eACxCpF,OAAA,CAACpD,WAAW;UAACsI,EAAE,EAAE;YAAEO,CAAC,EAAE;UAAE,CAAE;UAAAL,QAAA,gBACxBpF,OAAA,CAACzD,GAAG;YAAC2I,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxDpF,OAAA,CAACnB,aAAa;cAACqG,EAAE,EAAE;gBAAEa,KAAK,EAAE,SAAS;gBAAEW,EAAE,EAAE,CAAC;gBAAE2B,QAAQ,EAAE;cAAG;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEnG,OAAA,CAACxD,UAAU;cAACqJ,OAAO,EAAC,IAAI;cAACX,EAAE,EAAE;gBAAEY,UAAU,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAU,CAAE;cAAAX,QAAA,EAAC;YAEpE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNnG,OAAA,CAACtD,IAAI;YAACiK,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAxB,QAAA,EACxBH,IAAI,CAAC2D,YAAY,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7ChJ,OAAA,CAACtD,IAAI;cAACmK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eAC9BpF,OAAA,CAACzD,GAAG;gBAAC2I,EAAE,EAAE;kBACPO,CAAC,EAAE,CAAC;kBACJ2C,MAAM,EAAE,mBAAmB;kBAC3BzC,YAAY,EAAE,CAAC;kBACfD,OAAO,EAAEsD,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;kBAC5CL,UAAU,EAAE,UAAU;kBACtB,SAAS,EAAE;oBACTjD,OAAO,EAAE,SAAS;oBAClBuD,SAAS,EAAE,kBAAkB;oBAC7BC,SAAS,EAAE;kBACb;gBACF,CAAE;gBAAA9D,QAAA,gBACApF,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,OAAO;kBAACX,EAAE,EAAE;oBAAEa,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EACtD2D,IAAI,CAAC9D;gBAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACbnG,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,IAAI;kBAACX,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAX,QAAA,GAChE2D,IAAI,CAACI,KAAK,EAAC,GACd;gBAAA;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EACZ6C,KAAK,KAAK,CAAC,iBACVhJ,OAAA,CAAClD,IAAI;kBACH2J,KAAK,EAAC,gBAAa;kBACnBc,IAAI,EAAC,OAAO;kBACZrC,EAAE,EAAE;oBACFuD,EAAE,EAAE,CAAC;oBACL/C,OAAO,EAAE,SAAS;oBAClBK,KAAK,EAAE,OAAO;oBACdsC,QAAQ,EAAE;kBACZ;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GA/B8B6C,KAAK;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgCrC,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGNlB,IAAI,CAAC2D,YAAY,CAACzF,MAAM,GAAG,CAAC,iBAC3BnD,OAAA,CAACzD,GAAG;YAAC2I,EAAE,EAAE;cAAEuD,EAAE,EAAE,CAAC;cAAEH,SAAS,EAAE;YAAS,CAAE;YAAAlD,QAAA,eACtCpF,OAAA,CAACrC,SAAS;cAAAyH,QAAA,gBACRpF,OAAA,CAACpC,gBAAgB;gBAACwL,UAAU,eAAEpJ,OAAA,CAACb,cAAc;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAf,QAAA,eAC/CpF,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,OAAO;kBAACX,EAAE,EAAE;oBAAEa,KAAK,EAAE;kBAAU,CAAE;kBAAAX,QAAA,GAAC,iBACrC,EAACH,IAAI,CAAC2D,YAAY,CAACzF,MAAM,EAAC,SAC3C;gBAAA;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACnBnG,OAAA,CAACnC,gBAAgB;gBAAAuH,QAAA,eACfpF,OAAA,CAACP,eAAe;kBACdwF,IAAI,EAAEA,IAAI,CAAC2D,YAAY,CAACE,GAAG,CAACC,IAAI,KAAK;oBACnC9D,IAAI,EAAE8D,IAAI,CAAC9D,IAAI;oBACfkE,KAAK,EAAE,GAAGJ,IAAI,CAACI,KAAK;kBACtB,CAAC,CAAC,CAAE;kBACJE,OAAO,EAAE,CACP;oBAAEC,KAAK,EAAE,MAAM;oBAAEC,UAAU,EAAE,MAAM;oBAAEpE,KAAK,EAAE;kBAAI,CAAC,EACjD;oBAAEmE,KAAK,EAAE,OAAO;oBAAEC,UAAU,EAAE,cAAc;oBAAEpE,KAAK,EAAE,GAAG;oBAAEqE,KAAK,EAAE;kBAAQ,CAAC,CAC1E;kBACFC,UAAU,EAAE,IAAK;kBACjBC,QAAQ,EAAE;gBAAG;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMwD,eAAe,GAAI1E,IAAI,iBAC3BjF,OAAA,CAACzD,GAAG;IAAA6I,QAAA,gBAEFpF,OAAA,CAACzD,GAAG;MAAC2I,EAAE,EAAE;QACPG,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfyC,MAAM,EAAE;MACV,CAAE;MAAAhD,QAAA,gBACApF,OAAA,CAAC3B,QAAQ;QAAC6G,EAAE,EAAE;UAAEa,KAAK,EAAE,SAAS;UAAEW,EAAE,EAAE,CAAC;UAAE2B,QAAQ,EAAE;QAAG;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DnG,OAAA,CAACxD,UAAU;QAACqJ,OAAO,EAAC,IAAI;QAACX,EAAE,EAAE;UAAEY,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAX,QAAA,EAAC;MAEpE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGLvE,UAAU,iBACT5B,OAAA,CAACzD,GAAG;MAAC2I,EAAE,EAAE;QAAEM,EAAE,EAAE,CAAC;QAAEL,KAAK,EAAE;MAAO,CAAE;MAAAC,QAAA,eAChCpF,OAAA,CAACH,QAAQ;QAACoF,IAAI,EAAEA;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGE,CACN;EAMD,MAAMyD,uBAAuB,GAAI3E,IAAI,iBACnCjF,OAAA,CAACzD,GAAG;IAAA6I,QAAA,gBAEFpF,OAAA,CAACzD,GAAG;MAAC2I,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFpF,OAAA,CAACxD,UAAU;QAACqJ,OAAO,EAAC,IAAI;QAACX,EAAE,EAAE;UAAEY,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAX,QAAA,EAAC;MAEzE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnG,OAAA,CAACjC,gBAAgB;QACfqI,OAAO,eACLpG,OAAA,CAAClC,MAAM;UACLuI,OAAO,EAAEzE,UAAW;UACpB0E,QAAQ,EAAGC,CAAC,IAAK1E,aAAa,CAAC0E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDN,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDM,KAAK,eACHzG,OAAA,CAACzD,GAAG;UAAC2I,EAAE,EAAE;YAAEG,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjDpF,OAAA,CAACX,aAAa;YAAC6F,EAAE,EAAE;cAAEwB,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNnG,OAAA,CAACtD,IAAI;MAACiK,SAAS;MAACC,OAAO,EAAE,CAAE;MAAC1B,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxCpF,OAAA,CAACtD,IAAI;QAACmK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA5B,QAAA,eACvBpF,OAAA,CAACvD,KAAK;UAACyI,EAAE,EAAE;YAAEO,CAAC,EAAE,CAAC;YAAE6C,SAAS,EAAE,QAAQ;YAAE5C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAX,QAAA,gBAChFpF,OAAA,CAACxD,UAAU;YAACqJ,OAAO,EAAC,IAAI;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDH,IAAI,CAAC4E,oBAAoB,EAAC,GAC7B;UAAA;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnG,OAAA,CAACxD,UAAU;YAACqJ,OAAO,EAAC,OAAO;YAAAT,QAAA,EAAC;UAAY;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrDnG,OAAA,CAACxD,UAAU;YAACqJ,OAAO,EAAC,SAAS;YAAAT,QAAA,GAAEH,IAAI,CAAC9D,WAAW,EAAC,KAAG,EAAC8D,IAAI,CAAC7D,SAAS;UAAA;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPnG,OAAA,CAACtD,IAAI;QAACmK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA5B,QAAA,eACvBpF,OAAA,CAACvD,KAAK;UAACyI,EAAE,EAAE;YAAEO,CAAC,EAAE,CAAC;YAAE6C,SAAS,EAAE,QAAQ;YAAE5C,OAAO,EAAE,WAAW;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAX,QAAA,gBAC7EpF,OAAA,CAACxD,UAAU;YAACqJ,OAAO,EAAC,IAAI;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,EACxDH,IAAI,CAAC6E;UAAa;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACbnG,OAAA,CAACxD,UAAU;YAACqJ,OAAO,EAAC,OAAO;YAAAT,QAAA,EAAC;UAAa;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPnG,OAAA,CAACtD,IAAI;QAACmK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA5B,QAAA,eACvBpF,OAAA,CAACvD,KAAK;UAACyI,EAAE,EAAE;YAAEO,CAAC,EAAE,CAAC;YAAE6C,SAAS,EAAE,QAAQ;YAAE5C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAX,QAAA,gBAChFpF,OAAA,CAACxD,UAAU;YAACqJ,OAAO,EAAC,IAAI;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDH,IAAI,CAAC6C,iBAAiB,EAAC,GAC1B;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnG,OAAA,CAACxD,UAAU;YAACqJ,OAAO,EAAC,OAAO;YAAAT,QAAA,EAAC;UAAY;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPnG,OAAA,CAACtD,IAAI;QAACmK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA5B,QAAA,eACvBpF,OAAA,CAACvD,KAAK;UAACyI,EAAE,EAAE;YAAEO,CAAC,EAAE,CAAC;YAAE6C,SAAS,EAAE,QAAQ;YAAE5C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAX,QAAA,gBAChFpF,OAAA,CAACxD,UAAU;YAACqJ,OAAO,EAAC,IAAI;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxD/B,IAAI,CAACC,KAAK,CAAC2B,IAAI,CAAC4E,oBAAoB,GAAG5E,IAAI,CAAC6E,aAAa,GAAG,CAAC,CAAC,EAAC,GAClE;UAAA;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnG,OAAA,CAACxD,UAAU;YAACqJ,OAAO,EAAC,OAAO;YAAAT,QAAA,EAAC;UAAe;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNvE,UAAU,iBACT5B,OAAA,CAACzD,GAAG;MAAC2I,EAAE,EAAE;QAAEM,EAAE,EAAE,CAAC;QAAEL,KAAK,EAAE;MAAO,CAAE;MAAAC,QAAA,eAChCpF,OAAA,CAACF,aAAa;QAACmF,IAAI,EAAEA;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDnG,OAAA,CAACvD,KAAK;MAACyI,EAAE,EAAE;QAAEO,CAAC,EAAE;MAAE,CAAE;MAAAL,QAAA,gBAClBpF,OAAA,CAACxD,UAAU;QAACqJ,OAAO,EAAC,IAAI;QAACX,EAAE,EAAE;UAAEM,EAAE,EAAE,CAAC;UAAEM,UAAU,EAAE;QAAI,CAAE;QAAAV,QAAA,EAAC;MAEzD;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnG,OAAA,CAACP,eAAe;QACdwF,IAAI,EAAEA,IAAI,CAAC8E,gBAAgB,IAAI,EAAG;QAClCV,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,MAAM;UAAEpE,KAAK,EAAE;QAAI,CAAC,EACjD;UAAEmE,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,cAAc;UAAEpE,KAAK,EAAE,GAAG;UAAEqE,KAAK,EAAE,OAAO;UAAEQ,QAAQ,EAAE,QAAQ;UAC1FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACf,KAAK;QAAI,CAAC,CACxC;QACFO,QAAQ,EAAE;MAAG;QAAA1D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAID,MAAMgE,YAAY,GAAGA,CAAA,kBACnBnK,OAAA,CAAC9C,MAAM;IAACyH,IAAI,EAAEjE,UAAW;IAAC0J,OAAO,EAAErF,iBAAkB;IAACsF,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAlF,QAAA,gBAC3EpF,OAAA,CAAC7C,WAAW;MAAAiI,QAAA,EACTxE,UAAU,KAAK,cAAc,GAAG,yBAAyB,GAAG;IAAe;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eACdnG,OAAA,CAAC5C,aAAa;MAAAgI,QAAA,GACX5E,KAAK,iBACJR,OAAA,CAACjD,KAAK;QAACwN,QAAQ,EAAC,OAAO;QAACrF,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnC5E;MAAK;QAAAwF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDnG,OAAA,CAACtD,IAAI;QAACiK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC1B,EAAE,EAAE;UAAEuD,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,gBACxCpF,OAAA,CAACtD,IAAI;UAACmK,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA1B,QAAA,eAChBpF,OAAA,CAAC1C,WAAW;YAACgN,SAAS;YAAAlF,QAAA,gBACpBpF,OAAA,CAACzC,UAAU;cAAA6H,QAAA,EAAC;YAAO;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChCnG,OAAA,CAACxC,MAAM;cACL0J,KAAK,EAAElG,QAAQ,CAACE,OAAQ;cACxBuF,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAKtF,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAEqF,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cAAA9B,QAAA,gBAEvEpF,OAAA,CAACvC,QAAQ;gBAACyJ,KAAK,EAAC,OAAO;gBAAA9B,QAAA,EAAC;cAAoB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvDnG,OAAA,CAACvC,QAAQ;gBAACyJ,KAAK,EAAC,KAAK;gBAAA9B,QAAA,EAAC;cAAY;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7CnG,OAAA,CAACvC,QAAQ;gBAACyJ,KAAK,EAAC,OAAO;gBAAA9B,QAAA,EAAC;cAAc;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAINvF,UAAU,KAAK,cAAc,iBAC5BZ,OAAA,CAAAE,SAAA;UAAAkF,QAAA,gBACEpF,OAAA,CAACtD,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACfpF,OAAA,CAACtC,SAAS;cACR4M,SAAS;cACTE,IAAI,EAAC,MAAM;cACX/D,KAAK,EAAC,aAAa;cACnBS,KAAK,EAAElG,QAAQ,CAACG,WAAY;cAC5BmF,QAAQ,EAAGC,CAAC,IAAKtF,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEoF,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cAC3EuD,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnG,OAAA,CAACtD,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA1B,QAAA,eACfpF,OAAA,CAACtC,SAAS;cACR4M,SAAS;cACTE,IAAI,EAAC,MAAM;cACX/D,KAAK,EAAC,WAAW;cACjBS,KAAK,EAAElG,QAAQ,CAACI,SAAU;cAC1BkF,QAAQ,EAAGC,CAAC,IAAKtF,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAEmF,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cACzEuD,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChBnG,OAAA,CAAC3C,aAAa;MAAA+H,QAAA,gBACZpF,OAAA,CAACnD,MAAM;QAAC8N,OAAO,EAAE5F,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpDnG,OAAA,CAACnD,MAAM;QACL8N,OAAO,EAAE7F,oBAAqB;QAC9Be,OAAO,EAAC,WAAW;QACnB+E,QAAQ,EAAEtK,OAAQ;QAClBuK,SAAS,EAAEvK,OAAO,gBAAGN,OAAA,CAAChD,gBAAgB;UAACuK,IAAI,EAAE;QAAG;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGnG,OAAA,CAACvB,cAAc;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAf,QAAA,EAExE9E,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAA0F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACEnG,OAAA,CAACzD,GAAG;IAAC2L,SAAS,EAAC,sCAAsC;IAAA9C,QAAA,gBAEnDpF,OAAA,CAACzD,GAAG;MAAC2I,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACpFpF,OAAA,CAACT,eAAe;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGL7F,OAAO,iBACNN,OAAA,CAACzD,GAAG;MAAC2I,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEwF,EAAE,EAAE;MAAE,CAAE;MAAA1F,QAAA,eAC5DpF,OAAA,CAAChD,gBAAgB;QAAAgJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDnG,OAAA,CAACzD,GAAG;MAAC2I,EAAE,EAAE;QAAEuD,EAAE,EAAE;MAAE,CAAE;MAAArD,QAAA,gBAEjBpF,OAAA,CAACzD,GAAG;QAAC2I,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACjBpF,OAAA,CAACxD,UAAU;UAACqJ,OAAO,EAAC,IAAI;UAACX,EAAE,EAAE;YAAEY,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE,SAAS;YAAEP,EAAE,EAAE,CAAC;YAAE8C,SAAS,EAAE;UAAS,CAAE;UAAAlD,QAAA,EAAC;QAEhG;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnG,OAAA,CAACtD,IAAI;UAACiK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAxB,QAAA,gBAEzBpF,OAAA,CAACtD,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eAC7BpF,OAAA,CAACrD,IAAI;cACHuL,SAAS,EAAE,eAAepH,kBAAkB,KAAK,UAAU,GAAG,sBAAsB,GAAG,EAAE,EAAG;cAC5FoE,EAAE,EAAE;gBACFiD,MAAM,EAAE,OAAO;gBACf4C,MAAM,EAAE,SAAS;gBACjB3C,MAAM,EAAEtH,kBAAkB,KAAK,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;gBACrF4E,OAAO,EAAE5E,kBAAkB,KAAK,UAAU,GAAG,SAAS,GAAG,OAAO;gBAChE6H,UAAU,EAAE;cACd,CAAE;cACFgC,OAAO,EAAEA,CAAA,KAAM5J,qBAAqB,CAAC,UAAU,CAAE;cAAAqE,QAAA,eAEjDpF,OAAA,CAACpD,WAAW;gBAACsI,EAAE,EAAE;kBAAEO,CAAC,EAAE,CAAC;kBAAE6C,SAAS,EAAE,QAAQ;kBAAEH,MAAM,EAAE,MAAM;kBAAE9C,OAAO,EAAE,MAAM;kBAAE2F,aAAa,EAAE,QAAQ;kBAAE1F,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjIpF,OAAA,CAAC/B,cAAc;kBAACiH,EAAE,EAAE;oBAAEmD,QAAQ,EAAE,EAAE;oBAAEtC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjEnG,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,WAAW;kBAACX,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE6C,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEtF;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnG,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,OAAO;kBAACX,EAAE,EAAE;oBAAEa,KAAK,EAAE,MAAM;oBAAEsC,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEvE;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPnG,OAAA,CAACtD,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eAC7BpF,OAAA,CAACrD,IAAI;cACHuI,EAAE,EAAE;gBACFiD,MAAM,EAAE,OAAO;gBACf4C,MAAM,EAAE,SAAS;gBACjB3C,MAAM,EAAEtH,kBAAkB,KAAK,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;gBAChF4E,OAAO,EAAE5E,kBAAkB,KAAK,KAAK,GAAG,SAAS,GAAG,OAAO;gBAC3D6H,UAAU,EAAE;cACd,CAAE;cACFgC,OAAO,EAAEA,CAAA,KAAM5J,qBAAqB,CAAC,KAAK,CAAE;cAAAqE,QAAA,eAE5CpF,OAAA,CAACpD,WAAW;gBAACsI,EAAE,EAAE;kBAAEO,CAAC,EAAE,CAAC;kBAAE6C,SAAS,EAAE,QAAQ;kBAAEH,MAAM,EAAE,MAAM;kBAAE9C,OAAO,EAAE,MAAM;kBAAE2F,aAAa,EAAE,QAAQ;kBAAE1F,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjIpF,OAAA,CAAC3B,QAAQ;kBAAC6G,EAAE,EAAE;oBAAEmD,QAAQ,EAAE,EAAE;oBAAEtC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3DnG,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,WAAW;kBAACX,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE6C,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEtF;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnG,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,OAAO;kBAACX,EAAE,EAAE;oBAAEa,KAAK,EAAE,MAAM;oBAAEsC,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEvE;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAKPnG,OAAA,CAACtD,IAAI;YAACmK,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eAC7BpF,OAAA,CAACrD,IAAI;cACHuI,EAAE,EAAE;gBACFiD,MAAM,EAAE,OAAO;gBACf4C,MAAM,EAAE,SAAS;gBACjB3C,MAAM,EAAEtH,kBAAkB,KAAK,cAAc,GAAG,mBAAmB,GAAG,mBAAmB;gBACzF4E,OAAO,EAAE5E,kBAAkB,KAAK,cAAc,GAAG,SAAS,GAAG,OAAO;gBACpE6H,UAAU,EAAE;cACd,CAAE;cACFgC,OAAO,EAAEA,CAAA,KAAM5J,qBAAqB,CAAC,cAAc,CAAE;cAAAqE,QAAA,eAErDpF,OAAA,CAACpD,WAAW;gBAACsI,EAAE,EAAE;kBAAEO,CAAC,EAAE,CAAC;kBAAE6C,SAAS,EAAE,QAAQ;kBAAEH,MAAM,EAAE,MAAM;kBAAE9C,OAAO,EAAE,MAAM;kBAAE2F,aAAa,EAAE,QAAQ;kBAAE1F,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjIpF,OAAA,CAAC7B,YAAY;kBAAC+G,EAAE,EAAE;oBAAEmD,QAAQ,EAAE,EAAE;oBAAEtC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/DnG,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,WAAW;kBAACX,EAAE,EAAE;oBAAEY,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE6C,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEtF;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnG,OAAA,CAACxD,UAAU;kBAACqJ,OAAO,EAAC,OAAO;kBAACX,EAAE,EAAE;oBAAEa,KAAK,EAAE,MAAM;oBAAEsC,QAAQ,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,EAAC;gBAEvE;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNnG,OAAA,CAACzD,GAAG;QAAC2I,EAAE,EAAE;UAAE+F,SAAS,EAAE,OAAO;UAAE9F,KAAK,EAAE;QAAO,CAAE;QAAAC,QAAA,GAE5CtE,kBAAkB,KAAK,UAAU,iBAChCd,OAAA,CAACvD,KAAK;UAACyI,EAAE,EAAE;YAAEO,CAAC,EAAE,CAAC;YAAEN,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,EAChC9D,WAAW,CAACE,QAAQ,gBACnBxB,OAAA,CAACzD,GAAG;YAAC2I,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACzBpF,OAAA,CAACzD,GAAG;cAAC2I,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACpEpF,OAAA,CAACnD,MAAM;gBACLgO,SAAS,eAAE7K,OAAA,CAACzB,YAAY;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BwE,OAAO,EAAEA,CAAA,KAAMzG,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;gBAC3D2B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfb,EAAE,EAAE;kBAAEwB,EAAE,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,EACf;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnG,OAAA,CAACnD,MAAM;gBACLgO,SAAS,eAAE7K,OAAA,CAACzB,YAAY;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BwE,OAAO,EAAEA,CAAA,KAAMzG,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;gBAC7D2B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAX,QAAA,EAChB;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLnB,oBAAoB,CAAC1D,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJ7F,OAAO,gBACTN,OAAA,CAACN,UAAU;YACT8K,IAAI,EAAC,SAAS;YACdrG,UAAU,EAAC,UAAU;YACrB8C,KAAK,EAAC,mCAAmC;YACzCiE,WAAW,EAAC;UAAsD;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,gBAEFnG,OAAA,CAACN,UAAU;YACT8K,IAAI,EAAC,OAAO;YACZrG,UAAU,EAAC,UAAU;YACrB8C,KAAK,EAAC,wBAAwB;YAC9BiE,WAAW,EAAC,mFAAmF;YAC/FC,OAAO,EAAEA,CAAA,KAAM;cACb5K,UAAU,CAAC,IAAI,CAAC;cAChBf,aAAa,CAACyC,iBAAiB,CAAC5B,UAAU,EAAE,OAAO,CAAC,CACjD+K,IAAI,CAACnG,IAAI,IAAI;gBACZ1D,cAAc,CAACiD,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACPhD,QAAQ,EAAEyD,IAAI,CAAC5C;gBACjB,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAAC5B,KAAK,CAAC,iCAAiC,EAAE2B,GAAG,CAAC;cACvD,CAAC,CAAC,CACDkJ,OAAO,CAAC,MAAM;gBACb9K,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGArF,kBAAkB,KAAK,KAAK,iBAC3Bd,OAAA,CAACvD,KAAK;UAACyI,EAAE,EAAE;YAAEO,CAAC,EAAE,CAAC;YAAEN,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,EAChC9D,WAAW,CAACG,GAAG,gBACdzB,OAAA,CAACzD,GAAG;YAAC2I,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACzBpF,OAAA,CAACzD,GAAG;cAAC2I,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACpEpF,OAAA,CAACnD,MAAM;gBACLgO,SAAS,eAAE7K,OAAA,CAACzB,YAAY;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BwE,OAAO,EAAEA,CAAA,KAAMzG,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtD2B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfb,EAAE,EAAE;kBAAEwB,EAAE,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,EACf;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnG,OAAA,CAACnD,MAAM;gBACLgO,SAAS,eAAE7K,OAAA,CAACzB,YAAY;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BwE,OAAO,EAAEA,CAAA,KAAMzG,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxD2B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAX,QAAA,EAChB;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLwD,eAAe,CAACrI,WAAW,CAACG,GAAG,CAAC;UAAA;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJ7F,OAAO,gBACTN,OAAA,CAACN,UAAU;YACT8K,IAAI,EAAC,SAAS;YACdrG,UAAU,EAAC,KAAK;YAChB8C,KAAK,EAAC,mCAAmC;YACzCiE,WAAW,EAAC;UAAyC;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,gBAEFnG,OAAA,CAACN,UAAU;YACT8K,IAAI,EAAC,OAAO;YACZrG,UAAU,EAAC,KAAK;YAChB8C,KAAK,EAAC,wBAAwB;YAC9BiE,WAAW,EAAC,gFAAgF;YAC5FC,OAAO,EAAEA,CAAA,KAAM;cACb5K,UAAU,CAAC,IAAI,CAAC;cAChBf,aAAa,CAAC+C,mBAAmB,CAAClC,UAAU,EAAE,OAAO,CAAC,CACnD+K,IAAI,CAACnG,IAAI,IAAI;gBACZ1D,cAAc,CAACiD,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACP/C,GAAG,EAAEwD,IAAI,CAAC5C;gBACZ,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAAC5B,KAAK,CAAC,4BAA4B,EAAE2B,GAAG,CAAC;cAClD,CAAC,CAAC,CACDkJ,OAAO,CAAC,MAAM;gBACb9K,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAKArF,kBAAkB,KAAK,cAAc,iBACpCd,OAAA,CAACvD,KAAK;UAACyI,EAAE,EAAE;YAAEO,CAAC,EAAE;UAAE,CAAE;UAAAL,QAAA,EACjB9D,WAAW,CAACK,WAAW,gBACtB3B,OAAA,CAACzD,GAAG;YAAA6I,QAAA,gBACFpF,OAAA,CAACzD,GAAG;cAAC2I,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9DpF,OAAA,CAACnD,MAAM;gBACLgO,SAAS,eAAE7K,OAAA,CAACzB,YAAY;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BwE,OAAO,EAAEA,CAAA,KAAMzG,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/D2B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfb,EAAE,EAAE;kBAAEwB,EAAE,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,EACf;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnG,OAAA,CAACnD,MAAM;gBACLgO,SAAS,eAAE7K,OAAA,CAACzB,YAAY;kBAAAyH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BwE,OAAO,EAAEA,CAAA,KAAMzG,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjE2B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAX,QAAA,EAChB;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLyD,uBAAuB,CAACtI,WAAW,CAACK,WAAW,CAAC;UAAA;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,gBAENnG,OAAA,CAACN,UAAU;YACT8K,IAAI,EAAC,iBAAiB;YACtBrG,UAAU,EAAC,cAAc;YACzB8C,KAAK,EAAC,sBAAsB;YAC5BiE,WAAW,EAAC,8GAA2G;YACvHI,WAAW,EAAC,mBAAmB;YAC/BC,QAAQ,EAAEA,CAAA,KAAM;cACd1K,aAAa,CAAC,cAAc,CAAC;cAC7B;cACA,MAAM2K,KAAK,GAAG,IAAIhI,IAAI,CAAC,CAAC;cACxB,MAAMiI,SAAS,GAAG,IAAIjI,IAAI,CAAC,CAAC;cAC5BiI,SAAS,CAACC,QAAQ,CAACF,KAAK,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;cAExC1K,WAAW,CAAC;gBACV,GAAGD,QAAQ;gBACXG,WAAW,EAAEsK,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClDzK,SAAS,EAAEoK,KAAK,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC;cACFlL,aAAa,CAAC,IAAI,CAAC;YACrB;UAAE;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLgE,YAAY,CAAC,CAAC;EAAA;IAAAnE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC/F,EAAA,CAx+BID,iBAAiB;EAAA,QACEb,SAAS;AAAA;AAAAwM,EAAA,GAD5B3L,iBAAiB;AA0+BvB,eAAeA,iBAAiB;AAAC,IAAA2L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}