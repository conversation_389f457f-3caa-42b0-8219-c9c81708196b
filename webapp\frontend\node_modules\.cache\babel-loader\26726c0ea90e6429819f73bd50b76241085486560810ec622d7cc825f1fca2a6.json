{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\QuickAddCablesDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, TextField, Checkbox, FormControlLabel, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Alert, IconButton, Chip, Tooltip, Tabs, Tab, List, ListItem, ListItemText, ListItemSecondaryAction, Divider, Card, CardContent, CardHeader, Badge } from '@mui/material';\nimport { Add as AddIcon, Delete as DeleteIcon, Save as SaveIcon, Info as InfoIcon, Warning as WarningIcon, Search as SearchIcon, Cable as CableIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n// Utility per verificare compatibilità tra cavo e bobina\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst isCompatible = (cavo, bobina) => {\n  return cavo.tipologia === bobina.tipologia && String(cavo.sezione) === String(bobina.sezione);\n};\n\n// Utility per ottenere il numero della bobina dall'ID\nconst getBobinaNumber = idBobina => {\n  if (!idBobina) return '';\n  const parts = idBobina.split('-');\n  return parts.length > 1 ? parts[1] : idBobina;\n};\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * Implementa sistema di doppia lista per cavi compatibili/incompatibili\n * con gestione intelligente delle incompatibilità e dialog moderni\n */\nconst QuickAddCablesDialog = ({\n  open,\n  onClose,\n  bobina,\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  var _bobina$metri_residui, _bobina$metri_residui2;\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per i dati separati\n  const [allCavi, setAllCavi] = useState([]);\n  const [caviCompatibili, setCaviCompatibili] = useState([]);\n  const [caviIncompatibili, setCaviIncompatibili] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n\n  // Stati per la UI\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);\n  const [incompatibleSelection, setIncompatibleSelection] = useState(null);\n  const [showOverDialog, setShowOverDialog] = useState(false);\n  const [overDialogData, setOverDialogData] = useState(null);\n\n  // Stati per la validazione\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina && cantiereId) {\n      loadCavi();\n      // Reset stati quando si apre il dialog\n      resetDialogState();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Reset dello stato del dialog\n  const resetDialogState = () => {\n    setSelectedCavi([]);\n    setCaviMetri({});\n    setErrors({});\n    setWarnings({});\n    setSearchTerm('');\n    setActiveTab(0);\n    setShowIncompatibleDialog(false);\n    setIncompatibleSelection(null);\n    setShowOverDialog(false);\n    setOverDialogData(null);\n  };\n\n  // Funzione per caricare i cavi con sistema di doppia lista\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi disponibili (non installati e non SPARE)\n      const caviDisponibili = caviData.filter(cavo => !isCableInstalled(cavo) && cavo.modificato_manualmente !== 3);\n\n      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)\n      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));\n      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));\n      setAllCavi(caviDisponibili);\n      setCaviCompatibili(compatibili);\n      setCaviIncompatibili(incompatibili);\n      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione/deselezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n      if (isSelected) {\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = {\n          ...caviMetri\n        };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo\n  const validateMetri = (cavoId, value) => {\n    const cavo = cavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n    setWarnings(prev => {\n      const newWarnings = {\n        ...prev\n      };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore per i metri posati'\n      }));\n      return false;\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina\n    const metriTotaliRichiesti = Object.entries(caviMetri).filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n    .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m)`\n      }));\n    }\n    return true;\n  };\n\n  // Valida tutti i metri inseriti\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n        // Non mostrare popup di conferma, solo l'avviso nel form\n      }\n    }\n\n    // Verifica che i metri totali richiesti non superino i metri residui della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      // Questo è un avviso globale, non specifico per un cavo\n      if (!window.confirm(`ATTENZIONE: I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n        isValid = false;\n      }\n    }\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n    return isValid;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    try {\n      // Validazione\n      if (!validateAllMetri()) {\n        return;\n      }\n      setSaving(true);\n\n      // Conferma finale\n      if (!window.confirm(`Confermi l'aggiornamento di ${selectedCavi.length} cavi con la bobina ${bobina.id_bobina}?`)) {\n        setSaving(false);\n        return;\n      }\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const forceOver = metriGiàUtilizzati + metriPosati > bobina.metri_residui;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, bobina.id_bobina, forceOver);\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato\n      if (errors.length === 0) {\n        // Tutti i cavi sono stati aggiornati con successo\n        onSuccess(`${results.length} cavi aggiornati con successo`);\n        onClose();\n      } else if (results.length > 0) {\n        // Alcuni cavi sono stati aggiornati, altri no\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        // Nessun cavo è stato aggiornato\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filteredCavi = cavi.filter(cavo => cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: [\"Aggiungi cavi alla bobina \", (bobina === null || bobina === void 0 ? void 0 : bobina.numero_bobina) || '']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: !bobina ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: \"Nessuna bobina selezionata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Dettagli bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"ID Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: bobina.id_bobina\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: bobina.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Conduttori\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: [bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Metri residui\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: [((_bobina$metri_residui = bobina.metri_residui) === null || _bobina$metri_residui === void 0 ? void 0 : _bobina$metri_residui.toFixed(1)) || '0', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: bobina.stato_bobina || 'N/D',\n                size: \"small\",\n                color: bobina.stato_bobina === 'Disponibile' ? 'success' : bobina.stato_bobina === 'In uso' ? 'primary' : bobina.stato_bobina === 'Over' ? 'error' : bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Cerca cavi\",\n          variant: \"outlined\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"Cerca per ID, tipologia, ubicazione...\",\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 13\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 15\n        }, this) : filteredCavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Nessun cavo compatibile disponibile per questa bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: '#f5f5f5'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  padding: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Ubicazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri Teorici\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri Posati\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: filteredCavi.map(cavo => {\n                const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  hover: true,\n                  selected: isSelected,\n                  onClick: () => handleCavoSelect(cavo),\n                  sx: {\n                    cursor: 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    padding: \"checkbox\",\n                    children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      checked: isSelected,\n                      onChange: e => {\n                        e.stopPropagation();\n                        handleCavoSelect(cavo);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.tipologia || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: `Da: ${cavo.ubicazione_partenza || 'N/A'} - A: ${cavo.ubicazione_arrivo || 'N/A'}`,\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          maxWidth: 150,\n                          overflow: 'hidden',\n                          textOverflow: 'ellipsis',\n                          whiteSpace: 'nowrap'\n                        },\n                        children: [cavo.ubicazione_partenza || 'N/A', \" \\u2192 \", cavo.ubicazione_arrivo || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 482,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.metri_teorici || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: isSelected ? /*#__PURE__*/_jsxDEV(TextField, {\n                      size: \"small\",\n                      type: \"number\",\n                      value: caviMetri[cavo.id_cavo] || '',\n                      onChange: e => {\n                        e.stopPropagation();\n                        handleMetriChange(cavo.id_cavo, e.target.value);\n                      },\n                      onClick: e => e.stopPropagation(),\n                      error: !!errors[cavo.id_cavo],\n                      helperText: errors[cavo.id_cavo] || warnings[cavo.id_cavo],\n                      FormHelperTextProps: {\n                        sx: {\n                          color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main'\n                        }\n                      },\n                      InputProps: {\n                        endAdornment: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                          title: warnings[cavo.id_cavo],\n                          children: /*#__PURE__*/_jsxDEV(WarningIcon, {\n                            color: \"warning\",\n                            fontSize: \"small\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 507,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 506,\n                          columnNumber: 37\n                        }, this) : null\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 31\n                    }, this) : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: cavo.stato_installazione || 'Da installare',\n                      color: getCableStateColor(cavo.stato_installazione)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 517,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 27\n                  }, this)]\n                }, cavo.id_cavo, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 15\n        }, this), selectedCavi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: [\"Riepilogo selezione (\", selectedCavi.length, \" cavi)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  sx: {\n                    bgcolor: '#f5f5f5'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"ID Cavo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Posati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Azioni\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: [selectedCavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      size: \"small\",\n                      type: \"number\",\n                      value: caviMetri[cavo.id_cavo] || '',\n                      onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n                      error: !!errors[cavo.id_cavo],\n                      helperText: errors[cavo.id_cavo] || warnings[cavo.id_cavo],\n                      FormHelperTextProps: {\n                        sx: {\n                          color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 551,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleCavoSelect(cavo),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 569,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 27\n                  }, this)]\n                }, cavo.id_cavo, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 25\n                }, this)), /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: /*#__PURE__*/_jsxDEV(TableCell, {\n                    colSpan: 3,\n                    align: \"right\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri totali richiesti:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 577,\n                        columnNumber: 29\n                      }, this), \" \", Object.values(caviMetri).reduce((sum, metri) => {\n                        const value = parseFloat(metri || 0);\n                        return isNaN(value) ? sum : sum + value;\n                      }, 0).toFixed(1), \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 576,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri residui bobina:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 585,\n                        columnNumber: 29\n                      }, this), \" \", ((_bobina$metri_residui2 = bobina.metri_residui) === null || _bobina$metri_residui2 === void 0 ? void 0 : _bobina$metri_residui2.toFixed(1)) || '0', \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 15\n        }, this), Object.keys(warnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: Object.entries(warnings).map(([cavoId, warning]) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [cavoId, \": \", warning]\n            }, cavoId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"Seleziona i cavi che vuoi associare a questa bobina e inserisci i metri posati per ciascuno. I metri posati verranno sottratti dai metri residui della bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        disabled: saving,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        color: \"primary\",\n        variant: \"contained\",\n        disabled: saving || selectedCavi.length === 0 || Object.keys(errors).length > 0,\n        startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 64\n        }, this),\n        children: \"Salva\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 615,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 377,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickAddCablesDialog, \"AbIIuLGdIuO/w2uebXwSQvCbijs=\");\n_c = QuickAddCablesDialog;\nexport default QuickAddCablesDialog;\nvar _c;\n$RefreshReg$(_c, \"QuickAddCablesDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "TextField", "Checkbox", "FormControlLabel", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "CircularProgress", "<PERSON><PERSON>", "IconButton", "Chip", "<PERSON><PERSON><PERSON>", "Tabs", "Tab", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Divider", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Badge", "Add", "AddIcon", "Delete", "DeleteIcon", "Save", "SaveIcon", "Info", "InfoIcon", "Warning", "WarningIcon", "Search", "SearchIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "caviService", "determineCableState", "getCableStateColor", "isCableInstalled", "IncompatibleReelDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "isCompatible", "cavo", "bobina", "tipologia", "String", "sezione", "getBobinaNumber", "idBobina", "parts", "split", "length", "QuickAddCablesDialog", "open", "onClose", "cantiereId", "onSuccess", "onError", "_s", "_bobina$metri_residui", "_bobina$metri_residui2", "loading", "setLoading", "caviLoading", "setCaviLoading", "saving", "setSaving", "allCavi", "set<PERSON><PERSON><PERSON><PERSON>", "caviCompatibili", "setCaviCompatibili", "caviIncompatibili", "setCaviIncompatibili", "<PERSON><PERSON><PERSON>", "setSelectedCavi", "caviMetri", "setCaviMetri", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "showIncompatibleDialog", "setShowIncompatibleDialog", "incompatibleSelection", "setIncompatibleSelection", "showOverDialog", "setShowOverDialog", "overDialogData", "setOverDialogData", "errors", "setErrors", "warnings", "setWarnings", "loadCavi", "resetDialogState", "caviData", "get<PERSON><PERSON>", "caviDisponibili", "filter", "modificato_manualmente", "compatibili", "incompatibili", "console", "log", "error", "message", "handleCavoSelect", "prev", "isSelected", "some", "c", "id_cavo", "newSelected", "newCaviMetri", "handleMetriChange", "cavoId", "value", "validate<PERSON>etri", "cavi", "find", "newErrors", "newWarnings", "trim", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "metriTotaliRichiesti", "Object", "entries", "id", "_", "reduce", "sum", "metri", "metri_residui", "validateAllMetri", "<PERSON><PERSON><PERSON><PERSON>", "values", "window", "confirm", "handleSave", "id_bobina", "results", "metriGiàUtilizzati", "r", "forceOver", "result", "updateMetri<PERSON><PERSON><PERSON>", "push", "success", "map", "e", "join", "filteredCavi", "toLowerCase", "includes", "ubicazione_partenza", "ubicazione_arrivo", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "numero_bobina", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "sx", "mb", "p", "bgcolor", "borderRadius", "variant", "gutterBottom", "display", "flexWrap", "gap", "color", "n_conduttori", "toFixed", "label", "stato_bobina", "size", "onChange", "target", "placeholder", "justifyContent", "my", "component", "padding", "hover", "selected", "onClick", "cursor", "checked", "stopPropagation", "title", "overflow", "textOverflow", "whiteSpace", "type", "helperText", "FormHelperTextProps", "InputProps", "endAdornment", "fontSize", "stato_installazione", "colSpan", "align", "keys", "warning", "disabled", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/QuickAddCablesDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  TextField,\n  Checkbox,\n  FormControlLabel,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  CircularProgress,\n  Alert,\n  IconButton,\n  Chip,\n  Tooltip,\n  Tabs,\n  Tab,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Divider,\n  Card,\n  CardContent,\n  CardHeader,\n  Badge\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Delete as DeleteIcon,\n  Save as SaveIcon,\n  Info as InfoIcon,\n  Warning as WarningIcon,\n  Search as SearchIcon,\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n// Utility per verificare compatibilità tra cavo e bobina\nconst isCompatible = (cavo, bobina) => {\n  return cavo.tipologia === bobina.tipologia &&\n         String(cavo.sezione) === String(bobina.sezione);\n};\n\n// Utility per ottenere il numero della bobina dall'ID\nconst getBobinaNumber = (idBobina) => {\n  if (!idBobina) return '';\n  const parts = idBobina.split('-');\n  return parts.length > 1 ? parts[1] : idBobina;\n};\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * Implementa sistema di doppia lista per cavi compatibili/incompatibili\n * con gestione intelligente delle incompatibilità e dialog moderni\n */\nconst QuickAddCablesDialog = ({ open, onClose, bobina, cantiereId, onSuccess, onError }) => {\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per i dati separati\n  const [allCavi, setAllCavi] = useState([]);\n  const [caviCompatibili, setCaviCompatibili] = useState([]);\n  const [caviIncompatibili, setCaviIncompatibili] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n\n  // Stati per la UI\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);\n  const [incompatibleSelection, setIncompatibleSelection] = useState(null);\n  const [showOverDialog, setShowOverDialog] = useState(false);\n  const [overDialogData, setOverDialogData] = useState(null);\n\n  // Stati per la validazione\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina && cantiereId) {\n      loadCavi();\n      // Reset stati quando si apre il dialog\n      resetDialogState();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Reset dello stato del dialog\n  const resetDialogState = () => {\n    setSelectedCavi([]);\n    setCaviMetri({});\n    setErrors({});\n    setWarnings({});\n    setSearchTerm('');\n    setActiveTab(0);\n    setShowIncompatibleDialog(false);\n    setIncompatibleSelection(null);\n    setShowOverDialog(false);\n    setOverDialogData(null);\n  };\n\n  // Funzione per caricare i cavi con sistema di doppia lista\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi disponibili (non installati e non SPARE)\n      const caviDisponibili = caviData.filter(cavo =>\n        !isCableInstalled(cavo) &&\n        cavo.modificato_manualmente !== 3\n      );\n\n      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)\n      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));\n      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));\n\n      setAllCavi(caviDisponibili);\n      setCaviCompatibili(compatibili);\n      setCaviIncompatibili(incompatibili);\n\n      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione/deselezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n\n      if (isSelected) {\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = { ...caviMetri };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo\n  const validateMetri = (cavoId, value) => {\n    const cavo = cavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = { ...prev };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n\n    setWarnings(prev => {\n      const newWarnings = { ...prev };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore per i metri posati'\n      }));\n      return false;\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina\n    const metriTotaliRichiesti = Object.entries(caviMetri)\n      .filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n      .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m)`\n      }));\n    }\n\n    return true;\n  };\n\n  // Valida tutti i metri inseriti\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n        // Non mostrare popup di conferma, solo l'avviso nel form\n      }\n    }\n\n    // Verifica che i metri totali richiesti non superino i metri residui della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      // Questo è un avviso globale, non specifico per un cavo\n      if (!window.confirm(`ATTENZIONE: I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n        isValid = false;\n      }\n    }\n\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n    return isValid;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    try {\n      // Validazione\n      if (!validateAllMetri()) {\n        return;\n      }\n\n      setSaving(true);\n\n      // Conferma finale\n      if (!window.confirm(`Confermi l'aggiornamento di ${selectedCavi.length} cavi con la bobina ${bobina.id_bobina}?`)) {\n        setSaving(false);\n        return;\n      }\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const forceOver = (metriGiàUtilizzati + metriPosati) > bobina.metri_residui;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(\n            cantiereId,\n            cavo.id_cavo,\n            metriPosati,\n            bobina.id_bobina,\n            forceOver\n          );\n\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato\n      if (errors.length === 0) {\n        // Tutti i cavi sono stati aggiornati con successo\n        onSuccess(`${results.length} cavi aggiornati con successo`);\n        onClose();\n      } else if (results.length > 0) {\n        // Alcuni cavi sono stati aggiornati, altri no\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        // Nessun cavo è stato aggiornato\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filteredCavi = cavi.filter(cavo =>\n    cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase())) ||\n    (cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase())) ||\n    (cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"lg\" fullWidth>\n      <DialogTitle>\n        Aggiungi cavi alla bobina {bobina?.numero_bobina || ''}\n      </DialogTitle>\n      <DialogContent>\n        {!bobina ? (\n          <Alert severity=\"error\">Nessuna bobina selezionata</Alert>\n        ) : (\n          <>\n            {/* Informazioni sulla bobina */}\n            <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Dettagli bobina\n              </Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">ID Bobina</Typography>\n                  <Typography variant=\"body1\">{bobina.id_bobina}</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Tipologia</Typography>\n                  <Typography variant=\"body1\">{bobina.tipologia || 'N/A'}</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Conduttori</Typography>\n                  <Typography variant=\"body1\">{bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n                  <Typography variant=\"body1\">{bobina.metri_residui?.toFixed(1) || '0'} m</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Stato</Typography>\n                  <Chip\n                    label={bobina.stato_bobina || 'N/D'}\n                    size=\"small\"\n                    color={\n                      bobina.stato_bobina === 'Disponibile' ? 'success' :\n                      bobina.stato_bobina === 'In uso' ? 'primary' :\n                      bobina.stato_bobina === 'Over' ? 'error' :\n                      bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                    }\n                  />\n                </Box>\n              </Box>\n            </Box>\n\n            {/* Ricerca cavi */}\n            <TextField\n              fullWidth\n              label=\"Cerca cavi\"\n              variant=\"outlined\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"Cerca per ID, tipologia, ubicazione...\"\n              sx={{ mb: 2 }}\n            />\n\n            {/* Tabella cavi */}\n            {caviLoading ? (\n              <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n                <CircularProgress />\n              </Box>\n            ) : filteredCavi.length === 0 ? (\n              <Alert severity=\"info\">\n                Nessun cavo compatibile disponibile per questa bobina.\n              </Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mb: 3 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                      <TableCell padding=\"checkbox\"></TableCell>\n                      <TableCell>ID Cavo</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>Ubicazione</TableCell>\n                      <TableCell>Metri Teorici</TableCell>\n                      <TableCell>Metri Posati</TableCell>\n                      <TableCell>Stato</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {filteredCavi.map((cavo) => {\n                      const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n                      return (\n                        <TableRow\n                          key={cavo.id_cavo}\n                          hover\n                          selected={isSelected}\n                          onClick={() => handleCavoSelect(cavo)}\n                          sx={{ cursor: 'pointer' }}\n                        >\n                          <TableCell padding=\"checkbox\">\n                            <Checkbox\n                              checked={isSelected}\n                              onChange={(e) => {\n                                e.stopPropagation();\n                                handleCavoSelect(cavo);\n                              }}\n                            />\n                          </TableCell>\n                          <TableCell>{cavo.id_cavo}</TableCell>\n                          <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                          <TableCell>\n                            <Tooltip title={`Da: ${cavo.ubicazione_partenza || 'N/A'} - A: ${cavo.ubicazione_arrivo || 'N/A'}`}>\n                              <Box sx={{ maxWidth: 150, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>\n                                {cavo.ubicazione_partenza || 'N/A'} → {cavo.ubicazione_arrivo || 'N/A'}\n                              </Box>\n                            </Tooltip>\n                          </TableCell>\n                          <TableCell>{cavo.metri_teorici || 'N/A'}</TableCell>\n                          <TableCell>\n                            {isSelected ? (\n                              <TextField\n                                size=\"small\"\n                                type=\"number\"\n                                value={caviMetri[cavo.id_cavo] || ''}\n                                onChange={(e) => {\n                                  e.stopPropagation();\n                                  handleMetriChange(cavo.id_cavo, e.target.value);\n                                }}\n                                onClick={(e) => e.stopPropagation()}\n                                error={!!errors[cavo.id_cavo]}\n                                helperText={errors[cavo.id_cavo] || warnings[cavo.id_cavo]}\n                                FormHelperTextProps={{\n                                  sx: { color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main' }\n                                }}\n                                InputProps={{\n                                  endAdornment: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? (\n                                    <Tooltip title={warnings[cavo.id_cavo]}>\n                                      <WarningIcon color=\"warning\" fontSize=\"small\" />\n                                    </Tooltip>\n                                  ) : null\n                                }}\n                              />\n                            ) : (\n                              'N/A'\n                            )}\n                          </TableCell>\n                          <TableCell>\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione || 'Da installare'}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                            />\n                          </TableCell>\n                        </TableRow>\n                      );\n                    })}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n\n            {/* Riepilogo selezione */}\n            {selectedCavi.length > 0 && (\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Riepilogo selezione ({selectedCavi.length} cavi)\n                </Typography>\n                <TableContainer component={Paper}>\n                  <Table size=\"small\">\n                    <TableHead>\n                      <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                        <TableCell>ID Cavo</TableCell>\n                        <TableCell>Metri Posati</TableCell>\n                        <TableCell>Azioni</TableCell>\n                      </TableRow>\n                    </TableHead>\n                    <TableBody>\n                      {selectedCavi.map((cavo) => (\n                        <TableRow key={cavo.id_cavo}>\n                          <TableCell>{cavo.id_cavo}</TableCell>\n                          <TableCell>\n                            <TextField\n                              size=\"small\"\n                              type=\"number\"\n                              value={caviMetri[cavo.id_cavo] || ''}\n                              onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                              error={!!errors[cavo.id_cavo]}\n                              helperText={errors[cavo.id_cavo] || warnings[cavo.id_cavo]}\n                              FormHelperTextProps={{\n                                sx: { color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main' }\n                              }}\n                            />\n                          </TableCell>\n                          <TableCell>\n                            <IconButton\n                              size=\"small\"\n                              color=\"error\"\n                              onClick={() => handleCavoSelect(cavo)}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                      <TableRow>\n                        <TableCell colSpan={3} align=\"right\">\n                          <Typography variant=\"body2\">\n                            <strong>Metri totali richiesti:</strong> {\n                              Object.values(caviMetri).reduce((sum, metri) => {\n                                const value = parseFloat(metri || 0);\n                                return isNaN(value) ? sum : sum + value;\n                              }, 0).toFixed(1)\n                            } m\n                          </Typography>\n                          <Typography variant=\"body2\">\n                            <strong>Metri residui bobina:</strong> {bobina.metri_residui?.toFixed(1) || '0'} m\n                          </Typography>\n                        </TableCell>\n                      </TableRow>\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n              </Box>\n            )}\n\n            {/* Avvisi */}\n            {Object.keys(warnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle2\">Attenzione:</Typography>\n                <ul>\n                  {Object.entries(warnings).map(([cavoId, warning]) => (\n                    <li key={cavoId}>{cavoId}: {warning}</li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n\n            {/* Istruzioni */}\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Seleziona i cavi che vuoi associare a questa bobina e inserisci i metri posati per ciascuno.\n              I metri posati verranno sottratti dai metri residui della bobina.\n            </Alert>\n          </>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose} disabled={saving}>\n          Annulla\n        </Button>\n        <Button\n          onClick={handleSave}\n          color=\"primary\"\n          variant=\"contained\"\n          disabled={saving || selectedCavi.length === 0 || Object.keys(errors).length > 0}\n          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}\n        >\n          Salva\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default QuickAddCablesDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,gBAAgB,QAAQ,wBAAwB;AAClG,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK;EACrC,OAAOD,IAAI,CAACE,SAAS,KAAKD,MAAM,CAACC,SAAS,IACnCC,MAAM,CAACH,IAAI,CAACI,OAAO,CAAC,KAAKD,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC;AACxD,CAAC;;AAED;AACA,MAAMC,eAAe,GAAIC,QAAQ,IAAK;EACpC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;EACxB,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;EACjC,OAAOD,KAAK,CAACE,MAAM,GAAG,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAGD,QAAQ;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMI,oBAAoB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEX,MAAM;EAAEY,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC1F;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsF,MAAM,EAAEC,SAAS,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACwF,OAAO,EAAEC,UAAU,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8F,YAAY,EAAEC,eAAe,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgG,SAAS,EAAEC,YAAY,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACkG,SAAS,EAAEC,YAAY,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACoG,UAAU,EAAEC,aAAa,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACwG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAAC0G,cAAc,EAAEC,iBAAiB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4G,cAAc,EAAEC,iBAAiB,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM,CAAC8G,MAAM,EAAEC,SAAS,CAAC,GAAG/G,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACgH,QAAQ,EAAEC,WAAW,CAAC,GAAGjH,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIyE,IAAI,IAAIV,MAAM,IAAIY,UAAU,EAAE;MAChCsC,QAAQ,CAAC,CAAC;MACV;MACAC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACzC,IAAI,EAAEV,MAAM,EAAEY,UAAU,CAAC,CAAC;;EAE9B;EACA,MAAMuC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpB,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,CAAC,CAAC,CAAC;IAChBc,SAAS,CAAC,CAAC,CAAC,CAAC;IACbE,WAAW,CAAC,CAAC,CAAC,CAAC;IACfZ,aAAa,CAAC,EAAE,CAAC;IACjBF,YAAY,CAAC,CAAC,CAAC;IACfI,yBAAyB,CAAC,KAAK,CAAC;IAChCE,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMK,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF7B,cAAc,CAAC,IAAI,CAAC;MACpB,MAAM+B,QAAQ,GAAG,MAAM/D,WAAW,CAACgE,OAAO,CAACzC,UAAU,CAAC;;MAEtD;MACA,MAAM0C,eAAe,GAAGF,QAAQ,CAACG,MAAM,CAACxD,IAAI,IAC1C,CAACP,gBAAgB,CAACO,IAAI,CAAC,IACvBA,IAAI,CAACyD,sBAAsB,KAAK,CAClC,CAAC;;MAED;MACA,MAAMC,WAAW,GAAGH,eAAe,CAACC,MAAM,CAACxD,IAAI,IAAID,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;MAC9E,MAAM0D,aAAa,GAAGJ,eAAe,CAACC,MAAM,CAACxD,IAAI,IAAI,CAACD,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;MAEjFyB,UAAU,CAAC6B,eAAe,CAAC;MAC3B3B,kBAAkB,CAAC8B,WAAW,CAAC;MAC/B5B,oBAAoB,CAAC6B,aAAa,CAAC;MAEnCC,OAAO,CAACC,GAAG,CAAC,kBAAkBH,WAAW,CAACjD,MAAM,iBAAiBkD,aAAa,CAAClD,MAAM,gBAAgB,CAAC;IACxG,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD/C,OAAO,CAAC,mCAAmC,IAAI+C,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACRzC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM0C,gBAAgB,GAAIhE,IAAI,IAAK;IACjCgC,eAAe,CAACiC,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;MAE7D,IAAIH,UAAU,EAAE;QACd;QACA,MAAMI,WAAW,GAAGL,IAAI,CAACT,MAAM,CAACY,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;;QAEhE;QACA,MAAME,YAAY,GAAG;UAAE,GAAGtC;QAAU,CAAC;QACrC,OAAOsC,YAAY,CAACvE,IAAI,CAACqE,OAAO,CAAC;QACjCnC,YAAY,CAACqC,YAAY,CAAC;QAE1B,OAAOD,WAAW;MACpB,CAAC,MAAM;QACL;QACA,OAAO,CAAC,GAAGL,IAAI,EAAEjE,IAAI,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwE,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC3C;IACAxC,YAAY,CAAC+B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACQ,MAAM,GAAGC;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAC,aAAa,CAACF,MAAM,EAAEC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACF,MAAM,EAAEC,KAAK,KAAK;IACvC,MAAM1E,IAAI,GAAG4E,IAAI,CAACC,IAAI,CAACT,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKI,MAAM,CAAC;IACjD,IAAI,CAACzE,IAAI,EAAE;;IAEX;IACAgD,SAAS,CAACiB,IAAI,IAAI;MAChB,MAAMa,SAAS,GAAG;QAAE,GAAGb;MAAK,CAAC;MAC7B,OAAOa,SAAS,CAACL,MAAM,CAAC;MACxB,OAAOK,SAAS;IAClB,CAAC,CAAC;IAEF5B,WAAW,CAACe,IAAI,IAAI;MAClB,MAAMc,WAAW,GAAG;QAAE,GAAGd;MAAK,CAAC;MAC/B,OAAOc,WAAW,CAACN,MAAM,CAAC;MAC1B,OAAOM,WAAW;IACpB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjChC,SAAS,CAACiB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACQ,MAAM,GAAG;MACZ,CAAC,CAAC,CAAC;MACH,OAAO,KAAK;IACd;;IAEA;IACA,IAAIQ,KAAK,CAACC,UAAU,CAACR,KAAK,CAAC,CAAC,IAAIQ,UAAU,CAACR,KAAK,CAAC,IAAI,CAAC,EAAE;MACtD1B,SAAS,CAACiB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACQ,MAAM,GAAG;MACZ,CAAC,CAAC,CAAC;MACH,OAAO,KAAK;IACd;IAEA,MAAMU,WAAW,GAAGD,UAAU,CAACR,KAAK,CAAC;;IAErC;IACA,IAAI1E,IAAI,CAACoF,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAClF,IAAI,CAACoF,aAAa,CAAC,EAAE;MACtElC,WAAW,CAACe,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACQ,MAAM,GAAG,mBAAmBU,WAAW,yCAAyCnF,IAAI,CAACoF,aAAa;MACrG,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,MAAMC,oBAAoB,GAAGC,MAAM,CAACC,OAAO,CAACtD,SAAS,CAAC,CACnDuB,MAAM,CAAC,CAAC,CAACgC,EAAE,EAAEC,CAAC,CAAC,KAAKD,EAAE,KAAKf,MAAM,CAAC,CAAC;IAAA,CACnCiB,MAAM,CAAC,CAACC,GAAG,EAAE,CAACF,CAAC,EAAEG,KAAK,CAAC,KAAKD,GAAG,GAAGT,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGT,WAAW;IAE7E,IAAIE,oBAAoB,GAAGpF,MAAM,CAAC4F,aAAa,EAAE;MAC/C3C,WAAW,CAACe,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACQ,MAAM,GAAG,6BAA6BY,oBAAoB,6CAA6CpF,MAAM,CAAC4F,aAAa;MAC9H,CAAC,CAAC,CAAC;IACL;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMjB,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,WAAW,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAIhD,YAAY,CAACtB,MAAM,KAAK,CAAC,EAAE;MAC7BM,OAAO,CAAC,0BAA0B,CAAC;MACnC,OAAO,KAAK;IACd;;IAEA;IACA,KAAK,MAAMf,IAAI,IAAI+B,YAAY,EAAE;MAC/B,MAAM6D,KAAK,GAAG3D,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC;;MAErC;MACA,IAAI,CAACuB,KAAK,IAAIA,KAAK,CAACZ,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCF,SAAS,CAAC9E,IAAI,CAACqE,OAAO,CAAC,GAAG,uCAAuC;QACjE0B,OAAO,GAAG,KAAK;QACf;MACF;;MAEA;MACA,IAAId,KAAK,CAACC,UAAU,CAACU,KAAK,CAAC,CAAC,IAAIV,UAAU,CAACU,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDd,SAAS,CAAC9E,IAAI,CAACqE,OAAO,CAAC,GAAG,sCAAsC;QAChE0B,OAAO,GAAG,KAAK;QACf;MACF;MAEA,MAAMZ,WAAW,GAAGD,UAAU,CAACU,KAAK,CAAC;;MAErC;MACA,IAAI5F,IAAI,CAACoF,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAClF,IAAI,CAACoF,aAAa,CAAC,EAAE;QACtEL,WAAW,CAAC/E,IAAI,CAACqE,OAAO,CAAC,GAAG,mBAAmBc,WAAW,yCAAyCnF,IAAI,CAACoF,aAAa,IAAI;QACzH;MACF;IACF;;IAEA;IACA,MAAMC,oBAAoB,GAAGC,MAAM,CAACU,MAAM,CAAC/D,SAAS,CAAC,CAACyD,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGT,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7G,IAAIP,oBAAoB,GAAGpF,MAAM,CAAC4F,aAAa,EAAE;MAC/C;MACA,IAAI,CAACI,MAAM,CAACC,OAAO,CAAC,yCAAyCb,oBAAoB,6CAA6CpF,MAAM,CAAC4F,aAAa,oEAAoE,CAAC,EAAE;QACvNE,OAAO,GAAG,KAAK;MACjB;IACF;IAEA/C,SAAS,CAAC8B,SAAS,CAAC;IACpB5B,WAAW,CAAC6B,WAAW,CAAC;IACxB,OAAOgB,OAAO;EAChB,CAAC;;EAED;EACA,MAAMI,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,IAAI,CAACL,gBAAgB,CAAC,CAAC,EAAE;QACvB;MACF;MAEAtE,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,IAAI,CAACyE,MAAM,CAACC,OAAO,CAAC,+BAA+BnE,YAAY,CAACtB,MAAM,uBAAuBR,MAAM,CAACmG,SAAS,GAAG,CAAC,EAAE;QACjH5E,SAAS,CAAC,KAAK,CAAC;QAChB;MACF;;MAEA;MACA,MAAM6E,OAAO,GAAG,EAAE;MAClB,IAAItD,MAAM,GAAG,EAAE;MAEf,KAAK,MAAM/C,IAAI,IAAI+B,YAAY,EAAE;QAC/B,IAAI;UACF,MAAMoD,WAAW,GAAGD,UAAU,CAACjD,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,CAAC;;UAEvD;UACA,MAAMiC,kBAAkB,GAAGD,OAAO,CAACX,MAAM,CAAC,CAACC,GAAG,EAAEY,CAAC,KAAKZ,GAAG,GAAGY,CAAC,CAACpB,WAAW,EAAE,CAAC,CAAC;UAC7E,MAAMqB,SAAS,GAAIF,kBAAkB,GAAGnB,WAAW,GAAIlF,MAAM,CAAC4F,aAAa;;UAE3E;UACA,MAAMY,MAAM,GAAG,MAAMnH,WAAW,CAACoH,iBAAiB,CAChD7F,UAAU,EACVb,IAAI,CAACqE,OAAO,EACZc,WAAW,EACXlF,MAAM,CAACmG,SAAS,EAChBI,SACF,CAAC;UAEDH,OAAO,CAACM,IAAI,CAAC;YACX3G,IAAI,EAAEA,IAAI,CAACqE,OAAO;YAClBc,WAAW;YACXyB,OAAO,EAAE;UACX,CAAC,CAAC;QACJ,CAAC,CAAC,OAAO9C,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,sCAAsC9D,IAAI,CAACqE,OAAO,GAAG,EAAEP,KAAK,CAAC;UAC3Ef,MAAM,CAAC4D,IAAI,CAAC;YACV3G,IAAI,EAAEA,IAAI,CAACqE,OAAO;YAClBP,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;UAC1B,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAIhB,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE;QACvB;QACAK,SAAS,CAAC,GAAGuF,OAAO,CAAC5F,MAAM,+BAA+B,CAAC;QAC3DG,OAAO,CAAC,CAAC;MACX,CAAC,MAAM,IAAIyF,OAAO,CAAC5F,MAAM,GAAG,CAAC,EAAE;QAC7B;QACAK,SAAS,CAAC,GAAGuF,OAAO,CAAC5F,MAAM,kCAAkCsC,MAAM,CAACtC,MAAM,UAAU,CAAC;QACrFM,OAAO,CAAC,WAAWgC,MAAM,CAAC8D,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAAC9G,IAAI,KAAK8G,CAAC,CAAChD,KAAK,EAAE,CAAC,CAACiD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACzEnG,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL;QACAG,OAAO,CAAC,mCAAmCgC,MAAM,CAAC8D,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAAC9G,IAAI,KAAK8G,CAAC,CAAChD,KAAK,EAAE,CAAC,CAACiD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MACnG;IACF,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD/C,OAAO,CAAC,iCAAiC,IAAI+C,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRvC,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMwF,YAAY,GAAGpC,IAAI,CAACpB,MAAM,CAACxD,IAAI,IACnCA,IAAI,CAACqE,OAAO,CAAC4C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7E,UAAU,CAAC4E,WAAW,CAAC,CAAC,CAAC,IAC5DjH,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACE,SAAS,CAAC+G,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7E,UAAU,CAAC4E,WAAW,CAAC,CAAC,CAAE,IAClFjH,IAAI,CAACmH,mBAAmB,IAAInH,IAAI,CAACmH,mBAAmB,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7E,UAAU,CAAC4E,WAAW,CAAC,CAAC,CAAE,IACtGjH,IAAI,CAACoH,iBAAiB,IAAIpH,IAAI,CAACoH,iBAAiB,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7E,UAAU,CAAC4E,WAAW,CAAC,CAAC,CACnG,CAAC;EAED,oBACErH,OAAA,CAACzD,MAAM;IAACwE,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAACyG,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3D3H,OAAA,CAACxD,WAAW;MAAAmL,QAAA,GAAC,4BACe,EAAC,CAAAtH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuH,aAAa,KAAI,EAAE;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eACdhI,OAAA,CAACvD,aAAa;MAAAkL,QAAA,EACX,CAACtH,MAAM,gBACNL,OAAA,CAACvC,KAAK;QAACwK,QAAQ,EAAC,OAAO;QAAAN,QAAA,EAAC;MAA0B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,gBAE1DhI,OAAA,CAAAE,SAAA;QAAAyH,QAAA,gBAEE3H,OAAA,CAACnD,GAAG;UAACqL,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAX,QAAA,gBAC5D3H,OAAA,CAACpD,UAAU;YAAC2L,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAb,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhI,OAAA,CAACnD,GAAG;YAACqL,EAAE,EAAE;cAAEO,OAAO,EAAE,MAAM;cAAEC,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAhB,QAAA,gBACrD3H,OAAA,CAACnD,GAAG;cAAA8K,QAAA,gBACF3H,OAAA,CAACpD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzEhI,OAAA,CAACpD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAZ,QAAA,EAAEtH,MAAM,CAACmG;cAAS;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNhI,OAAA,CAACnD,GAAG;cAAA8K,QAAA,gBACF3H,OAAA,CAACpD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzEhI,OAAA,CAACpD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAZ,QAAA,EAAEtH,MAAM,CAACC,SAAS,IAAI;cAAK;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACNhI,OAAA,CAACnD,GAAG;cAAA8K,QAAA,gBACF3H,OAAA,CAACpD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1EhI,OAAA,CAACpD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAZ,QAAA,GAAEtH,MAAM,CAACwI,YAAY,IAAI,KAAK,EAAC,KAAG,EAACxI,MAAM,CAACG,OAAO,IAAI,KAAK;cAAA;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC,eACNhI,OAAA,CAACnD,GAAG;cAAA8K,QAAA,gBACF3H,OAAA,CAACpD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7EhI,OAAA,CAACpD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAZ,QAAA,GAAE,EAAAtG,qBAAA,GAAAhB,MAAM,CAAC4F,aAAa,cAAA5E,qBAAA,uBAApBA,qBAAA,CAAsByH,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,IAAE;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,eACNhI,OAAA,CAACnD,GAAG;cAAA8K,QAAA,gBACF3H,OAAA,CAACpD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrEhI,OAAA,CAACrC,IAAI;gBACHoL,KAAK,EAAE1I,MAAM,CAAC2I,YAAY,IAAI,KAAM;gBACpCC,IAAI,EAAC,OAAO;gBACZL,KAAK,EACHvI,MAAM,CAAC2I,YAAY,KAAK,aAAa,GAAG,SAAS,GACjD3I,MAAM,CAAC2I,YAAY,KAAK,QAAQ,GAAG,SAAS,GAC5C3I,MAAM,CAAC2I,YAAY,KAAK,MAAM,GAAG,OAAO,GACxC3I,MAAM,CAAC2I,YAAY,KAAK,WAAW,GAAG,SAAS,GAAG;cACnD;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhI,OAAA,CAAClD,SAAS;UACR4K,SAAS;UACTqB,KAAK,EAAC,YAAY;UAClBR,OAAO,EAAC,UAAU;UAClBzD,KAAK,EAAErC,UAAW;UAClByG,QAAQ,EAAGhC,CAAC,IAAKxE,aAAa,CAACwE,CAAC,CAACiC,MAAM,CAACrE,KAAK,CAAE;UAC/CsE,WAAW,EAAC,wCAAwC;UACpDlB,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EAGDvG,WAAW,gBACVzB,OAAA,CAACnD,GAAG;UAACqL,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEY,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAC5D3H,OAAA,CAACxC,gBAAgB;YAAAqK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJZ,YAAY,CAACvG,MAAM,KAAK,CAAC,gBAC3Bb,OAAA,CAACvC,KAAK;UAACwK,QAAQ,EAAC,MAAM;UAAAN,QAAA,EAAC;QAEvB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERhI,OAAA,CAAC5C,cAAc;UAACmM,SAAS,EAAEhM,KAAM;UAAC2K,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,eAC9C3H,OAAA,CAAC/C,KAAK;YAACgM,IAAI,EAAC,OAAO;YAAAtB,QAAA,gBACjB3H,OAAA,CAAC3C,SAAS;cAAAsK,QAAA,eACR3H,OAAA,CAAC1C,QAAQ;gBAAC4K,EAAE,EAAE;kBAAEG,OAAO,EAAE;gBAAU,CAAE;gBAAAV,QAAA,gBACnC3H,OAAA,CAAC7C,SAAS;kBAACqM,OAAO,EAAC;gBAAU;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1ChI,OAAA,CAAC7C,SAAS;kBAAAwK,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BhI,OAAA,CAAC7C,SAAS;kBAAAwK,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChChI,OAAA,CAAC7C,SAAS;kBAAAwK,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjChI,OAAA,CAAC7C,SAAS;kBAAAwK,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpChI,OAAA,CAAC7C,SAAS;kBAAAwK,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnChI,OAAA,CAAC7C,SAAS;kBAAAwK,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZhI,OAAA,CAAC9C,SAAS;cAAAyK,QAAA,EACPP,YAAY,CAACH,GAAG,CAAE7G,IAAI,IAAK;gBAC1B,MAAMkE,UAAU,GAAGnC,YAAY,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;gBACrE,oBACEzE,OAAA,CAAC1C,QAAQ;kBAEPmM,KAAK;kBACLC,QAAQ,EAAEpF,UAAW;kBACrBqF,OAAO,EAAEA,CAAA,KAAMvF,gBAAgB,CAAChE,IAAI,CAAE;kBACtC8H,EAAE,EAAE;oBAAE0B,MAAM,EAAE;kBAAU,CAAE;kBAAAjC,QAAA,gBAE1B3H,OAAA,CAAC7C,SAAS;oBAACqM,OAAO,EAAC,UAAU;oBAAA7B,QAAA,eAC3B3H,OAAA,CAACjD,QAAQ;sBACP8M,OAAO,EAAEvF,UAAW;sBACpB4E,QAAQ,EAAGhC,CAAC,IAAK;wBACfA,CAAC,CAAC4C,eAAe,CAAC,CAAC;wBACnB1F,gBAAgB,CAAChE,IAAI,CAAC;sBACxB;oBAAE;sBAAAyH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZhI,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,EAAEvH,IAAI,CAACqE;kBAAO;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrChI,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,EAAEvH,IAAI,CAACE,SAAS,IAAI;kBAAK;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChDhI,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,eACR3H,OAAA,CAACpC,OAAO;sBAACmM,KAAK,EAAE,OAAO3J,IAAI,CAACmH,mBAAmB,IAAI,KAAK,SAASnH,IAAI,CAACoH,iBAAiB,IAAI,KAAK,EAAG;sBAAAG,QAAA,eACjG3H,OAAA,CAACnD,GAAG;wBAACqL,EAAE,EAAE;0BAAET,QAAQ,EAAE,GAAG;0BAAEuC,QAAQ,EAAE,QAAQ;0BAAEC,YAAY,EAAE,UAAU;0BAAEC,UAAU,EAAE;wBAAS,CAAE;wBAAAvC,QAAA,GAC5FvH,IAAI,CAACmH,mBAAmB,IAAI,KAAK,EAAC,UAAG,EAACnH,IAAI,CAACoH,iBAAiB,IAAI,KAAK;sBAAA;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACZhI,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,EAAEvH,IAAI,CAACoF,aAAa,IAAI;kBAAK;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpDhI,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,EACPrD,UAAU,gBACTtE,OAAA,CAAClD,SAAS;sBACRmM,IAAI,EAAC,OAAO;sBACZkB,IAAI,EAAC,QAAQ;sBACbrF,KAAK,EAAEzC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAI,EAAG;sBACrCyE,QAAQ,EAAGhC,CAAC,IAAK;wBACfA,CAAC,CAAC4C,eAAe,CAAC,CAAC;wBACnBlF,iBAAiB,CAACxE,IAAI,CAACqE,OAAO,EAAEyC,CAAC,CAACiC,MAAM,CAACrE,KAAK,CAAC;sBACjD,CAAE;sBACF6E,OAAO,EAAGzC,CAAC,IAAKA,CAAC,CAAC4C,eAAe,CAAC,CAAE;sBACpC5F,KAAK,EAAE,CAAC,CAACf,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAE;sBAC9B2F,UAAU,EAAEjH,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,IAAIpB,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAE;sBAC3D4F,mBAAmB,EAAE;wBACnBnC,EAAE,EAAE;0BAAEU,KAAK,EAAEvF,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC,IAAI,CAACtB,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,GAAG,cAAc,GAAG;wBAAa;sBAC/F,CAAE;sBACF6F,UAAU,EAAE;wBACVC,YAAY,EAAElH,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC,IAAI,CAACtB,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,gBAC3DzE,OAAA,CAACpC,OAAO;0BAACmM,KAAK,EAAE1G,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAE;0BAAAkD,QAAA,eACrC3H,OAAA,CAACf,WAAW;4BAAC2J,KAAK,EAAC,SAAS;4BAAC4B,QAAQ,EAAC;0BAAO;4BAAA3C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,GACR;sBACN;oBAAE;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,GAEF;kBACD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC,eACZhI,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,eACR3H,OAAA,CAACrC,IAAI;sBACHsL,IAAI,EAAC,OAAO;sBACZF,KAAK,EAAE3I,IAAI,CAACqK,mBAAmB,IAAI,eAAgB;sBACnD7B,KAAK,EAAEhJ,kBAAkB,CAACQ,IAAI,CAACqK,mBAAmB;oBAAE;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC;gBAAA,GA3DP5H,IAAI,CAACqE,OAAO;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4DT,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB,EAGA7F,YAAY,CAACtB,MAAM,GAAG,CAAC,iBACtBb,OAAA,CAACnD,GAAG;UAACqL,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACjB3H,OAAA,CAACpD,UAAU;YAAC2L,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAb,QAAA,GAAC,uBACtB,EAACxF,YAAY,CAACtB,MAAM,EAAC,QAC5C;UAAA;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhI,OAAA,CAAC5C,cAAc;YAACmM,SAAS,EAAEhM,KAAM;YAAAoK,QAAA,eAC/B3H,OAAA,CAAC/C,KAAK;cAACgM,IAAI,EAAC,OAAO;cAAAtB,QAAA,gBACjB3H,OAAA,CAAC3C,SAAS;gBAAAsK,QAAA,eACR3H,OAAA,CAAC1C,QAAQ;kBAAC4K,EAAE,EAAE;oBAAEG,OAAO,EAAE;kBAAU,CAAE;kBAAAV,QAAA,gBACnC3H,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BhI,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnChI,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZhI,OAAA,CAAC9C,SAAS;gBAAAyK,QAAA,GACPxF,YAAY,CAAC8E,GAAG,CAAE7G,IAAI,iBACrBJ,OAAA,CAAC1C,QAAQ;kBAAAqK,QAAA,gBACP3H,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,EAAEvH,IAAI,CAACqE;kBAAO;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrChI,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,eACR3H,OAAA,CAAClD,SAAS;sBACRmM,IAAI,EAAC,OAAO;sBACZkB,IAAI,EAAC,QAAQ;sBACbrF,KAAK,EAAEzC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAI,EAAG;sBACrCyE,QAAQ,EAAGhC,CAAC,IAAKtC,iBAAiB,CAACxE,IAAI,CAACqE,OAAO,EAAEyC,CAAC,CAACiC,MAAM,CAACrE,KAAK,CAAE;sBACjEZ,KAAK,EAAE,CAAC,CAACf,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAE;sBAC9B2F,UAAU,EAAEjH,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,IAAIpB,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAE;sBAC3D4F,mBAAmB,EAAE;wBACnBnC,EAAE,EAAE;0BAAEU,KAAK,EAAEvF,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC,IAAI,CAACtB,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,GAAG,cAAc,GAAG;wBAAa;sBAC/F;oBAAE;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZhI,OAAA,CAAC7C,SAAS;oBAAAwK,QAAA,eACR3H,OAAA,CAACtC,UAAU;sBACTuL,IAAI,EAAC,OAAO;sBACZL,KAAK,EAAC,OAAO;sBACbe,OAAO,EAAEA,CAAA,KAAMvF,gBAAgB,CAAChE,IAAI,CAAE;sBAAAuH,QAAA,eAEtC3H,OAAA,CAACrB,UAAU;wBAAC6L,QAAQ,EAAC;sBAAO;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAvBC5H,IAAI,CAACqE,OAAO;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwBjB,CACX,CAAC,eACFhI,OAAA,CAAC1C,QAAQ;kBAAAqK,QAAA,eACP3H,OAAA,CAAC7C,SAAS;oBAACuN,OAAO,EAAE,CAAE;oBAACC,KAAK,EAAC,OAAO;oBAAAhD,QAAA,gBAClC3H,OAAA,CAACpD,UAAU;sBAAC2L,OAAO,EAAC,OAAO;sBAAAZ,QAAA,gBACzB3H,OAAA;wBAAA2H,QAAA,EAAQ;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EACvCtC,MAAM,CAACU,MAAM,CAAC/D,SAAS,CAAC,CAACyD,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;wBAC9C,MAAMlB,KAAK,GAAGQ,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC;wBACpC,OAAOX,KAAK,CAACP,KAAK,CAAC,GAAGiB,GAAG,GAAGA,GAAG,GAAGjB,KAAK;sBACzC,CAAC,EAAE,CAAC,CAAC,CAACgE,OAAO,CAAC,CAAC,CAAC,EACjB,IACH;oBAAA;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbhI,OAAA,CAACpD,UAAU;sBAAC2L,OAAO,EAAC,OAAO;sBAAAZ,QAAA,gBACzB3H,OAAA;wBAAA2H,QAAA,EAAQ;sBAAqB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA1G,sBAAA,GAAAjB,MAAM,CAAC4F,aAAa,cAAA3E,sBAAA,uBAApBA,sBAAA,CAAsBwH,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,IAClF;oBAAA;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACN,EAGAtC,MAAM,CAACkF,IAAI,CAACvH,QAAQ,CAAC,CAACxC,MAAM,GAAG,CAAC,iBAC/Bb,OAAA,CAACvC,KAAK;UAACwK,QAAQ,EAAC,SAAS;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACtC3H,OAAA,CAACpD,UAAU;YAAC2L,OAAO,EAAC,WAAW;YAAAZ,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxDhI,OAAA;YAAA2H,QAAA,EACGjC,MAAM,CAACC,OAAO,CAACtC,QAAQ,CAAC,CAAC4D,GAAG,CAAC,CAAC,CAACpC,MAAM,EAAEgG,OAAO,CAAC,kBAC9C7K,OAAA;cAAA2H,QAAA,GAAkB9C,MAAM,EAAC,IAAE,EAACgG,OAAO;YAAA,GAA1BhG,MAAM;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAyB,CACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR,eAGDhI,OAAA,CAACvC,KAAK;UAACwK,QAAQ,EAAC,MAAM;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EAAC;QAGtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,eACR;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAChBhI,OAAA,CAACtD,aAAa;MAAAiL,QAAA,gBACZ3H,OAAA,CAACrD,MAAM;QAACgN,OAAO,EAAE3I,OAAQ;QAAC8J,QAAQ,EAAEnJ,MAAO;QAAAgG,QAAA,EAAC;MAE5C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThI,OAAA,CAACrD,MAAM;QACLgN,OAAO,EAAEpD,UAAW;QACpBqC,KAAK,EAAC,SAAS;QACfL,OAAO,EAAC,WAAW;QACnBuC,QAAQ,EAAEnJ,MAAM,IAAIQ,YAAY,CAACtB,MAAM,KAAK,CAAC,IAAI6E,MAAM,CAACkF,IAAI,CAACzH,MAAM,CAAC,CAACtC,MAAM,GAAG,CAAE;QAChFkK,SAAS,EAAEpJ,MAAM,gBAAG3B,OAAA,CAACxC,gBAAgB;UAACyL,IAAI,EAAE;QAAG;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGhI,OAAA,CAACnB,QAAQ;UAAAgJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,EACnE;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAC5G,EAAA,CAjjBIN,oBAAoB;AAAAkK,EAAA,GAApBlK,oBAAoB;AAmjB1B,eAAeA,oBAAoB;AAAC,IAAAkK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}