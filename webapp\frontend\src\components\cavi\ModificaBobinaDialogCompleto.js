import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Radio,
  RadioGroup,
  IconButton,
  Divider,
  InputAdornment,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Tooltip,
  Stack
} from '@mui/material';
import {
  Search as SearchIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Cable as CableIcon,
  Settings as SettingsIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import caviService from '../../services/caviService';
import parcoCaviService from '../../services/parcoCaviService';
import CavoDetailsView from './CavoDetailsView';
import IncompatibleReelDialog from './IncompatibleReelDialog';
import {
  CABLE_STATES,
  REEL_STATES,
  determineCableState,
  determineReelState,
  canModifyCable,
  isCableSpare,
  isCableInstalled,
  getCableStateColor,
  getReelStateColor
} from '../../utils/stateUtils';

/**
 * Dialog completo per la modifica della bobina di un cavo installato
 * Integra tutta la logica del ModificaBobinaForm esistente
 */
const ModificaBobinaDialogCompleto = ({ 
  open, 
  onClose, 
  cavo: cavoPreselezionato, 
  cantiereId, 
  onSuccess, 
  onError 
}) => {
  // Stati principali
  const [loading, setLoading] = useState(false);
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [selectedOption, setSelectedOption] = useState('');
  const [selectedBobinaId, setSelectedBobinaId] = useState('');
  
  // Stati per bobine
  const [bobine, setBobine] = useState([]);
  const [searchText, setSearchText] = useState('');
  
  // Stati per dialoghi
  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);
  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);
  const [incompatibleReel, setIncompatibleReel] = useState(null);
  const [confirmationDialog, setConfirmationDialog] = useState({ open: false, message: '', action: null });

  // Effetto per preselezionare il cavo quando il dialog si apre
  useEffect(() => {
    if (open && cavoPreselezionato) {
      console.log('Preselezionando cavo:', cavoPreselezionato);
      setSelectedCavo(cavoPreselezionato);
      loadBobine();
    }
  }, [open, cavoPreselezionato]);

  // Reset quando il dialog si chiude
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  const resetForm = () => {
    setSelectedCavo(null);
    setSelectedOption('');
    setSelectedBobinaId('');
    setBobine([]);
    setSearchText('');
    setShowCavoDetailsDialog(false);
    setShowIncompatibleReelDialog(false);
    setIncompatibleReel(null);
    setConfirmationDialog({ open: false, message: '', action: null });
  };

  // Carica le bobine disponibili
  const loadBobine = async () => {
    try {
      setLoading(true);
      const bobineData = await parcoCaviService.getBobine(cantiereId);
      setBobine(bobineData || []);
    } catch (error) {
      console.error('Errore nel caricamento delle bobine:', error);
      onError?.('Errore nel caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  // Gestisce il cambio di opzione
  const handleOptionChange = (event) => {
    setSelectedOption(event.target.value);
    setSelectedBobinaId('');
  };

  // Gestisce il cambio del testo di ricerca
  const handleSearchTextChange = (event) => {
    setSearchText(event.target.value);
  };

  // Ottiene il numero della bobina per la visualizzazione
  const getBobinaNumber = (idBobina) => {
    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA';
    const bobina = bobine.find(b => b.id_bobina === idBobina);
    return bobina ? bobina.numero_bobina || idBobina : idBobina;
  };

  // Filtra le bobine compatibili
  const getBobineCompatibili = () => {
    if (!selectedCavo) return [];
    
    return bobine.filter(bobina => {
      const isCompatible = bobina.tipologia === selectedCavo.tipologia && 
                          bobina.sezione === selectedCavo.sezione;
      const matchesSearch = searchText === '' || 
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||
                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||
                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()));
      return isCompatible && matchesSearch && bobina.metri_residui > 0;
    });
  };

  // Filtra le bobine incompatibili
  const getBobineIncompatibili = () => {
    if (!selectedCavo) return [];
    
    return bobine.filter(bobina => {
      const isIncompatible = bobina.tipologia !== selectedCavo.tipologia || 
                            bobina.sezione !== selectedCavo.sezione;
      const matchesSearch = searchText === '' || 
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||
                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||
                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()));
      return isIncompatible && matchesSearch && bobina.metri_residui > 0;
    });
  };

  // Gestisce la selezione di una bobina compatibile
  const handleSelectBobinaCompatibile = (bobina) => {
    console.log('Bobina compatibile selezionata:', bobina);
    setSelectedBobinaId(bobina.id_bobina);
  };

  // Gestisce la selezione di una bobina incompatibile
  const handleSelectBobinaIncompatibile = (bobina) => {
    console.log('Bobina incompatibile selezionata:', bobina);
    setIncompatibleReel(bobina);
    setShowIncompatibleReelDialog(true);
  };

  // Gestisce l'uso di una bobina incompatibile
  const handleUseIncompatibleReel = async (bobina, cavo) => {
    try {
      setLoading(true);
      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);
      
      // Mantieni la bobina selezionata
      setSelectedBobinaId(bobina.id_bobina);
      
      onSuccess?.(`Bobina incompatibile ${getBobinaNumber(bobina.id_bobina)} selezionata per il cavo ${cavo.id_cavo}`);
      setShowIncompatibleReelDialog(false);
    } catch (error) {
      console.error('Errore durante la selezione della bobina incompatibile:', error);
      onError?.('Errore durante la selezione della bobina incompatibile: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la chiusura del dialog bobina incompatibile
  const handleCloseIncompatibleReelDialog = () => {
    setShowIncompatibleReelDialog(false);
    setIncompatibleReel(null);
  };

  // Gestisce la conferma dell'azione
  const handleConfirmAction = () => {
    let message = '';
    let action = null;

    if (selectedOption === 'assegnaNuova') {
      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);
      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;
      action = () => updateBobina(selectedBobinaId);
    } else if (selectedOption === 'rimuoviBobina') {
      message = `Sei sicuro di voler rimuovere la bobina attuale dal cavo ${selectedCavo.id_cavo}?`;
      action = () => updateBobina('BOBINA_VUOTA');
    } else if (selectedOption === 'annullaInstallazione') {
      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;
      action = () => annullaInstallazione();
    }

    setConfirmationDialog({ open: true, message, action });
  };

  // Funzione per aggiornare la bobina di un cavo
  const updateBobina = async (bobinaId) => {
    try {
      setLoading(true);
      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);

      onSuccess?.(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);
      
      // Chiudi il dialog
      onClose();
    } catch (error) {
      console.error('Errore durante l\'aggiornamento della bobina:', error);
      onError?.('Errore durante l\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
      setConfirmationDialog({ open: false, message: '', action: null });
    }
  };

  // Funzione per annullare l'installazione
  const annullaInstallazione = async () => {
    try {
      setLoading(true);
      await caviService.annullaInstallazione(cantiereId, selectedCavo.id_cavo);

      onSuccess?.(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);
      
      // Chiudi il dialog
      onClose();
    } catch (error) {
      console.error('Errore durante l\'annullamento dell\'installazione:', error);
      onError?.('Errore durante l\'annullamento dell\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
      setConfirmationDialog({ open: false, message: '', action: null });
    }
  };

  if (!selectedCavo) {
    return null;
  }

  return (
    <>
      <Dialog 
        open={open} 
        onClose={onClose} 
        maxWidth="lg" 
        fullWidth
        PaperProps={{
          sx: { minHeight: '70vh' }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          pb: 1
        }}>
          <Typography variant="h6" sx={{ fontSize: '1.1rem' }}>
            Modifica Bobina Cavo {selectedCavo.id_cavo}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        
        <DialogContent dividers>
          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          )}

          {!loading && (
            <Box>
              {/* Informazioni del cavo selezionato */}
              <Paper sx={{ p: 3, mb: 3, backgroundColor: '#f8f9fa' }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <CableIcon sx={{ mr: 1 }} />
                  Cavo Selezionato
                </Typography>

                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>ID:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>{selectedCavo.id_cavo}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipologia:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Formazione:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Metri:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>{selectedCavo.metratura_reale || 'N/A'} m</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Bobina:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>
                      {selectedCavo.id_bobina ? (selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedCavo.id_bobina)) : 'VUOTA'}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>
                    <Chip
                      size="small"
                      label={selectedCavo.stato_installazione || 'N/D'}
                      color="success"
                      sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}
                    />
                  </Box>
                  <IconButton
                    size="small"
                    onClick={() => setShowCavoDetailsDialog(true)}
                    title="Visualizza dettagli cavo"
                  >
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Paper>

              {/* Opzioni di modifica */}
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Opzioni di modifica
                </Typography>

                <RadioGroup
                  value={selectedOption}
                  onChange={handleOptionChange}
                >
                  <FormControlLabel
                    value="assegnaNuova"
                    control={<Radio />}
                    label="Assegna nuova bobina"
                  />
                  <FormControlLabel
                    value="rimuoviBobina"
                    control={<Radio />}
                    label="Rimuovi bobina attuale"
                  />
                  <FormControlLabel
                    value="annullaInstallazione"
                    control={<Radio />}
                    label="Annulla installazione"
                  />
                </RadioGroup>
              </Paper>

              {/* Sezione selezione bobina - solo se opzione "assegnaNuova" */}
              {selectedOption === 'assegnaNuova' && (
                <Paper sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Seleziona bobina
                  </Typography>

                  {/* Campo di ricerca */}
                  <TextField
                    fullWidth
                    variant="outlined"
                    placeholder="Cerca bobina per ID, tipologia o numero..."
                    value={searchText}
                    onChange={handleSearchTextChange}
                    sx={{ mb: 3 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <Grid container spacing={3}>
                    {/* Bobine compatibili */}
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1" gutterBottom sx={{ color: 'success.main', fontWeight: 'medium' }}>
                        <CheckCircleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                        Bobine Compatibili ({getBobineCompatibili().length})
                      </Typography>

                      <Box sx={{ maxHeight: 300, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>
                        {getBobineCompatibili().length === 0 ? (
                          <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                            Nessuna bobina compatibile trovata
                          </Box>
                        ) : (
                          <List dense>
                            {getBobineCompatibili().map((bobina) => (
                              <ListItem
                                key={bobina.id_bobina}
                                disablePadding
                              >
                                <ListItemButton
                                  selected={selectedBobinaId === bobina.id_bobina}
                                  onClick={() => handleSelectBobinaCompatibile(bobina)}
                                  sx={{
                                    py: 1.5,
                                    '&.Mui-selected': {
                                      backgroundColor: 'rgba(76, 175, 80, 0.1)',
                                      '&:hover': {
                                        backgroundColor: 'rgba(76, 175, 80, 0.2)',
                                      },
                                    },
                                  }}
                                >
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1, minWidth: 0 }}>
                                      <Typography variant="body1" fontWeight="bold" sx={{ fontSize: '1rem', minWidth: 'fit-content' }}>
                                        {bobina.numero_bobina || bobina.id_bobina}
                                      </Typography>
                                      <Typography variant="body2" sx={{ fontSize: '0.95rem', color: 'text.secondary', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                        {bobina.tipologia} - {bobina.sezione}
                                      </Typography>
                                    </Box>
                                    <Chip
                                      size="small"
                                      label={`${bobina.metri_residui}m`}
                                      color="success"
                                      variant="outlined"
                                      sx={{ fontSize: '0.85rem', fontWeight: 'medium', minWidth: 'fit-content' }}
                                    />
                                  </Box>
                                </ListItemButton>
                              </ListItem>
                            ))}
                          </List>
                        )}
                      </Box>
                    </Grid>

                    {/* Bobine incompatibili */}
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1" gutterBottom sx={{ color: 'warning.main', fontWeight: 'medium' }}>
                        <WarningIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                        Bobine Incompatibili ({getBobineIncompatibili().length})
                      </Typography>

                      <Box sx={{ maxHeight: 300, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>
                        {getBobineIncompatibili().length === 0 ? (
                          <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                            Nessuna bobina incompatibile trovata
                          </Box>
                        ) : (
                          <List dense>
                            {getBobineIncompatibili().map((bobina) => (
                              <ListItem
                                key={bobina.id_bobina}
                                disablePadding
                              >
                                <ListItemButton
                                  onClick={() => handleSelectBobinaIncompatibile(bobina)}
                                  sx={{
                                    py: 1.5,
                                    '&:hover': {
                                      backgroundColor: 'rgba(255, 152, 0, 0.1)',
                                    },
                                  }}
                                >
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1, minWidth: 0 }}>
                                      <Typography variant="body1" fontWeight="bold" sx={{ fontSize: '1rem', minWidth: 'fit-content' }}>
                                        {bobina.numero_bobina || bobina.id_bobina}
                                      </Typography>
                                      <Typography variant="body2" sx={{ fontSize: '0.95rem', color: 'text.secondary', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                        {bobina.tipologia} - {bobina.sezione}
                                      </Typography>
                                    </Box>
                                    <Chip
                                      size="small"
                                      label={`${bobina.metri_residui}m`}
                                      color="warning"
                                      variant="outlined"
                                      sx={{ fontSize: '0.85rem', fontWeight: 'medium', minWidth: 'fit-content' }}
                                    />
                                  </Box>
                                </ListItemButton>
                              </ListItem>
                            ))}
                          </List>
                        )}
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              )}
            </Box>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 2 }}>
          <Button onClick={onClose} disabled={loading}>
            Annulla
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirmAction}
            disabled={loading || !selectedOption || (selectedOption === 'assegnaNuova' && !selectedBobinaId)}
          >
            {loading ? <CircularProgress size={20} /> : 'Conferma'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog dettagli cavo */}
      <Dialog
        open={showCavoDetailsDialog}
        onClose={() => setShowCavoDetailsDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Dettagli Cavo {selectedCavo?.id_cavo}</DialogTitle>
        <DialogContent>
          <CavoDetailsView cavo={selectedCavo} />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCavoDetailsDialog(false)}>Chiudi</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog bobina incompatibile */}
      <IncompatibleReelDialog
        open={showIncompatibleReelDialog}
        onClose={handleCloseIncompatibleReelDialog}
        bobina={incompatibleReel}
        cavo={selectedCavo}
        onConfirm={handleUseIncompatibleReel}
      />

      {/* Dialog di conferma */}
      <Dialog
        open={confirmationDialog.open}
        onClose={() => setConfirmationDialog({ open: false, message: '', action: null })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Conferma Operazione</DialogTitle>
        <DialogContent>
          <Typography>{confirmationDialog.message}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmationDialog({ open: false, message: '', action: null })}>
            Annulla
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              if (confirmationDialog.action) {
                confirmationDialog.action();
              }
            }}
          >
            Conferma
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ModificaBobinaDialogCompleto;
