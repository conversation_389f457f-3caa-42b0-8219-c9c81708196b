import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  ComposedChart,
  Area,
  AreaChart
} from 'recharts';
import { Box, Typography, Grid, Paper, Chip } from '@mui/material';

const COLORS = {
  primary: '#1976d2',
  secondary: '#dc004e',
  success: '#2e7d32',
  warning: '#ed6c02',
  info: '#0288d1',
  error: '#d32f2f'
};

const TimelineChart = ({ data }) => {
  if (!data) return null;

  // Prepara dati per il grafico temporale
  const timelineData = data.posa_giornaliera?.map(posa => ({
    data: posa.data,
    metri: posa.metri,
    data_formatted: new Date(posa.data).toLocaleDateString('it-IT', { 
      day: '2-digit', 
      month: '2-digit' 
    })
  })) || [];

  // Calcola media mobile (7 giorni)
  const timelineWithMovingAverage = timelineData.map((item, index) => {
    const start = Math.max(0, index - 6);
    const end = index + 1;
    const slice = timelineData.slice(start, end);
    const average = slice.reduce((sum, day) => sum + day.metri, 0) / slice.length;

    return {
      ...item,
      media_mobile: average
    };
  });

  // Raggruppa per settimana
  const weeklyData = [];
  let currentWeek = null;
  let weekMeters = 0;
  let weekDays = 0;

  timelineData.forEach((day, index) => {
    const date = new Date(day.data);
    const weekStart = new Date(date);
    weekStart.setDate(date.getDate() - date.getDay() + 1); // Lunedì
    const weekKey = weekStart.toISOString().split('T')[0];

    if (currentWeek !== weekKey) {
      if (currentWeek !== null) {
        weeklyData.push({
          settimana: `${currentWeek.split('-')[2]}/${currentWeek.split('-')[1]}`,
          metri_totali: weekMeters,
          giorni_attivi: weekDays,
          media_giornaliera: weekDays > 0 ? weekMeters / weekDays : 0
        });
      }
      currentWeek = weekKey;
      weekMeters = day.metri;
      weekDays = 1;
    } else {
      weekMeters += day.metri;
      weekDays++;
    }
  });

  // Aggiungi l'ultima settimana
  if (currentWeek !== null) {
    weeklyData.push({
      settimana: `${currentWeek.split('-')[2]}/${currentWeek.split('-')[1]}`,
      metri_totali: weekMeters,
      giorni_attivi: weekDays,
      media_giornaliera: weekDays > 0 ? weekMeters / weekDays : 0
    });
  }

  // Statistiche del periodo
  const stats = {
    totale_metri: data.totale_metri_periodo || 0,
    giorni_attivi: data.giorni_attivi || 0,
    media_giornaliera: data.media_giornaliera || 0,
    giorno_migliore: timelineData.reduce((max, day) => day.metri > max.metri ? day : max, { metri: 0 }),
    giorno_peggiore: timelineData.reduce((min, day) => day.metri < min.metri ? day : min, { metri: Infinity })
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`Data: ${label}`}</Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value?.toFixed(2)}m`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  return (
    <Box sx={{ mt: 3, width: '100%' }}>
      <Typography variant="h6" gutterBottom>
        Analisi Temporale Posa ({data.data_inizio} - {data.data_fine})
      </Typography>

      <Grid container spacing={4}>
        {/* Statistiche Periodo */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Statistiche del Periodo
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Typography variant="h6" color="primary">
                    {stats.totale_metri.toFixed(0)}m
                  </Typography>
                  <Typography variant="body2">Metri Totali</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Typography variant="h6" color="success.main">
                    {stats.giorni_attivi}
                  </Typography>
                  <Typography variant="body2">Giorni Attivi</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Typography variant="h6" color="info.main">
                    {stats.media_giornaliera.toFixed(1)}m
                  </Typography>
                  <Typography variant="body2">Media Giornaliera</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Typography variant="h6" color="warning.main">
                    {stats.giorno_migliore.metri?.toFixed(0)}m
                  </Typography>
                  <Typography variant="body2">Giorno Migliore</Typography>
                  <Typography variant="caption">
                    {stats.giorno_migliore.data_formatted}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Grafico Temporale Giornaliero */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, height: 350, borderRadius: 2 }}>
            <Typography variant="subtitle1" gutterBottom align="center">
              Trend Posa Giornaliera con Media Mobile (7 giorni)
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <ComposedChart data={timelineWithMovingAverage} margin={{ top: 30, right: 50, left: 40, bottom: 80 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="data_formatted" 
                  angle={-45} 
                  textAnchor="end" 
                  height={80}
                />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="metri" fill={COLORS.primary} name="Metri Giornalieri" opacity={0.7} />
                <Line 
                  type="monotone" 
                  dataKey="media_mobile" 
                  stroke={COLORS.error} 
                  strokeWidth={3}
                  name="Media Mobile (7gg)"
                  dot={false}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Grafico Area Cumulativo */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 250, borderRadius: 2 }}>
            <Typography variant="subtitle1" gutterBottom align="center">
              Progresso Cumulativo
            </Typography>
            <ResponsiveContainer width="100%" height={200}>
              <AreaChart data={timelineData.map((item, index) => ({
                ...item,
                cumulativo: timelineData.slice(0, index + 1).reduce((sum, day) => sum + day.metri, 0)
              }))} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="data_formatted" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Area 
                  type="monotone" 
                  dataKey="cumulativo" 
                  stroke={COLORS.success} 
                  fill={COLORS.success}
                  fillOpacity={0.6}
                  name="Metri Cumulativi"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Grafico Settimanale */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 250, borderRadius: 2 }}>
            <Typography variant="subtitle1" gutterBottom align="center">
              Performance Settimanale
            </Typography>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={weeklyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="settimana" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="metri_totali" fill={COLORS.info} name="Metri Settimanali" />
                <Bar dataKey="media_giornaliera" fill={COLORS.warning} name="Media Giornaliera" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Analisi Performance */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Analisi Performance Dettagliata
            </Typography>
            <Grid container spacing={2}>
              {/* Top 5 giorni migliori */}
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>
                  Top 5 Giorni Migliori
                </Typography>
                {timelineData
                  .sort((a, b) => b.metri - a.metri)
                  .slice(0, 5)
                  .map((day, index) => (
                    <Box key={index} sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      p: 1, 
                      mb: 1,
                      border: '1px solid #e0e0e0', 
                      borderRadius: 1,
                      borderLeft: `4px solid ${COLORS.success}`
                    }}>
                      <Typography variant="body2">{day.data}</Typography>
                      <Chip 
                        label={`${day.metri.toFixed(0)}m`}
                        color="success"
                        size="small"
                      />
                    </Box>
                  ))}
              </Grid>

              {/* Settimane migliori */}
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>
                  Top 3 Settimane Migliori
                </Typography>
                {weeklyData
                  .sort((a, b) => b.metri_totali - a.metri_totali)
                  .slice(0, 3)
                  .map((week, index) => (
                    <Box key={index} sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      p: 1, 
                      mb: 1,
                      border: '1px solid #e0e0e0', 
                      borderRadius: 1,
                      borderLeft: `4px solid ${COLORS.info}`
                    }}>
                      <Box>
                        <Typography variant="body2">Settimana {week.settimana}</Typography>
                        <Typography variant="caption">
                          {week.giorni_attivi} giorni attivi
                        </Typography>
                      </Box>
                      <Box sx={{ textAlign: 'right' }}>
                        <Chip 
                          label={`${week.metri_totali.toFixed(0)}m`}
                          color="info"
                          size="small"
                        />
                        <Typography variant="caption" display="block">
                          Media: {week.media_giornaliera.toFixed(1)}m/g
                        </Typography>
                      </Box>
                    </Box>
                  ))}
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TimelineChart;
