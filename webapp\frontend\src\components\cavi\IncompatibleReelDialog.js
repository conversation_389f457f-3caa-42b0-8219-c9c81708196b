import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert
} from '@mui/material';
import WarningIcon from '@mui/icons-material/Warning';

/**
 * Dialog component for handling incompatible reels
 *
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the dialog is open
 * @param {Function} props.onClose - Function to call when the dialog is closed
 * @param {Object} props.cavo - The cable object
 * @param {Object} props.bobina - The reel object
 * @param {Array} props.incompatibilities - List of incompatibilities between cable and reel
 * @param {Function} props.onConfirm - Function to call when the user chooses to continue with incompatible reel
 */
const IncompatibleReelDialog = ({
  open,
  onClose,
  cavo,
  bobina,
  incompatibilities: propIncompatibilities,
  onConfirm
}) => {
  if (!cavo || !bobina) return null;

  // Funzione per estrarre solo la parte "Y" dell'ID bobina
  const getBobinaNumber = (idBobina) => {
    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';

    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}
    if (idBobina && idBobina.includes('_B')) {
      return idBobina.split('_B')[1];
    }
    return idBobina;
  };

  // Usa le incompatibilità passate come prop o calcola quelle predefinite
  const incompatibilities = propIncompatibilities || [];

  // Se non sono state passate incompatibilità, le calcoliamo qui
  if (incompatibilities.length === 0) {
    if (cavo.tipologia !== bobina.tipologia) {
      incompatibilities.push({
        property: 'Tipologia',
        cavoValue: cavo.tipologia || 'N/A',
        bobinaValue: bobina.tipologia || 'N/A'
      });
    }

    // Nella nuova configurazione, il campo n_conduttori non viene più utilizzato

    if (String(cavo.sezione) !== String(bobina.sezione)) {
      incompatibilities.push({
        property: 'Formazione',
        cavoValue: cavo.sezione || 'N/A',
        bobinaValue: bobina.sezione || 'N/A'
      });
    }
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ bgcolor: 'warning.light', display: 'flex', alignItems: 'center', gap: 1 }}>
        <WarningIcon color="warning" />
        <Typography variant="h6">Bobina incompatibile</Typography>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2, mb: 3 }}>
          <Typography variant="body1" paragraph>
            La bobina <strong>{getBobinaNumber(bobina.id_bobina)}</strong> non è compatibile con il cavo <strong>{cavo.id_cavo}</strong>.
            Le seguenti caratteristiche non corrispondono:
          </Typography>

          <TableContainer component={Paper} sx={{ mt: 2, mb: 2 }}>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'grey.100' }}>
                  <TableCell><strong>Caratteristica</strong></TableCell>
                  <TableCell><strong>Valore cavo</strong></TableCell>
                  <TableCell><strong>Valore bobina</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {incompatibilities.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{item.property}</TableCell>
                    <TableCell>
                      {item.cavoValue}
                      {item.note && <Typography variant="caption" color="text.secondary"> {item.note}</Typography>}
                    </TableCell>
                    <TableCell>{item.bobinaValue}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Typography variant="body1" paragraph>
            Puoi scegliere di:
          </Typography>
          <Typography variant="body2" component="ul">
            <li><strong>Utilizzare questa bobina incompatibile</strong> senza modificare le caratteristiche del cavo</li>
            <li><strong>Annullare l'operazione</strong> e selezionare un'altra bobina</li>
          </Typography>

        </Box>
      </DialogContent>
      <DialogActions sx={{ p: 2, justifyContent: 'space-between', gap: 2 }}>
        <Button onClick={onClose} color="secondary" variant="outlined">
          Annulla
        </Button>
        <Button
          onClick={() => onConfirm && onConfirm(bobina, cavo)}
          color="warning"
          variant="contained"
        >
          Usa Bobina Incompatibile
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default IncompatibleReelDialog;
