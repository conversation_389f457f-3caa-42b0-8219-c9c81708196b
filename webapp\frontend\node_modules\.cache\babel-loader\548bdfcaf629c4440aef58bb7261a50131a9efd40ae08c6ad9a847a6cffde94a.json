{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ParcoCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, CardActions, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Divider, Alert, CircularProgress, FormHelperText, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Save as SaveIcon, ViewList as ViewListIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport BobineFilterableTable from './BobineFilterableTable';\nimport QuickAddCablesDialog from './QuickAddCablesDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParcoCavi = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  initialOption = null\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [showQuickAddDialog, setShowQuickAddDialog] = useState(false);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '0',\n    // Imposta sempre a '0' per il campo spare\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      let errorMessage = 'Errore nel caricamento delle bobine';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      }\n      onError(errorMessage);\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Effetto per popolare il form quando viene selezionata una bobina per la modifica\n  useEffect(() => {\n    if (selectedBobina && dialogType === 'modificaBobina') {\n      console.log('Popolamento form per modifica bobina:', selectedBobina);\n\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\n      setFormData({\n        numero_bobina: selectedBobina.numero_bobina || '',\n        utility: String(selectedBobina.utility || ''),\n        tipologia: String(selectedBobina.tipologia || ''),\n        n_conduttori: selectedBobina.n_conduttori !== null && selectedBobina.n_conduttori !== undefined ? String(selectedBobina.n_conduttori) : '',\n        sezione: selectedBobina.sezione !== null && selectedBobina.sezione !== undefined ? String(selectedBobina.sezione) : '',\n        metri_totali: selectedBobina.metri_totali !== null && selectedBobina.metri_totali !== undefined ? Number(selectedBobina.metri_totali) : '',\n        metri_residui: selectedBobina.metri_residui !== null && selectedBobina.metri_residui !== undefined ? Number(selectedBobina.metri_residui) : '',\n        stato_bobina: String(selectedBobina.stato_bobina || 'Disponibile'),\n        ubicazione_bobina: String(selectedBobina.ubicazione_bobina || ''),\n        fornitore: String(selectedBobina.fornitore || ''),\n        n_DDT: String(selectedBobina.n_DDT || ''),\n        data_DDT: selectedBobina.data_DDT || '',\n        configurazione: String(selectedBobina.configurazione || 's')\n      });\n      console.log('Form data impostati per la modifica:', {\n        numero_bobina: selectedBobina.numero_bobina,\n        utility: selectedBobina.utility,\n        tipologia: selectedBobina.tipologia,\n        n_conduttori: selectedBobina.n_conduttori,\n        sezione: selectedBobina.sezione,\n        metri_totali: selectedBobina.metri_totali,\n        metri_residui: selectedBobina.metri_residui\n      });\n    }\n  }, [selectedBobina, dialogType]);\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        // IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\\'AVVIO');\n        setIsFirstInsertion(true);\n        setOpenConfigDialog(true);\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []); // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    // Variabile per memorizzare la configurazione\n    let configurazione = 's'; // Valore predefinito\n\n    try {\n      // Previene chiamate multiple\n      if (loading) {\n        console.log('Operazione già in corso, uscita');\n        return;\n      }\n\n      // Assicuriamoci che nessun dialog sia aperto\n      setOpenDialog(false);\n      setOpenConfigDialog(false); // Chiudi anche il dialog di configurazione se aperto\n\n      setLoading(true);\n      console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n\n      // Gestione caso in cui cantiereId non sia valido\n      if (!cantiereId || isNaN(parseInt(cantiereId))) {\n        onError('ID cantiere non valido');\n        console.error('ID cantiere non valido:', cantiereId);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API reale\n      let isFirst = false;\n      try {\n        console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n        const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n        isFirst = response.is_first_insertion;\n\n        // Controlla se c'è una configurazione salvata in localStorage\n        const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`);\n\n        // Usa la configurazione salvata in localStorage se disponibile, altrimenti usa quella dal server\n        configurazione = savedConfig || response.configurazione || 's';\n        console.log('Configurazione da localStorage:', savedConfig);\n        console.log('Configurazione dal server:', response.configurazione);\n        console.log('Configurazione finale utilizzata:', configurazione);\n        setIsFirstInsertion(isFirst);\n        console.log('È il primo inserimento di una bobina?', isFirst);\n        console.log('Configurazione esistente:', configurazione);\n      } catch (error) {\n        console.error('Errore durante la verifica del primo inserimento:', error);\n        // In caso di errore, assumiamo che non sia il primo inserimento\n        setIsFirstInsertion(false);\n        onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');\n      }\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        // Assicuriamoci che il dialog di creazione sia chiuso\n        setOpenDialog(false);\n\n        // IMPORTANTE: Forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n\n        // Ottieni il prossimo numero di bobina solo se la configurazione è automatica\n        let nextBobinaNumber = '1';\n        if (configurazione === 's') {\n          try {\n            // Ottieni l'ultimo numero di bobina dal backend\n            const bobine = await parcoCaviService.getBobine(cantiereId);\n            if (bobine && bobine.length > 0) {\n              // Filtra solo le bobine con numero_bobina numerico\n              const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n              // Log per debug\n              console.log('Bobine totali:', bobine.length);\n              console.log('Bobine con numero numerico:', numericBobine.length);\n              console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n              if (numericBobine.length > 0) {\n                // Trova il numero massimo tra le bobine esistenti\n                const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n                console.log('Numero massimo trovato:', maxNumber);\n                nextBobinaNumber = String(maxNumber + 1);\n              }\n            }\n            console.log('Prossimo numero bobina:', nextBobinaNumber);\n          } catch (error) {\n            console.error('Errore nel recupero del prossimo numero bobina:', error);\n            // In caso di errore, usa 1 come default\n            nextBobinaNumber = '1';\n          }\n        }\n        setDialogType('creaBobina');\n        setFormData({\n          // In modalità automatica, imposta il numero progressivo\n          // In modalità manuale, lascia vuoto per far inserire all'utente\n          numero_bobina: configurazione === 's' ? nextBobinaNumber : '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '0',\n          // Imposta sempre a '0' per il campo spare\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: configurazione // Usa la configurazione esistente\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      // Gestione dettagliata dell'errore\n      let errorMessage = 'Errore nel controllo dell\\'inserimento della prima bobina';\n      if (error.response) {\n        var _error$response$data;\n        // Errore di risposta dal server\n        errorMessage += `: ${error.response.status} - ${((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore server'}`;\n        console.error('Dettagli errore API:', error.response);\n      } else if (error.request) {\n        // Errore di rete (nessuna risposta ricevuta)\n        errorMessage += ': Errore di connessione al server';\n      } else {\n        // Errore generico\n        errorMessage += `: ${error.message || 'Errore sconosciuto'}`;\n      }\n      onError(errorMessage);\n      console.error('Errore completo:', error);\n\n      // In caso di errore, mantieni la configurazione esistente o usa il default\n      // Non forzare il reset a 's' per evitare di perdere la configurazione manuale\n      if (!configurazione) {\n        configurazione = 's'; // Fallback al valore di default solo se non è già impostato\n      }\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: configurazione === 's' ? '1' : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '0',\n        // Imposta sempre a '0' per il campo spare\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configurazione // Usa la configurazione esistente o il default\n      });\n      setOpenDialog(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = async configValue => {\n    console.log('Configurazione selezionata:', configValue);\n    // Salva la configurazione selezionata in localStorage per persistenza\n    localStorage.setItem(`cantiere_${cantiereId}_config`, configValue);\n    setLoading(true);\n    try {\n      // Ottieni il prossimo numero di bobina se la configurazione è automatica\n      let nextBobinaNumber = '1';\n      if (configValue === 's') {\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Filtra solo le bobine con numero_bobina numerico\n            const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n            // Log per debug\n            console.log('Bobine totali:', bobine.length);\n            console.log('Bobine con numero numerico:', numericBobine.length);\n            console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n            if (numericBobine.length > 0) {\n              // Trova il numero massimo tra le bobine esistenti\n              const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n              console.log('Numero massimo trovato:', maxNumber);\n              nextBobinaNumber = String(maxNumber + 1);\n            }\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n      }\n\n      // Imposta i valori di default per la bobina\n      const defaultFormData = {\n        // In modalità automatica, imposta il numero progressivo\n        // In modalità manuale, lascia vuoto per far inserire all'utente\n        numero_bobina: configValue === 's' ? nextBobinaNumber : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '0',\n        // Imposta sempre a '0' per il campo spare\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configValue // Imposta la configurazione scelta\n      };\n      console.log('Impostando i dati del form con configurazione:', configValue, 'numero_bobina:', defaultFormData.numero_bobina);\n\n      // Importante: prima prepara il form, poi chiudi il dialog di configurazione\n      // e solo dopo apri il dialog di creazione\n      setFormData(defaultFormData);\n      setDialogType('creaBobina');\n\n      // Chiudi il dialog di configurazione\n      setOpenConfigDialog(false);\n\n      // Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso\n      // prima di aprire il dialog di creazione\n      setTimeout(() => {\n        setOpenDialog(true);\n        console.log('Dialog di creazione bobina aperto con numero_bobina:', defaultFormData.numero_bobina);\n      }, 300);\n    } catch (error) {\n      console.error('Errore durante la preparazione del form:', error);\n      onError('Errore durante la preparazione del form: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n\n    // Recupera la configurazione salvata per questo cantiere\n    const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`) || 's';\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '0',\n      // Imposta sempre a '0' per il campo spare\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: savedConfig // Mantieni la configurazione salvata\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = bobina => {\n    console.log('Bobina selezionata:', bobina);\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n      // Il popolamento del form è ora gestito dall'useEffect\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = {\n              ...prev\n            };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = {\n            ...prev\n          };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = {\n              ...prev\n            };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n\n        // Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale\n        if (formData.configurazione === 'n' && (!formData.numero_bobina || formData.numero_bobina.trim() === '')) {\n          setFormErrors({\n            ...formErrors,\n            numero_bobina: 'L\\'ID della bobina è obbligatorio'\n          });\n          onError('L\\'ID della bobina è obbligatorio');\n          return;\n        }\n      }\n      setLoading(true);\n      console.log('Salvataggio dati bobina in corso...');\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Assicurati che tutti i campi siano nel formato corretto\n          numero_bobina: String(formData.numero_bobina || ''),\n          utility: String(formData.utility || ''),\n          tipologia: String(formData.tipologia || ''),\n          n_conduttori: String(formData.n_conduttori || ''),\n          sezione: String(formData.sezione || ''),\n          metri_totali: parseFloat(formData.metri_totali) || 0,\n          ubicazione_bobina: String(formData.ubicazione_bobina || 'TBD'),\n          fornitore: String(formData.fornitore || 'TBD'),\n          n_DDT: String(formData.n_DDT || 'TBD'),\n          data_DDT: formData.data_DDT || null,\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: String(formData.configurazione || 's')\n        };\n        console.log('Dati bobina da inviare:', bobinaData);\n        try {\n          // Log dei dati che stiamo per inviare\n          console.log('Invio dati bobina al backend:', JSON.stringify(bobinaData, null, 2));\n\n          // Invia i dati al backend\n          await parcoCaviService.createBobina(cantiereId, bobinaData);\n          onSuccess('Bobina creata con successo');\n\n          // Chiudi il dialog\n          handleCloseDialog();\n\n          // Imposta l'opzione selezionata a visualizzaBobine e carica le bobine\n          setSelectedOption('visualizzaBobine');\n          loadBobine();\n\n          // Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine\n          if (typeof window !== 'undefined') {\n            // Usa un evento personalizzato per comunicare con il componente padre\n            const event = new CustomEvent('redirectToVisualizzaBobine', {\n              detail: {\n                cantiereId\n              }\n            });\n            window.dispatchEvent(event);\n          }\n        } catch (error) {\n          console.error('Errore durante la creazione della bobina:', error);\n\n          // Gestione dettagliata dell'errore\n          let errorMessage = 'Errore durante la creazione della bobina';\n          if (error.detail) {\n            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n\n          // Log dettagliato dell'errore\n          console.error('Dettagli errore:', errorMessage);\n          console.error('Dati inviati:', JSON.stringify(bobinaData, null, 2));\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina || formData.numero_bobina;\n        console.log('Modifica bobina con ID:', bobinaId);\n        console.log('Dati da inviare:', formData);\n        try {\n          const response = await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n          console.log('Risposta modifica bobina:', response);\n          onSuccess('Bobina modificata con successo');\n        } catch (error) {\n          console.error('Errore durante la modifica della bobina:', error);\n          let errorMessage = 'Errore durante la modifica della bobina';\n          if (error.detail) {\n            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        const response = await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n\n        // Verifica se è stata eliminata l'ultima bobina\n        if (response.is_last_bobina) {\n          console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);\n          onSuccess('Bobina eliminata con successo. Il parco cavi è ora vuoto.');\n          // Non forziamo più l'apertura del dialog di creazione quando l'ultima bobina viene eliminata\n          // L'utente può decidere se inserire una nuova bobina o lasciare il parco cavi vuoto\n        } else {\n          onSuccess('Bobina eliminata con successo');\n        }\n      }\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      let errorMessage = 'Errore durante l\\'operazione';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      } else {\n        errorMessage += ': Errore sconosciuto';\n      }\n      onError(errorMessage);\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // La visualizzazione delle bobine è ora gestita dal componente BobineFilterableTable\n  const renderBobineCards = () => {\n    return /*#__PURE__*/_jsxDEV(BobineFilterableTable, {\n      bobine: bobine,\n      loading: loading,\n      onFilteredDataChange: filteredData => console.log('Bobine filtrate:', filteredData.length),\n      onEdit: bobina => {\n        setSelectedBobina(bobina);\n        setDialogType('modificaBobina');\n        setOpenDialog(true);\n      },\n      onDelete: bobina => {\n        setSelectedBobina(bobina);\n        setDialogType('eliminaBobina');\n        setOpenDialog(true);\n      },\n      onViewHistory: bobina => {\n        setSelectedBobina(bobina);\n        loadStoricoUtilizzo();\n        setDialogType('visualizzaStorico');\n        setOpenDialog(true);\n      },\n      onQuickAdd: bobina => {\n        setSelectedBobina(bobina);\n        setShowQuickAddDialog(true);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 681,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [dialogType === 'modificaBobina' && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              fontWeight: \"bold\",\n              children: \"Condizioni per la modifica:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"La bobina deve essere nello stato \\\"Disponibile\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"La bobina non deve essere associata a nessun cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato attuale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 730,\n                  columnNumber: 21\n                }, this), \" \", (selectedBobina === null || selectedBobina === void 0 ? void 0 : selectedBobina.stato_bobina) || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 15\n          }, this), Object.keys(formWarnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              children: \"Attenzione:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                margin: 0,\n                paddingLeft: '20px'\n              },\n              children: Object.values(formWarnings).map((warning, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: warning\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"numero_bobina\",\n                label: \"ID Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.numero_bobina,\n                onChange: handleFormChange,\n                disabled: dialogType === 'modificaBobina' || dialogType === 'creaBobina' && formData.configurazione === 's',\n                required: true,\n                error: !!formErrors.numero_bobina,\n                InputProps: {\n                  sx: {\n                    bgcolor: formData.configurazione === 's' ? '#f5f5f5' : 'transparent',\n                    fontWeight: 'bold'\n                  }\n                },\n                helperText: formErrors.numero_bobina || (dialogType === 'creaBobina' && formData.configurazione === 's' ? `ID completo: C${cantiereId}_B${formData.numero_bobina || ''} (generato automaticamente)` : dialogType === 'creaBobina' && formData.configurazione === 'n' ? `Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}` : ''),\n                type: formData.configurazione === 's' ? \"text\" : \"text\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.utility,\n                helperText: formErrors.utility || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.tipologia,\n                helperText: formErrors.tipologia || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Formazione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.sezione,\n                helperText: formErrors.sezione || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_totali\",\n                label: \"Metri Totali\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_totali,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.metri_totali,\n                helperText: formErrors.metri_totali || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 15\n            }, this), dialogType === 'modificaBobina' && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_residui\",\n                label: \"Metri Residui\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_residui,\n                onChange: handleFormChange,\n                required: true,\n                disabled: true,\n                helperText: \"I metri residui non possono essere modificati direttamente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"stato-bobina-label\",\n                  children: \"Stato Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"stato-bobina-label\",\n                  name: \"stato_bobina\",\n                  value: formData.stato_bobina,\n                  label: \"Stato Bobina\",\n                  onChange: handleFormChange,\n                  disabled: dialogType === 'creaBobina',\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Disponibile\",\n                    children: \"Disponibile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"In uso\",\n                    children: \"In uso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Terminata\",\n                    children: \"Terminata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 859,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Danneggiata\",\n                    children: \"Danneggiata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Over\",\n                    children: \"Over\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 849,\n                  columnNumber: 19\n                }, this), dialogType === 'creaBobina' && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: \"Per una nuova bobina, lo stato \\xE8 sempre \\\"Disponibile\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_bobina\",\n                label: \"Ubicazione Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_bobina,\n                onChange: handleFormChange,\n                error: !!formErrors.ubicazione_bobina,\n                helperText: formErrors.ubicazione_bobina || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"fornitore\",\n                label: \"Fornitore\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.fornitore,\n                onChange: handleFormChange,\n                error: !!formErrors.fornitore,\n                helperText: formErrors.fornitore || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_DDT\",\n                label: \"Numero DDT\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_DDT,\n                onChange: handleFormChange,\n                error: !!formErrors.n_DDT,\n                helperText: formErrors.n_DDT || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 892,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"data_DDT\",\n                label: \"Data DDT (YYYY-MM-DD)\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.data_DDT,\n                onChange: handleFormChange,\n                placeholder: \"YYYY-MM-DD\",\n                error: !!formErrors.data_DDT,\n                helperText: formErrors.data_DDT || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"configurazione\",\n                label: \"Modalit\\xE0 Numerazione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.configurazione === 's' ? 'Automatica' : 'Manuale',\n                InputProps: {\n                  readOnly: true,\n                  sx: {\n                    bgcolor: '#f5f5f5'\n                  }\n                },\n                helperText: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    fontWeight: 'medium',\n                    color: formData.configurazione === 's' ? 'success.main' : 'info.main'\n                  },\n                  children: formData.configurazione === 's' ? 'Numerazione progressiva automatica (1, 2, 3, ...)' : 'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || Object.keys(formErrors).length > 0,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 69\n            }, this),\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 943,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 941,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Bobina da Modificare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 958,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 15\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleBobinaSelect(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 21\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 967,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 959,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 981,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 957,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Elimina Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 989,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: !selectedBobina ? loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 17\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 995,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => setSelectedBobina(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1004,\n                columnNumber: 23\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Sei sicuro di voler eliminare la bobina \", selectedBobina.numero_bobina, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 2\n              },\n              children: \"Questa operazione non pu\\xF2 essere annullata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1017,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                fontWeight: \"bold\",\n                children: \"Condizioni per l'eliminazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"La bobina deve essere completamente integra (metri residui = metri totali)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1025,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"La bobina deve essere nello stato \\\"Disponibile\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1026,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"La bobina non deve essere associata a nessun cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1027,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1020,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 1,\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato attuale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1032,\n                  columnNumber: 21\n                }, this), \" \", selectedBobina.stato_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1031,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri totali:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 21\n                }, this), \" \", selectedBobina.metri_totali || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1034,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri residui:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1038,\n                  columnNumber: 21\n                }, this), \" \", selectedBobina.metri_residui || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1030,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1013,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 13\n          }, this), selectedBobina && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 71\n            }, this),\n            children: \"Elimina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1047,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 988,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'visualizzaStorico') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"lg\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Storico Utilizzo Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1062,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1065,\n            columnNumber: 15\n          }, this) : storicoUtilizzo.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun dato storico disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1067,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1073,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Utility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1074,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1075,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Formazione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1076,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1077,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1078,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Cavi Associati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1072,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1071,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: storicoUtilizzo.map((record, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.numero_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1085,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.utility\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1086,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1087,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1088,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_totali\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1089,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_residui\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1090,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.cavi.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1091,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1084,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1082,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1070,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1069,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1063,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1100,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1099,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1061,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [selectedOption === 'visualizzaBobine' && !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"inherit\",\n            startIcon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 28\n            }, this),\n            onClick: () => {\n              loadStoricoUtilizzo();\n              setDialogType('visualizzaStorico');\n              setOpenDialog(true);\n            },\n            sx: {\n              fontWeight: 'medium',\n              borderRadius: 0\n            },\n            children: \"Storico Utilizzo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1115,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"inherit\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1134,\n              columnNumber: 28\n            }, this),\n            onClick: () => checkIfFirstInsertion(),\n            sx: {\n              fontWeight: 'medium',\n              borderRadius: 0\n            },\n            children: \"Crea Nuova Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1131,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1114,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1113,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1147,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1146,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(BobineFilterableTable, {\n        bobine: bobine,\n        loading: loading,\n        onFilteredDataChange: filteredData => console.log('Bobine filtrate:', filteredData.length),\n        onEdit: bobina => {\n          setSelectedBobina(bobina);\n          setDialogType('modificaBobina');\n          setOpenDialog(true);\n        },\n        onDelete: bobina => {\n          setSelectedBobina(bobina);\n          setDialogType('eliminaBobina');\n          setOpenDialog(true);\n        },\n        onViewHistory: bobina => {\n          setSelectedBobina(bobina);\n          loadStoricoUtilizzo();\n          setDialogType('visualizzaStorico');\n          setOpenDialog(true);\n        },\n        onQuickAdd: bobina => {\n          setSelectedBobina(bobina);\n          setShowQuickAddDialog(true);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1150,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1112,\n      columnNumber: 9\n    }, this) : !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        minHeight: '300px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: !selectedOption ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Seleziona un'opzione dal menu principale per iniziare.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1180,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [selectedOption === 'creaBobina' && 'Crea Nuova Bobina', selectedOption === 'modificaBobina' && 'Modifica Bobina', selectedOption === 'eliminaBobina' && 'Elimina Bobina', selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1185,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1191,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1184,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1178,\n      columnNumber: 9\n    }, this) : null, renderDialog(), /*#__PURE__*/_jsxDEV(ConfigurazioneDialog, {\n      open: openConfigDialog,\n      onClose: () => setOpenConfigDialog(false),\n      onConfirm: handleConfigConfirm\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(QuickAddCablesDialog, {\n      open: showQuickAddDialog,\n      onClose: () => {\n        setShowQuickAddDialog(false);\n        // Ricarica le bobine per riflettere i cambiamenti\n        loadBobine();\n      },\n      bobina: selectedBobina,\n      cantiereId: cantiereId,\n      onSuccess: onSuccess,\n      onError: onError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1207,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1110,\n    columnNumber: 5\n  }, this);\n};\n_s(ParcoCavi, \"qr8fMcaDbJNhfyL5LIWAKZ3ZXs0=\");\n_c = ParcoCavi;\nexport default ParcoCavi;\nvar _c;\n$RefreshReg$(_c, \"ParcoCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Save", "SaveIcon", "ViewList", "ViewListIcon", "Warning", "WarningIcon", "parcoCaviService", "ConfigurazioneDialog", "BobineFilterableTable", "QuickAddCablesDialog", "validateBobinaData", "validateBob<PERSON>F<PERSON>", "validateBobinaId", "isEmpty", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantiereId", "onSuccess", "onError", "initialOption", "_s", "loading", "setLoading", "bobine", "set<PERSON>ob<PERSON>", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBobina", "showQuickAddDialog", "setShowQuickAddDialog", "formData", "setFormData", "numero_bobina", "utility", "tipologia", "n_conduttori", "sezione", "metri_totali", "metri_residui", "stato_bobina", "ubicazione_bobina", "fornitore", "n_DDT", "data_DDT", "configurazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "storico<PERSON><PERSON><PERSON><PERSON>", "setStoricoUtilizzo", "openConfigDialog", "setOpenConfigDialog", "isFirstInsertion", "setIsFirstInsertion", "loadBobine", "data", "getBobine", "error", "errorMessage", "detail", "JSON", "stringify", "message", "console", "log", "String", "undefined", "Number", "initialLoadDone", "useRef", "current", "handleOptionSelect", "checkIfFirstInsertion", "isNaN", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "response", "isFirstBobinaInsertion", "is_first_insertion", "savedConfig", "localStorage", "getItem", "nextBobinaNumber", "length", "numericBobine", "filter", "b", "test", "map", "maxNumber", "Math", "max", "_error$response$data", "status", "request", "handleConfigConfirm", "config<PERSON><PERSON><PERSON>", "setItem", "defaultFormData", "setTimeout", "option", "loadStoricoUtilizzo", "handleCloseDialog", "handleBobinaSelect", "bobina", "handleFormChange", "e", "name", "value", "target", "idResult", "valid", "prev", "newErrors", "result", "warning", "newWarnings", "handleSave", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "trim", "bobina<PERSON><PERSON>", "parseFloat", "createBobina", "window", "event", "CustomEvent", "dispatchEvent", "bobina<PERSON>d", "id_bobina", "updateBobina", "deleteBobina", "is_last_bobina", "renderBobineCards", "onFilteredDataChange", "filteredData", "onEdit", "onDelete", "onViewHistory", "onQuickAdd", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "severity", "sx", "mb", "mt", "variant", "fontWeight", "Object", "keys", "style", "margin", "paddingLeft", "values", "index", "container", "spacing", "item", "xs", "sm", "label", "onChange", "disabled", "required", "InputProps", "bgcolor", "helperText", "type", "id", "labelId", "placeholder", "readOnly", "color", "onClick", "startIcon", "size", "button", "primary", "secondary", "display", "flexDirection", "gap", "component", "record", "cavi", "p", "justifyContent", "alignItems", "borderRadius", "my", "minHeight", "textAlign", "gutterBottom", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/ParcoCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  History as HistoryIcon,\n  Save as SaveIcon,\n  ViewList as ViewListIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport BobineFilterableTable from './BobineFilterableTable';\nimport QuickAddCablesDialog from './QuickAddCablesDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\n\nconst ParcoCavi = ({ cantiereId, onSuccess, onError, initialOption = null }) => {\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [showQuickAddDialog, setShowQuickAddDialog] = useState(false);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '0',  // Imposta sempre a '0' per il campo spare\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      let errorMessage = 'Errore nel caricamento delle bobine';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      }\n      onError(errorMessage);\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Effetto per popolare il form quando viene selezionata una bobina per la modifica\n  useEffect(() => {\n    if (selectedBobina && dialogType === 'modificaBobina') {\n      console.log('Popolamento form per modifica bobina:', selectedBobina);\n\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\n      setFormData({\n        numero_bobina: selectedBobina.numero_bobina || '',\n        utility: String(selectedBobina.utility || ''),\n        tipologia: String(selectedBobina.tipologia || ''),\n        n_conduttori: selectedBobina.n_conduttori !== null && selectedBobina.n_conduttori !== undefined ? String(selectedBobina.n_conduttori) : '',\n        sezione: selectedBobina.sezione !== null && selectedBobina.sezione !== undefined ? String(selectedBobina.sezione) : '',\n        metri_totali: selectedBobina.metri_totali !== null && selectedBobina.metri_totali !== undefined ? Number(selectedBobina.metri_totali) : '',\n        metri_residui: selectedBobina.metri_residui !== null && selectedBobina.metri_residui !== undefined ? Number(selectedBobina.metri_residui) : '',\n        stato_bobina: String(selectedBobina.stato_bobina || 'Disponibile'),\n        ubicazione_bobina: String(selectedBobina.ubicazione_bobina || ''),\n        fornitore: String(selectedBobina.fornitore || ''),\n        n_DDT: String(selectedBobina.n_DDT || ''),\n        data_DDT: selectedBobina.data_DDT || '',\n        configurazione: String(selectedBobina.configurazione || 's')\n      });\n\n      console.log('Form data impostati per la modifica:', {\n        numero_bobina: selectedBobina.numero_bobina,\n        utility: selectedBobina.utility,\n        tipologia: selectedBobina.tipologia,\n        n_conduttori: selectedBobina.n_conduttori,\n        sezione: selectedBobina.sezione,\n        metri_totali: selectedBobina.metri_totali,\n        metri_residui: selectedBobina.metri_residui\n      });\n    }\n  }, [selectedBobina, dialogType]);\n\n\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        // IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\\'AVVIO');\n        setIsFirstInsertion(true);\n        setOpenConfigDialog(true);\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []);  // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    // Variabile per memorizzare la configurazione\n    let configurazione = 's'; // Valore predefinito\n\n    try {\n      // Previene chiamate multiple\n      if (loading) {\n        console.log('Operazione già in corso, uscita');\n        return;\n      }\n\n      // Assicuriamoci che nessun dialog sia aperto\n      setOpenDialog(false);\n      setOpenConfigDialog(false); // Chiudi anche il dialog di configurazione se aperto\n\n      setLoading(true);\n      console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n\n      // Gestione caso in cui cantiereId non sia valido\n      if (!cantiereId || isNaN(parseInt(cantiereId))) {\n        onError('ID cantiere non valido');\n        console.error('ID cantiere non valido:', cantiereId);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API reale\n      let isFirst = false;\n\n      try {\n        console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n        const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n        isFirst = response.is_first_insertion;\n\n        // Controlla se c'è una configurazione salvata in localStorage\n        const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`);\n\n        // Usa la configurazione salvata in localStorage se disponibile, altrimenti usa quella dal server\n        configurazione = savedConfig || response.configurazione || 's';\n\n        console.log('Configurazione da localStorage:', savedConfig);\n        console.log('Configurazione dal server:', response.configurazione);\n        console.log('Configurazione finale utilizzata:', configurazione);\n\n        setIsFirstInsertion(isFirst);\n        console.log('È il primo inserimento di una bobina?', isFirst);\n        console.log('Configurazione esistente:', configurazione);\n      } catch (error) {\n        console.error('Errore durante la verifica del primo inserimento:', error);\n        // In caso di errore, assumiamo che non sia il primo inserimento\n        setIsFirstInsertion(false);\n        onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');\n      }\n\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        // Assicuriamoci che il dialog di creazione sia chiuso\n        setOpenDialog(false);\n\n        // IMPORTANTE: Forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n\n        // Ottieni il prossimo numero di bobina solo se la configurazione è automatica\n        let nextBobinaNumber = '1';\n        if (configurazione === 's') {\n          try {\n            // Ottieni l'ultimo numero di bobina dal backend\n            const bobine = await parcoCaviService.getBobine(cantiereId);\n            if (bobine && bobine.length > 0) {\n              // Filtra solo le bobine con numero_bobina numerico\n              const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n              // Log per debug\n              console.log('Bobine totali:', bobine.length);\n              console.log('Bobine con numero numerico:', numericBobine.length);\n              console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n\n              if (numericBobine.length > 0) {\n                // Trova il numero massimo tra le bobine esistenti\n                const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n                console.log('Numero massimo trovato:', maxNumber);\n                nextBobinaNumber = String(maxNumber + 1);\n              }\n            }\n            console.log('Prossimo numero bobina:', nextBobinaNumber);\n          } catch (error) {\n            console.error('Errore nel recupero del prossimo numero bobina:', error);\n            // In caso di errore, usa 1 come default\n            nextBobinaNumber = '1';\n          }\n        }\n\n        setDialogType('creaBobina');\n        setFormData({\n          // In modalità automatica, imposta il numero progressivo\n          // In modalità manuale, lascia vuoto per far inserire all'utente\n          numero_bobina: configurazione === 's' ? nextBobinaNumber : '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '0',  // Imposta sempre a '0' per il campo spare\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: configurazione  // Usa la configurazione esistente\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      // Gestione dettagliata dell'errore\n      let errorMessage = 'Errore nel controllo dell\\'inserimento della prima bobina';\n\n      if (error.response) {\n        // Errore di risposta dal server\n        errorMessage += `: ${error.response.status} - ${error.response.data?.detail || 'Errore server'}`;\n        console.error('Dettagli errore API:', error.response);\n      } else if (error.request) {\n        // Errore di rete (nessuna risposta ricevuta)\n        errorMessage += ': Errore di connessione al server';\n      } else {\n        // Errore generico\n        errorMessage += `: ${error.message || 'Errore sconosciuto'}`;\n      }\n\n      onError(errorMessage);\n      console.error('Errore completo:', error);\n\n      // In caso di errore, mantieni la configurazione esistente o usa il default\n      // Non forzare il reset a 's' per evitare di perdere la configurazione manuale\n      if (!configurazione) {\n        configurazione = 's'; // Fallback al valore di default solo se non è già impostato\n      }\n\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: configurazione === 's' ? '1' : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '0',  // Imposta sempre a '0' per il campo spare\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configurazione  // Usa la configurazione esistente o il default\n      });\n      setOpenDialog(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = async (configValue) => {\n    console.log('Configurazione selezionata:', configValue);\n    // Salva la configurazione selezionata in localStorage per persistenza\n    localStorage.setItem(`cantiere_${cantiereId}_config`, configValue);\n    setLoading(true);\n\n    try {\n      // Ottieni il prossimo numero di bobina se la configurazione è automatica\n      let nextBobinaNumber = '1';\n      if (configValue === 's') {\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Filtra solo le bobine con numero_bobina numerico\n            const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n            // Log per debug\n            console.log('Bobine totali:', bobine.length);\n            console.log('Bobine con numero numerico:', numericBobine.length);\n            console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n\n            if (numericBobine.length > 0) {\n              // Trova il numero massimo tra le bobine esistenti\n              const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n              console.log('Numero massimo trovato:', maxNumber);\n              nextBobinaNumber = String(maxNumber + 1);\n            }\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n      }\n\n      // Imposta i valori di default per la bobina\n      const defaultFormData = {\n        // In modalità automatica, imposta il numero progressivo\n        // In modalità manuale, lascia vuoto per far inserire all'utente\n        numero_bobina: configValue === 's' ? nextBobinaNumber : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '0',  // Imposta sempre a '0' per il campo spare\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configValue // Imposta la configurazione scelta\n      };\n\n      console.log('Impostando i dati del form con configurazione:', configValue, 'numero_bobina:', defaultFormData.numero_bobina);\n\n      // Importante: prima prepara il form, poi chiudi il dialog di configurazione\n      // e solo dopo apri il dialog di creazione\n      setFormData(defaultFormData);\n      setDialogType('creaBobina');\n\n      // Chiudi il dialog di configurazione\n      setOpenConfigDialog(false);\n\n      // Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso\n      // prima di aprire il dialog di creazione\n      setTimeout(() => {\n        setOpenDialog(true);\n        console.log('Dialog di creazione bobina aperto con numero_bobina:', defaultFormData.numero_bobina);\n      }, 300);\n    } catch (error) {\n      console.error('Errore durante la preparazione del form:', error);\n      onError('Errore durante la preparazione del form: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n\n    // Recupera la configurazione salvata per questo cantiere\n    const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`) || 's';\n\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '0',  // Imposta sempre a '0' per il campo spare\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: savedConfig // Mantieni la configurazione salvata\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = (bobina) => {\n    console.log('Bobina selezionata:', bobina);\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n      // Il popolamento del form è ora gestito dall'useEffect\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = { ...prev };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = { ...prev };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n\n        // Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale\n        if (formData.configurazione === 'n' && (!formData.numero_bobina || formData.numero_bobina.trim() === '')) {\n          setFormErrors({\n            ...formErrors,\n            numero_bobina: 'L\\'ID della bobina è obbligatorio'\n          });\n          onError('L\\'ID della bobina è obbligatorio');\n          return;\n        }\n      }\n\n      setLoading(true);\n      console.log('Salvataggio dati bobina in corso...');\n\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Assicurati che tutti i campi siano nel formato corretto\n          numero_bobina: String(formData.numero_bobina || ''),\n          utility: String(formData.utility || ''),\n          tipologia: String(formData.tipologia || ''),\n          n_conduttori: String(formData.n_conduttori || ''),\n          sezione: String(formData.sezione || ''),\n          metri_totali: parseFloat(formData.metri_totali) || 0,\n          ubicazione_bobina: String(formData.ubicazione_bobina || 'TBD'),\n          fornitore: String(formData.fornitore || 'TBD'),\n          n_DDT: String(formData.n_DDT || 'TBD'),\n          data_DDT: formData.data_DDT || null,\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: String(formData.configurazione || 's')\n        };\n\n        console.log('Dati bobina da inviare:', bobinaData);\n\n        try {\n          // Log dei dati che stiamo per inviare\n          console.log('Invio dati bobina al backend:', JSON.stringify(bobinaData, null, 2));\n\n          // Invia i dati al backend\n          await parcoCaviService.createBobina(cantiereId, bobinaData);\n          onSuccess('Bobina creata con successo');\n\n          // Chiudi il dialog\n          handleCloseDialog();\n\n          // Imposta l'opzione selezionata a visualizzaBobine e carica le bobine\n          setSelectedOption('visualizzaBobine');\n          loadBobine();\n\n          // Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine\n          if (typeof window !== 'undefined') {\n            // Usa un evento personalizzato per comunicare con il componente padre\n            const event = new CustomEvent('redirectToVisualizzaBobine', { detail: { cantiereId } });\n            window.dispatchEvent(event);\n          }\n        } catch (error) {\n          console.error('Errore durante la creazione della bobina:', error);\n\n          // Gestione dettagliata dell'errore\n          let errorMessage = 'Errore durante la creazione della bobina';\n\n          if (error.detail) {\n            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n\n          // Log dettagliato dell'errore\n          console.error('Dettagli errore:', errorMessage);\n          console.error('Dati inviati:', JSON.stringify(bobinaData, null, 2));\n\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina || formData.numero_bobina;\n\n        console.log('Modifica bobina con ID:', bobinaId);\n        console.log('Dati da inviare:', formData);\n\n        try {\n          const response = await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n          console.log('Risposta modifica bobina:', response);\n          onSuccess('Bobina modificata con successo');\n        } catch (error) {\n          console.error('Errore durante la modifica della bobina:', error);\n          let errorMessage = 'Errore durante la modifica della bobina';\n          if (error.detail) {\n            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        const response = await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n\n        // Verifica se è stata eliminata l'ultima bobina\n        if (response.is_last_bobina) {\n          console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);\n          onSuccess('Bobina eliminata con successo. Il parco cavi è ora vuoto.');\n          // Non forziamo più l'apertura del dialog di creazione quando l'ultima bobina viene eliminata\n          // L'utente può decidere se inserire una nuova bobina o lasciare il parco cavi vuoto\n        } else {\n          onSuccess('Bobina eliminata con successo');\n        }\n      }\n\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      let errorMessage = 'Errore durante l\\'operazione';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      } else {\n        errorMessage += ': Errore sconosciuto';\n      }\n      onError(errorMessage);\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // La visualizzazione delle bobine è ora gestita dal componente BobineFilterableTable\n  const renderBobineCards = () => {\n    return (\n      <BobineFilterableTable\n        bobine={bobine}\n        loading={loading}\n        onFilteredDataChange={(filteredData) => console.log('Bobine filtrate:', filteredData.length)}\n        onEdit={(bobina) => {\n          setSelectedBobina(bobina);\n          setDialogType('modificaBobina');\n          setOpenDialog(true);\n        }}\n        onDelete={(bobina) => {\n          setSelectedBobina(bobina);\n          setDialogType('eliminaBobina');\n          setOpenDialog(true);\n        }}\n        onViewHistory={(bobina) => {\n          setSelectedBobina(bobina);\n          loadStoricoUtilizzo();\n          setDialogType('visualizzaStorico');\n          setOpenDialog(true);\n        }}\n        onQuickAdd={(bobina) => {\n          setSelectedBobina(bobina);\n          setShowQuickAddDialog(true);\n        }}\n      />\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'}\n          </DialogTitle>\n          <DialogContent>\n            {dialogType === 'modificaBobina' && (\n              <Alert severity=\"info\" sx={{ mb: 2, mt: 1 }}>\n                <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                  Condizioni per la modifica:\n                </Typography>\n                <ul>\n                  <li>La bobina deve essere nello stato \"Disponibile\"</li>\n                  <li>La bobina non deve essere associata a nessun cavo</li>\n                  <li>Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente</li>\n                </ul>\n                <Box sx={{ mt: 1 }}>\n                  <Typography variant=\"body2\">\n                    <strong>Stato attuale:</strong> {selectedBobina?.stato_bobina || 'N/A'}\n                  </Typography>\n                </Box>\n              </Alert>\n            )}\n\n            {Object.keys(formWarnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mb: 2, mt: 1 }}>\n                <Typography variant=\"subtitle2\">Attenzione:</Typography>\n                <ul style={{ margin: 0, paddingLeft: '20px' }}>\n                  {Object.values(formWarnings).map((warning, index) => (\n                    <li key={index}>{warning}</li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"numero_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.numero_bobina}\n                  onChange={handleFormChange}\n                  disabled={dialogType === 'modificaBobina' || (dialogType === 'creaBobina' && formData.configurazione === 's')}\n                  required\n                  error={!!formErrors.numero_bobina}\n                  InputProps={{\n                    sx: {\n                      bgcolor: formData.configurazione === 's' ? '#f5f5f5' : 'transparent',\n                      fontWeight: 'bold',\n                    }\n                  }}\n                  helperText={\n                    formErrors.numero_bobina ||\n                    (dialogType === 'creaBobina' && formData.configurazione === 's'\n                      ? `ID completo: C${cantiereId}_B${formData.numero_bobina || ''} (generato automaticamente)`\n                      : dialogType === 'creaBobina' && formData.configurazione === 'n'\n                        ? `Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}`\n                        : '')\n                  }\n                  type={formData.configurazione === 's' ? \"text\" : \"text\"}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia || ''}\n                />\n              </Grid>\n              {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Formazione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_totali\"\n                  label=\"Metri Totali\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_totali}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_totali}\n                  helperText={formErrors.metri_totali || ''}\n                />\n              </Grid>\n              {dialogType === 'modificaBobina' && (\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"metri_residui\"\n                    label=\"Metri Residui\"\n                    type=\"number\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.metri_residui}\n                    onChange={handleFormChange}\n                    required\n                    disabled={true}\n                    helperText=\"I metri residui non possono essere modificati direttamente\"\n                  />\n                </Grid>\n              )}\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel id=\"stato-bobina-label\">Stato Bobina</InputLabel>\n                  <Select\n                    labelId=\"stato-bobina-label\"\n                    name=\"stato_bobina\"\n                    value={formData.stato_bobina}\n                    label=\"Stato Bobina\"\n                    onChange={handleFormChange}\n                    disabled={dialogType === 'creaBobina'}\n                  >\n                    <MenuItem value=\"Disponibile\">Disponibile</MenuItem>\n                    <MenuItem value=\"In uso\">In uso</MenuItem>\n                    <MenuItem value=\"Terminata\">Terminata</MenuItem>\n                    <MenuItem value=\"Danneggiata\">Danneggiata</MenuItem>\n                    <MenuItem value=\"Over\">Over</MenuItem>\n                  </Select>\n                  {dialogType === 'creaBobina' && (\n                    <FormHelperText>Per una nuova bobina, lo stato è sempre \"Disponibile\"</FormHelperText>\n                  )}\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_bobina\"\n                  label=\"Ubicazione Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_bobina}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_bobina}\n                  helperText={formErrors.ubicazione_bobina || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"fornitore\"\n                  label=\"Fornitore\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.fornitore}\n                  onChange={handleFormChange}\n                  error={!!formErrors.fornitore}\n                  helperText={formErrors.fornitore || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_DDT\"\n                  label=\"Numero DDT\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_DDT}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_DDT}\n                  helperText={formErrors.n_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"data_DDT\"\n                  label=\"Data DDT (YYYY-MM-DD)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.data_DDT}\n                  onChange={handleFormChange}\n                  placeholder=\"YYYY-MM-DD\"\n                  error={!!formErrors.data_DDT}\n                  helperText={formErrors.data_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"configurazione\"\n                  label=\"Modalità Numerazione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.configurazione === 's' ? 'Automatica' : 'Manuale'}\n                  InputProps={{\n                    readOnly: true,\n                    sx: {\n                      bgcolor: '#f5f5f5',\n                    }\n                  }}\n                  helperText={\n                    <Box sx={{ fontWeight: 'medium', color: formData.configurazione === 's' ? 'success.main' : 'info.main' }}>\n                      {formData.configurazione === 's'\n                        ? 'Numerazione progressiva automatica (1, 2, 3, ...)'\n                        : 'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'}\n                    </Box>\n                  }\n                />\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading || Object.keys(formErrors).length > 0}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              variant=\"contained\"\n              color=\"primary\"\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Bobina da Modificare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : bobine.length === 0 ? (\n              <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n            ) : (\n              <List>\n                {bobine.map((bobina) => (\n                  <ListItem\n                    button\n                    key={bobina.numero_bobina}\n                    onClick={() => handleBobinaSelect(bobina)}\n                  >\n                    <ListItemText\n                      primary={`Bobina: ${bobina.numero_bobina}`}\n                      secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Bobina</DialogTitle>\n          <DialogContent>\n            {!selectedBobina ? (\n              loading ? (\n                <CircularProgress />\n              ) : bobine.length === 0 ? (\n                <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n              ) : (\n                <List>\n                  {bobine.map((bobina) => (\n                    <ListItem\n                      button\n                      key={bobina.numero_bobina}\n                      onClick={() => setSelectedBobina(bobina)}\n                    >\n                      <ListItemText\n                        primary={`Bobina: ${bobina.numero_bobina}`}\n                        secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la bobina {selectedBobina.numero_bobina}?\n                </Alert>\n                <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                  Questa operazione non può essere annullata.\n                </Typography>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                    Condizioni per l'eliminazione:\n                  </Typography>\n                  <ul>\n                    <li>La bobina deve essere completamente integra (metri residui = metri totali)</li>\n                    <li>La bobina deve essere nello stato \"Disponibile\"</li>\n                    <li>La bobina non deve essere associata a nessun cavo</li>\n                  </ul>\n                </Alert>\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    <strong>Stato attuale:</strong> {selectedBobina.stato_bobina || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Metri totali:</strong> {selectedBobina.metri_totali || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Metri residui:</strong> {selectedBobina.metri_residui || 'N/A'}\n                  </Typography>\n                </Box>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedBobina && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'visualizzaStorico') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"lg\" fullWidth>\n          <DialogTitle>Storico Utilizzo Bobine</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : storicoUtilizzo.length === 0 ? (\n              <Alert severity=\"info\">Nessun dato storico disponibile</Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mt: 2 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Bobina</TableCell>\n                      <TableCell>Utility</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>Formazione</TableCell>\n                      <TableCell>Metri Totali</TableCell>\n                      <TableCell>Metri Residui</TableCell>\n                      <TableCell>Cavi Associati</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {storicoUtilizzo.map((record, index) => (\n                      <TableRow key={index}>\n                        <TableCell>{record.numero_bobina}</TableCell>\n                        <TableCell>{record.utility}</TableCell>\n                        <TableCell>{record.tipologia}</TableCell>\n                        <TableCell>{record.sezione}</TableCell>\n                        <TableCell>{record.metri_totali}</TableCell>\n                        <TableCell>{record.metri_residui}</TableCell>\n                        <TableCell>{record.cavi.length}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n      {selectedOption === 'visualizzaBobine' && !openDialog ? (\n        <Paper sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"inherit\"\n                startIcon={<HistoryIcon />}\n                onClick={() => {\n                  loadStoricoUtilizzo();\n                  setDialogType('visualizzaStorico');\n                  setOpenDialog(true);\n                }}\n                sx={{\n                  fontWeight: 'medium',\n                  borderRadius: 0\n                }}\n              >\n                Storico Utilizzo\n              </Button>\n              <Button\n                variant=\"contained\"\n                color=\"inherit\"\n                startIcon={<AddIcon />}\n                onClick={() => checkIfFirstInsertion()}\n                sx={{\n                  fontWeight: 'medium',\n                  borderRadius: 0\n                }}\n              >\n                Crea Nuova Bobina\n              </Button>\n            </Box>\n          </Box>\n          {loading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <BobineFilterableTable\n              bobine={bobine}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Bobine filtrate:', filteredData.length)}\n              onEdit={(bobina) => {\n                setSelectedBobina(bobina);\n                setDialogType('modificaBobina');\n                setOpenDialog(true);\n              }}\n              onDelete={(bobina) => {\n                setSelectedBobina(bobina);\n                setDialogType('eliminaBobina');\n                setOpenDialog(true);\n              }}\n              onViewHistory={(bobina) => {\n                setSelectedBobina(bobina);\n                loadStoricoUtilizzo();\n                setDialogType('visualizzaStorico');\n                setOpenDialog(true);\n              }}\n              onQuickAdd={(bobina) => {\n                setSelectedBobina(bobina);\n                setShowQuickAddDialog(true);\n              }}\n            />\n          )}\n        </Paper>\n      ) : !openDialog ? (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu principale per iniziare.\n            </Typography>\n          ) : (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'creaBobina' && 'Crea Nuova Bobina'}\n                {selectedOption === 'modificaBobina' && 'Modifica Bobina'}\n                {selectedOption === 'eliminaBobina' && 'Elimina Bobina'}\n                {selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo'}\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      ) : null}\n\n      {renderDialog()}\n\n      {/* Dialog di configurazione per il primo inserimento */}\n      <ConfigurazioneDialog\n        open={openConfigDialog}\n        onClose={() => setOpenConfigDialog(false)}\n        onConfirm={handleConfigConfirm}\n      />\n\n      {/* Dialog per aggiungere rapidamente cavi a una bobina */}\n      <QuickAddCablesDialog\n        open={showQuickAddDialog}\n        onClose={() => {\n          setShowQuickAddDialog(false);\n          // Ricarica le bobine per riflettere i cambiamenti\n          loadBobine();\n        }}\n        bobina={selectedBobina}\n        cantiereId={cantiereId}\n        onSuccess={onSuccess}\n        onError={onError}\n      />\n    </Box>\n  );\n};\n\nexport default ParcoCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,OAAO,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvH,MAAMC,SAAS,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmE,MAAM,EAAEC,SAAS,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqE,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC+D,aAAa,CAAC;EACnE,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2E,cAAc,EAAEC,iBAAiB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+E,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC;IACvCiF,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,GAAG;IAAG;IACpBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,aAAa;IAC3BC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgG,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACkG,eAAe,EAAEC,kBAAkB,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMwG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFtC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuC,IAAI,GAAG,MAAMxD,gBAAgB,CAACyD,SAAS,CAAC9C,UAAU,CAAC;MACzDQ,SAAS,CAACqC,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,IAAIC,YAAY,GAAG,qCAAqC;MACxD,IAAID,KAAK,CAACE,MAAM,EAAE;QAChBD,YAAY,IAAI,IAAI,IAAI,OAAOD,KAAK,CAACE,MAAM,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM,CAAC;MACzG,CAAC,MAAM,IAAIF,KAAK,CAACK,OAAO,EAAE;QACxBJ,YAAY,IAAI,IAAI,GAAGD,KAAK,CAACK,OAAO;MACtC;MACAlD,OAAO,CAAC8C,YAAY,CAAC;MACrBK,OAAO,CAACN,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAjE,SAAS,CAAC,MAAM;IACd,IAAI0E,cAAc,IAAIF,UAAU,KAAK,gBAAgB,EAAE;MACrDwC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEvC,cAAc,CAAC;;MAEpE;MACAK,WAAW,CAAC;QACVC,aAAa,EAAEN,cAAc,CAACM,aAAa,IAAI,EAAE;QACjDC,OAAO,EAAEiC,MAAM,CAACxC,cAAc,CAACO,OAAO,IAAI,EAAE,CAAC;QAC7CC,SAAS,EAAEgC,MAAM,CAACxC,cAAc,CAACQ,SAAS,IAAI,EAAE,CAAC;QACjDC,YAAY,EAAET,cAAc,CAACS,YAAY,KAAK,IAAI,IAAIT,cAAc,CAACS,YAAY,KAAKgC,SAAS,GAAGD,MAAM,CAACxC,cAAc,CAACS,YAAY,CAAC,GAAG,EAAE;QAC1IC,OAAO,EAAEV,cAAc,CAACU,OAAO,KAAK,IAAI,IAAIV,cAAc,CAACU,OAAO,KAAK+B,SAAS,GAAGD,MAAM,CAACxC,cAAc,CAACU,OAAO,CAAC,GAAG,EAAE;QACtHC,YAAY,EAAEX,cAAc,CAACW,YAAY,KAAK,IAAI,IAAIX,cAAc,CAACW,YAAY,KAAK8B,SAAS,GAAGC,MAAM,CAAC1C,cAAc,CAACW,YAAY,CAAC,GAAG,EAAE;QAC1IC,aAAa,EAAEZ,cAAc,CAACY,aAAa,KAAK,IAAI,IAAIZ,cAAc,CAACY,aAAa,KAAK6B,SAAS,GAAGC,MAAM,CAAC1C,cAAc,CAACY,aAAa,CAAC,GAAG,EAAE;QAC9IC,YAAY,EAAE2B,MAAM,CAACxC,cAAc,CAACa,YAAY,IAAI,aAAa,CAAC;QAClEC,iBAAiB,EAAE0B,MAAM,CAACxC,cAAc,CAACc,iBAAiB,IAAI,EAAE,CAAC;QACjEC,SAAS,EAAEyB,MAAM,CAACxC,cAAc,CAACe,SAAS,IAAI,EAAE,CAAC;QACjDC,KAAK,EAAEwB,MAAM,CAACxC,cAAc,CAACgB,KAAK,IAAI,EAAE,CAAC;QACzCC,QAAQ,EAAEjB,cAAc,CAACiB,QAAQ,IAAI,EAAE;QACvCC,cAAc,EAAEsB,MAAM,CAACxC,cAAc,CAACkB,cAAc,IAAI,GAAG;MAC7D,CAAC,CAAC;MAEFoB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAClDjC,aAAa,EAAEN,cAAc,CAACM,aAAa;QAC3CC,OAAO,EAAEP,cAAc,CAACO,OAAO;QAC/BC,SAAS,EAAER,cAAc,CAACQ,SAAS;QACnCC,YAAY,EAAET,cAAc,CAACS,YAAY;QACzCC,OAAO,EAAEV,cAAc,CAACU,OAAO;QAC/BC,YAAY,EAAEX,cAAc,CAACW,YAAY;QACzCC,aAAa,EAAEZ,cAAc,CAACY;MAChC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACZ,cAAc,EAAEF,UAAU,CAAC,CAAC;;EAIhC;EACA;EACA,MAAM6C,eAAe,GAAGvH,KAAK,CAACwH,MAAM,CAAC,KAAK,CAAC;EAE3CtH,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACqH,eAAe,CAACE,OAAO,EAAE;MAC5BP,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEnD,aAAa,CAAC;MAC9EuD,eAAe,CAACE,OAAO,GAAG,IAAI;MAE9B,IAAIzD,aAAa,KAAK,YAAY,EAAE;QAClCkD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjEX,mBAAmB,CAAC,IAAI,CAAC;QACzBF,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM,IAAItC,aAAa,EAAE;QACxBkD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEnD,aAAa,CAAC;QAC/D0D,kBAAkB,CAAC1D,aAAa,CAAC;MACnC,CAAC,MAAM;QACLkD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/BV,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAE;;EAET;EACA,MAAMkB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC;IACA,IAAI7B,cAAc,GAAG,GAAG,CAAC,CAAC;;IAE1B,IAAI;MACF;MACA,IAAI5B,OAAO,EAAE;QACXgD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;MACF;;MAEA;MACA1C,aAAa,CAAC,KAAK,CAAC;MACpB6B,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;;MAE5BnC,UAAU,CAAC,IAAI,CAAC;MAChB+C,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEtD,UAAU,CAAC;;MAEjF;MACA,IAAI,CAACA,UAAU,IAAI+D,KAAK,CAACC,QAAQ,CAAChE,UAAU,CAAC,CAAC,EAAE;QAC9CE,OAAO,CAAC,wBAAwB,CAAC;QACjCmD,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAE/C,UAAU,CAAC;QACpDM,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAI2D,OAAO,GAAG,KAAK;MAEnB,IAAI;QACFZ,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEtD,UAAU,CAAC;QACjF,MAAMkE,QAAQ,GAAG,MAAM7E,gBAAgB,CAAC8E,sBAAsB,CAACnE,UAAU,CAAC;QAC1EiE,OAAO,GAAGC,QAAQ,CAACE,kBAAkB;;QAErC;QACA,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAYvE,UAAU,SAAS,CAAC;;QAEzE;QACAiC,cAAc,GAAGoC,WAAW,IAAIH,QAAQ,CAACjC,cAAc,IAAI,GAAG;QAE9DoB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEe,WAAW,CAAC;QAC3DhB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEY,QAAQ,CAACjC,cAAc,CAAC;QAClEoB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAErB,cAAc,CAAC;QAEhEU,mBAAmB,CAACsB,OAAO,CAAC;QAC5BZ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEW,OAAO,CAAC;QAC7DZ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAErB,cAAc,CAAC;MAC1D,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QACzE;QACAJ,mBAAmB,CAAC,KAAK,CAAC;QAC1BzC,OAAO,CAAC,wFAAwF,CAAC;MACnG;MAEA,IAAI+D,OAAO,EAAE;QACX;QACAZ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD;QACA1C,aAAa,CAAC,KAAK,CAAC;;QAEpB;QACAyC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtDb,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL;QACAY,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;;QAEpE;QACA,IAAIkB,gBAAgB,GAAG,GAAG;QAC1B,IAAIvC,cAAc,KAAK,GAAG,EAAE;UAC1B,IAAI;YACF;YACA,MAAM1B,MAAM,GAAG,MAAMlB,gBAAgB,CAACyD,SAAS,CAAC9C,UAAU,CAAC;YAC3D,IAAIO,MAAM,IAAIA,MAAM,CAACkE,MAAM,GAAG,CAAC,EAAE;cAC/B;cACA,MAAMC,aAAa,GAAGnE,MAAM,CAACoE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACvD,aAAa,IAAI,OAAO,CAACwD,IAAI,CAACD,CAAC,CAACvD,aAAa,CAAC,CAAC;;cAE1F;cACAgC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE/C,MAAM,CAACkE,MAAM,CAAC;cAC5CpB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoB,aAAa,CAACD,MAAM,CAAC;cAChEpB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoB,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIA,CAAC,CAACvD,aAAa,CAAC,CAAC;cAEzE,IAAIqD,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;gBAC5B;gBACA,MAAMM,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIZ,QAAQ,CAACY,CAAC,CAACvD,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;gBACpFgC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEyB,SAAS,CAAC;gBACjDP,gBAAgB,GAAGjB,MAAM,CAACwB,SAAS,GAAG,CAAC,CAAC;cAC1C;YACF;YACA1B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkB,gBAAgB,CAAC;UAC1D,CAAC,CAAC,OAAOzB,KAAK,EAAE;YACdM,OAAO,CAACN,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;YACvE;YACAyB,gBAAgB,GAAG,GAAG;UACxB;QACF;QAEA1D,aAAa,CAAC,YAAY,CAAC;QAC3BM,WAAW,CAAC;UACV;UACA;UACAC,aAAa,EAAEY,cAAc,KAAK,GAAG,GAAGuC,gBAAgB,GAAG,EAAE;UAC7DlD,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,EAAE;UACbC,YAAY,EAAE,GAAG;UAAG;UACpBC,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE,EAAE;UAChBC,aAAa,EAAE,EAAE;UACjBC,YAAY,EAAE,aAAa;UAC3BC,iBAAiB,EAAE,EAAE;UACrBC,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,cAAc,EAAEA,cAAc,CAAE;QAClC,CAAC,CAAC;QACFrB,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACd;MACA,IAAIC,YAAY,GAAG,2DAA2D;MAE9E,IAAID,KAAK,CAACmB,QAAQ,EAAE;QAAA,IAAAgB,oBAAA;QAClB;QACAlC,YAAY,IAAI,KAAKD,KAAK,CAACmB,QAAQ,CAACiB,MAAM,MAAM,EAAAD,oBAAA,GAAAnC,KAAK,CAACmB,QAAQ,CAACrB,IAAI,cAAAqC,oBAAA,uBAAnBA,oBAAA,CAAqBjC,MAAM,KAAI,eAAe,EAAE;QAChGI,OAAO,CAACN,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACmB,QAAQ,CAAC;MACvD,CAAC,MAAM,IAAInB,KAAK,CAACqC,OAAO,EAAE;QACxB;QACApC,YAAY,IAAI,mCAAmC;MACrD,CAAC,MAAM;QACL;QACAA,YAAY,IAAI,KAAKD,KAAK,CAACK,OAAO,IAAI,oBAAoB,EAAE;MAC9D;MAEAlD,OAAO,CAAC8C,YAAY,CAAC;MACrBK,OAAO,CAACN,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA;MACA,IAAI,CAACd,cAAc,EAAE;QACnBA,cAAc,GAAG,GAAG,CAAC,CAAC;MACxB;MAEAnB,aAAa,CAAC,YAAY,CAAC;MAC3BM,WAAW,CAAC;QACVC,aAAa,EAAEY,cAAc,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE;QAChDX,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,GAAG;QAAG;QACpBC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,aAAa;QAC3BC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAEA,cAAc,CAAE;MAClC,CAAC,CAAC;MACFrB,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+E,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjDjC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEgC,WAAW,CAAC;IACvD;IACAhB,YAAY,CAACiB,OAAO,CAAC,YAAYvF,UAAU,SAAS,EAAEsF,WAAW,CAAC;IAClEhF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,IAAIkE,gBAAgB,GAAG,GAAG;MAC1B,IAAIc,WAAW,KAAK,GAAG,EAAE;QACvB,IAAI;UACF;UACA,MAAM/E,MAAM,GAAG,MAAMlB,gBAAgB,CAACyD,SAAS,CAAC9C,UAAU,CAAC;UAC3D,IAAIO,MAAM,IAAIA,MAAM,CAACkE,MAAM,GAAG,CAAC,EAAE;YAC/B;YACA,MAAMC,aAAa,GAAGnE,MAAM,CAACoE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACvD,aAAa,IAAI,OAAO,CAACwD,IAAI,CAACD,CAAC,CAACvD,aAAa,CAAC,CAAC;;YAE1F;YACAgC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE/C,MAAM,CAACkE,MAAM,CAAC;YAC5CpB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoB,aAAa,CAACD,MAAM,CAAC;YAChEpB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoB,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIA,CAAC,CAACvD,aAAa,CAAC,CAAC;YAEzE,IAAIqD,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;cAC5B;cACA,MAAMM,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIZ,QAAQ,CAACY,CAAC,CAACvD,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;cACpFgC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEyB,SAAS,CAAC;cACjDP,gBAAgB,GAAGjB,MAAM,CAACwB,SAAS,GAAG,CAAC,CAAC;YAC1C;UACF;UACA1B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkB,gBAAgB,CAAC;QAC1D,CAAC,CAAC,OAAOzB,KAAK,EAAE;UACdM,OAAO,CAACN,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE;UACAyB,gBAAgB,GAAG,GAAG;QACxB;MACF;;MAEA;MACA,MAAMgB,eAAe,GAAG;QACtB;QACA;QACAnE,aAAa,EAAEiE,WAAW,KAAK,GAAG,GAAGd,gBAAgB,GAAG,EAAE;QAC1DlD,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,GAAG;QAAG;QACpBC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,aAAa;QAC3BC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAEqD,WAAW,CAAC;MAC9B,CAAC;MAEDjC,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEgC,WAAW,EAAE,gBAAgB,EAAEE,eAAe,CAACnE,aAAa,CAAC;;MAE3H;MACA;MACAD,WAAW,CAACoE,eAAe,CAAC;MAC5B1E,aAAa,CAAC,YAAY,CAAC;;MAE3B;MACA2B,mBAAmB,CAAC,KAAK,CAAC;;MAE1B;MACA;MACAgD,UAAU,CAAC,MAAM;QACf7E,aAAa,CAAC,IAAI,CAAC;QACnByC,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEkC,eAAe,CAACnE,aAAa,CAAC;MACpG,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE7C,OAAO,CAAC,2CAA2C,IAAI6C,KAAK,CAACK,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAChG,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuD,kBAAkB,GAAI6B,MAAM,IAAK;IACrChF,iBAAiB,CAACgF,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,kBAAkB,EAAE;MACjC9C,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAI8C,MAAM,KAAK,YAAY,EAAE;MAClC5B,qBAAqB,CAAC,CAAC;IACzB,CAAC,MAAM,IAAI4B,MAAM,KAAK,gBAAgB,EAAE;MACtC9C,UAAU,CAAC,CAAC;MACZ9B,aAAa,CAAC,iBAAiB,CAAC;MAChCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI8E,MAAM,KAAK,eAAe,EAAE;MACrC9C,UAAU,CAAC,CAAC;MACZ9B,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI8E,MAAM,KAAK,mBAAmB,EAAE;MACzCC,mBAAmB,CAAC,CAAC;MACrB7E,aAAa,CAAC,mBAAmB,CAAC;MAClCF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMgF,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhF,aAAa,CAAC,KAAK,CAAC;IACpBI,iBAAiB,CAAC,IAAI,CAAC;;IAEvB;IACA,MAAMqD,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAYvE,UAAU,SAAS,CAAC,IAAI,GAAG;IAEhFoB,WAAW,CAAC;MACVC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,GAAG;MAAG;MACpBC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,aAAa;MAC3BC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAEoC,WAAW,CAAC;IAC9B,CAAC,CAAC;IACFlC,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMwD,kBAAkB,GAAIC,MAAM,IAAK;IACrCzC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwC,MAAM,CAAC;IAC1C9E,iBAAiB,CAAC8E,MAAM,CAAC;IACzB,IAAIjF,UAAU,KAAK,iBAAiB,EAAE;MACpCC,aAAa,CAAC,gBAAgB,CAAC;MAC/B;IACF;EACF,CAAC;;EAED;EACA,MAAMiF,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA/E,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC8E,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAIrF,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE;MACA,IAAIoF,IAAI,KAAK,eAAe,IAAI9E,QAAQ,CAACc,cAAc,KAAK,GAAG,EAAE;QAC/D,MAAMmE,QAAQ,GAAGzG,gBAAgB,CAACuG,KAAK,CAAC;QACxC,IAAI,CAACE,QAAQ,CAACC,KAAK,EAAE;UACnBlE,aAAa,CAACmE,IAAI,KAAK;YACrB,GAAGA,IAAI;YACP,CAACL,IAAI,GAAGG,QAAQ,CAAChD;UACnB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLjB,aAAa,CAACmE,IAAI,IAAI;YACpB,MAAMC,SAAS,GAAG;cAAE,GAAGD;YAAK,CAAC;YAC7B,OAAOC,SAAS,CAACN,IAAI,CAAC;YACtB,OAAOM,SAAS;UAClB,CAAC,CAAC;QACJ;QACA;MACF;MAEA,MAAMC,MAAM,GAAG9G,mBAAmB,CAACuG,IAAI,EAAEC,KAAK,CAAC;;MAE/C;MACA,IAAI,CAACM,MAAM,CAACH,KAAK,EAAE;QACjBlE,aAAa,CAACmE,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP,CAACL,IAAI,GAAGO,MAAM,CAACpD;QACjB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLjB,aAAa,CAACmE,IAAI,IAAI;UACpB,MAAMC,SAAS,GAAG;YAAE,GAAGD;UAAK,CAAC;UAC7B,OAAOC,SAAS,CAACN,IAAI,CAAC;UACtB,OAAOM,SAAS;QAClB,CAAC,CAAC;;QAEF;QACA,IAAIC,MAAM,CAACC,OAAO,EAAE;UAClBpE,eAAe,CAACiE,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACL,IAAI,GAAGO,MAAM,CAACpD;UACjB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLf,eAAe,CAACiE,IAAI,IAAI;YACtB,MAAMI,WAAW,GAAG;cAAE,GAAGJ;YAAK,CAAC;YAC/B,OAAOI,WAAW,CAACT,IAAI,CAAC;YACxB,OAAOS,WAAW;UACpB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,IAAI9F,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;QAClE,MAAM+F,UAAU,GAAGnH,kBAAkB,CAAC0B,QAAQ,CAAC;QAE/C,IAAI,CAACyF,UAAU,CAACC,OAAO,EAAE;UACvB1E,aAAa,CAACyE,UAAU,CAACE,MAAM,CAAC;UAChCzE,eAAe,CAACuE,UAAU,CAACG,QAAQ,CAAC;UACpC7G,OAAO,CAAC,+CAA+C,CAAC;UACxD;QACF;;QAEA;QACA,IAAIiB,QAAQ,CAACc,cAAc,KAAK,GAAG,KAAK,CAACd,QAAQ,CAACE,aAAa,IAAIF,QAAQ,CAACE,aAAa,CAAC2F,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;UACxG7E,aAAa,CAAC;YACZ,GAAGD,UAAU;YACbb,aAAa,EAAE;UACjB,CAAC,CAAC;UACFnB,OAAO,CAAC,mCAAmC,CAAC;UAC5C;QACF;MACF;MAEAI,UAAU,CAAC,IAAI,CAAC;MAChB+C,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAIzC,UAAU,KAAK,YAAY,EAAE;QAC/B;QACA,MAAMoG,UAAU,GAAG;UACjB,GAAG9F,QAAQ;UACX;UACAE,aAAa,EAAEkC,MAAM,CAACpC,QAAQ,CAACE,aAAa,IAAI,EAAE,CAAC;UACnDC,OAAO,EAAEiC,MAAM,CAACpC,QAAQ,CAACG,OAAO,IAAI,EAAE,CAAC;UACvCC,SAAS,EAAEgC,MAAM,CAACpC,QAAQ,CAACI,SAAS,IAAI,EAAE,CAAC;UAC3CC,YAAY,EAAE+B,MAAM,CAACpC,QAAQ,CAACK,YAAY,IAAI,EAAE,CAAC;UACjDC,OAAO,EAAE8B,MAAM,CAACpC,QAAQ,CAACM,OAAO,IAAI,EAAE,CAAC;UACvCC,YAAY,EAAEwF,UAAU,CAAC/F,QAAQ,CAACO,YAAY,CAAC,IAAI,CAAC;UACpDG,iBAAiB,EAAE0B,MAAM,CAACpC,QAAQ,CAACU,iBAAiB,IAAI,KAAK,CAAC;UAC9DC,SAAS,EAAEyB,MAAM,CAACpC,QAAQ,CAACW,SAAS,IAAI,KAAK,CAAC;UAC9CC,KAAK,EAAEwB,MAAM,CAACpC,QAAQ,CAACY,KAAK,IAAI,KAAK,CAAC;UACtCC,QAAQ,EAAEb,QAAQ,CAACa,QAAQ,IAAI,IAAI;UACnC;UACAC,cAAc,EAAEsB,MAAM,CAACpC,QAAQ,CAACc,cAAc,IAAI,GAAG;QACvD,CAAC;QAEDoB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE2D,UAAU,CAAC;QAElD,IAAI;UACF;UACA5D,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEJ,IAAI,CAACC,SAAS,CAAC8D,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;UAEjF;UACA,MAAM5H,gBAAgB,CAAC8H,YAAY,CAACnH,UAAU,EAAEiH,UAAU,CAAC;UAC3DhH,SAAS,CAAC,4BAA4B,CAAC;;UAEvC;UACA2F,iBAAiB,CAAC,CAAC;;UAEnB;UACAlF,iBAAiB,CAAC,kBAAkB,CAAC;UACrCkC,UAAU,CAAC,CAAC;;UAEZ;UACA,IAAI,OAAOwE,MAAM,KAAK,WAAW,EAAE;YACjC;YACA,MAAMC,KAAK,GAAG,IAAIC,WAAW,CAAC,4BAA4B,EAAE;cAAErE,MAAM,EAAE;gBAAEjD;cAAW;YAAE,CAAC,CAAC;YACvFoH,MAAM,CAACG,aAAa,CAACF,KAAK,CAAC;UAC7B;QACF,CAAC,CAAC,OAAOtE,KAAK,EAAE;UACdM,OAAO,CAACN,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;UAEjE;UACA,IAAIC,YAAY,GAAG,0CAA0C;UAE7D,IAAID,KAAK,CAACE,MAAM,EAAE;YAChBD,YAAY,GAAG,OAAOD,KAAK,CAACE,MAAM,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM;UAC/F,CAAC,MAAM,IAAIF,KAAK,CAACK,OAAO,EAAE;YACxBJ,YAAY,GAAGD,KAAK,CAACK,OAAO;UAC9B;;UAEA;UACAC,OAAO,CAACN,KAAK,CAAC,kBAAkB,EAAEC,YAAY,CAAC;UAC/CK,OAAO,CAACN,KAAK,CAAC,eAAe,EAAEG,IAAI,CAACC,SAAS,CAAC8D,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEnE/G,OAAO,CAAC8C,YAAY,CAAC;UACrB1C,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,MAAM,IAAIO,UAAU,KAAK,gBAAgB,EAAE;QAC1C;QACA,MAAM2G,QAAQ,GAAGzG,cAAc,CAAC0G,SAAS,IAAI1G,cAAc,CAACM,aAAa,IAAIF,QAAQ,CAACE,aAAa;QAEnGgC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkE,QAAQ,CAAC;QAChDnE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEnC,QAAQ,CAAC;QAEzC,IAAI;UACF,MAAM+C,QAAQ,GAAG,MAAM7E,gBAAgB,CAACqI,YAAY,CAAC1H,UAAU,EAAEwH,QAAQ,EAAErG,QAAQ,CAAC;UACpFkC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEY,QAAQ,CAAC;UAClDjE,SAAS,CAAC,gCAAgC,CAAC;QAC7C,CAAC,CAAC,OAAO8C,KAAK,EAAE;UACdM,OAAO,CAACN,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;UAChE,IAAIC,YAAY,GAAG,yCAAyC;UAC5D,IAAID,KAAK,CAACE,MAAM,EAAE;YAChBD,YAAY,GAAG,OAAOD,KAAK,CAACE,MAAM,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM;UAC/F,CAAC,MAAM,IAAIF,KAAK,CAACK,OAAO,EAAE;YACxBJ,YAAY,GAAGD,KAAK,CAACK,OAAO;UAC9B;UACAlD,OAAO,CAAC8C,YAAY,CAAC;UACrB1C,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,MAAM,IAAIO,UAAU,KAAK,eAAe,EAAE;QACzC;QACA,MAAM2G,QAAQ,GAAGzG,cAAc,CAAC0G,SAAS,IAAI1G,cAAc,CAACM,aAAa;QACzE,MAAM6C,QAAQ,GAAG,MAAM7E,gBAAgB,CAACsI,YAAY,CAAC3H,UAAU,EAAEwH,QAAQ,CAAC;;QAE1E;QACA,IAAItD,QAAQ,CAAC0D,cAAc,EAAE;UAC3BvE,OAAO,CAACC,GAAG,CAAC,0CAA0CtD,UAAU,8BAA8B,CAAC;UAC/FC,SAAS,CAAC,2DAA2D,CAAC;UACtE;UACA;QACF,CAAC,MAAM;UACLA,SAAS,CAAC,+BAA+B,CAAC;QAC5C;MACF;MAEA2F,iBAAiB,CAAC,CAAC;MACnBhD,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,IAAIC,YAAY,GAAG,8BAA8B;MACjD,IAAID,KAAK,CAACE,MAAM,EAAE;QAChBD,YAAY,IAAI,IAAI,IAAI,OAAOD,KAAK,CAACE,MAAM,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM,CAAC;MACzG,CAAC,MAAM,IAAIF,KAAK,CAACK,OAAO,EAAE;QACxBJ,YAAY,IAAI,IAAI,GAAGD,KAAK,CAACK,OAAO;MACtC,CAAC,MAAM;QACLJ,YAAY,IAAI,sBAAsB;MACxC;MACA9C,OAAO,CAAC8C,YAAY,CAAC;MACrBK,OAAO,CAACN,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,oBACE/H,OAAA,CAACP,qBAAqB;MACpBgB,MAAM,EAAEA,MAAO;MACfF,OAAO,EAAEA,OAAQ;MACjByH,oBAAoB,EAAGC,YAAY,IAAK1E,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyE,YAAY,CAACtD,MAAM,CAAE;MAC7FuD,MAAM,EAAGlC,MAAM,IAAK;QAClB9E,iBAAiB,CAAC8E,MAAM,CAAC;QACzBhF,aAAa,CAAC,gBAAgB,CAAC;QAC/BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAE;MACFqH,QAAQ,EAAGnC,MAAM,IAAK;QACpB9E,iBAAiB,CAAC8E,MAAM,CAAC;QACzBhF,aAAa,CAAC,eAAe,CAAC;QAC9BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAE;MACFsH,aAAa,EAAGpC,MAAM,IAAK;QACzB9E,iBAAiB,CAAC8E,MAAM,CAAC;QACzBH,mBAAmB,CAAC,CAAC;QACrB7E,aAAa,CAAC,mBAAmB,CAAC;QAClCF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAE;MACFuH,UAAU,EAAGrC,MAAM,IAAK;QACtB9E,iBAAiB,CAAC8E,MAAM,CAAC;QACzB5E,qBAAqB,CAAC,IAAI,CAAC;MAC7B;IAAE;MAAAkH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEN,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI3H,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE,oBACEf,OAAA,CAAChD,MAAM;QAAC2L,IAAI,EAAE9H,UAAW;QAAC+H,OAAO,EAAE9C,iBAAkB;QAAC+C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E/I,OAAA,CAAC/C,WAAW;UAAA8L,QAAA,EACThI,UAAU,KAAK,YAAY,GAAG,mBAAmB,GAAG;QAAiB;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACdzI,OAAA,CAAC9C,aAAa;UAAA6L,QAAA,GACXhI,UAAU,KAAK,gBAAgB,iBAC9Bf,OAAA,CAACjC,KAAK;YAACiL,QAAQ,EAAC,MAAM;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBAC1C/I,OAAA,CAACvD,UAAU;cAAC2M,OAAO,EAAC,WAAW;cAACC,UAAU,EAAC,MAAM;cAAAN,QAAA,EAAC;YAElD;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzI,OAAA;cAAA+I,QAAA,gBACE/I,OAAA;gBAAA+I,QAAA,EAAI;cAA+C;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDzI,OAAA;gBAAA+I,QAAA,EAAI;cAAiD;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DzI,OAAA;gBAAA+I,QAAA,EAAI;cAAgF;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eACLzI,OAAA,CAACxD,GAAG;cAACyM,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,eACjB/I,OAAA,CAACvD,UAAU;gBAAC2M,OAAO,EAAC,OAAO;gBAAAL,QAAA,gBACzB/I,OAAA;kBAAA+I,QAAA,EAAQ;gBAAc;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,CAAAxH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEa,YAAY,KAAI,KAAK;cAAA;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EAEAa,MAAM,CAACC,IAAI,CAACjH,YAAY,CAAC,CAACqC,MAAM,GAAG,CAAC,iBACnC3E,OAAA,CAACjC,KAAK;YAACiL,QAAQ,EAAC,SAAS;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBAC7C/I,OAAA,CAACvD,UAAU;cAAC2M,OAAO,EAAC,WAAW;cAAAL,QAAA,EAAC;YAAW;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxDzI,OAAA;cAAIwJ,KAAK,EAAE;gBAAEC,MAAM,EAAE,CAAC;gBAAEC,WAAW,EAAE;cAAO,CAAE;cAAAX,QAAA,EAC3CO,MAAM,CAACK,MAAM,CAACrH,YAAY,CAAC,CAAC0C,GAAG,CAAC,CAAC2B,OAAO,EAAEiD,KAAK,kBAC9C5J,OAAA;gBAAA+I,QAAA,EAAiBpC;cAAO,GAAfiD,KAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACR,eAEDzI,OAAA,CAACpD,IAAI;YAACiN,SAAS;YAACC,OAAO,EAAE,CAAE;YAACb,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxC/I,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,eAAe;gBACpB+D,KAAK,EAAC,WAAW;gBACjBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACE,aAAc;gBAC9B4I,QAAQ,EAAElE,gBAAiB;gBAC3BmE,QAAQ,EAAErJ,UAAU,KAAK,gBAAgB,IAAKA,UAAU,KAAK,YAAY,IAAIM,QAAQ,CAACc,cAAc,KAAK,GAAK;gBAC9GkI,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACb,aAAc;gBAClC+I,UAAU,EAAE;kBACVrB,EAAE,EAAE;oBACFsB,OAAO,EAAElJ,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,SAAS,GAAG,aAAa;oBACpEkH,UAAU,EAAE;kBACd;gBACF,CAAE;gBACFmB,UAAU,EACRpI,UAAU,CAACb,aAAa,KACvBR,UAAU,KAAK,YAAY,IAAIM,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC3D,iBAAiBjC,UAAU,KAAKmB,QAAQ,CAACE,aAAa,IAAI,EAAE,6BAA6B,GACzFR,UAAU,KAAK,YAAY,IAAIM,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC5D,8DAA8DjC,UAAU,eAAe,GACvF,EAAE,CACT;gBACDuK,IAAI,EAAEpJ,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,MAAM,GAAG;cAAO;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPzI,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,SAAS;gBACd+D,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACG,OAAQ;gBACxB2I,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACZ,OAAQ;gBAC5BgJ,UAAU,EAAEpI,UAAU,CAACZ,OAAO,IAAI;cAAG;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPzI,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,WAAW;gBAChB+D,KAAK,EAAC,WAAW;gBACjBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACI,SAAU;gBAC1B0I,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACX,SAAU;gBAC9B+I,UAAU,EAAEpI,UAAU,CAACX,SAAS,IAAI;cAAG;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPzI,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,SAAS;gBACd+D,KAAK,EAAC,YAAY;gBAClBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACM,OAAQ;gBACxBwI,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACT,OAAQ;gBAC5B6I,UAAU,EAAEpI,UAAU,CAACT,OAAO,IAAI;cAAG;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPzI,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,cAAc;gBACnB+D,KAAK,EAAC,cAAc;gBACpBO,IAAI,EAAC,QAAQ;gBACb3B,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACO,YAAa;gBAC7BuI,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACR,YAAa;gBACjC4I,UAAU,EAAEpI,UAAU,CAACR,YAAY,IAAI;cAAG;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACN1H,UAAU,KAAK,gBAAgB,iBAC9Bf,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,eAAe;gBACpB+D,KAAK,EAAC,eAAe;gBACrBO,IAAI,EAAC,QAAQ;gBACb3B,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACQ,aAAc;gBAC9BsI,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRD,QAAQ,EAAE,IAAK;gBACfI,UAAU,EAAC;cAA4D;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP,eACDzI,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC3C,WAAW;gBAACyL,SAAS;gBAAAC,QAAA,gBACpB/I,OAAA,CAAC1C,UAAU;kBAACoN,EAAE,EAAC,oBAAoB;kBAAA3B,QAAA,EAAC;gBAAY;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7DzI,OAAA,CAACzC,MAAM;kBACLoN,OAAO,EAAC,oBAAoB;kBAC5BxE,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAE/E,QAAQ,CAACS,YAAa;kBAC7BoI,KAAK,EAAC,cAAc;kBACpBC,QAAQ,EAAElE,gBAAiB;kBAC3BmE,QAAQ,EAAErJ,UAAU,KAAK,YAAa;kBAAAgI,QAAA,gBAEtC/I,OAAA,CAACxC,QAAQ;oBAAC4I,KAAK,EAAC,aAAa;oBAAA2C,QAAA,EAAC;kBAAW;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpDzI,OAAA,CAACxC,QAAQ;oBAAC4I,KAAK,EAAC,QAAQ;oBAAA2C,QAAA,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CzI,OAAA,CAACxC,QAAQ;oBAAC4I,KAAK,EAAC,WAAW;oBAAA2C,QAAA,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDzI,OAAA,CAACxC,QAAQ;oBAAC4I,KAAK,EAAC,aAAa;oBAAA2C,QAAA,EAAC;kBAAW;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpDzI,OAAA,CAACxC,QAAQ;oBAAC4I,KAAK,EAAC,MAAM;oBAAA2C,QAAA,EAAC;kBAAI;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,EACR1H,UAAU,KAAK,YAAY,iBAC1Bf,OAAA,CAAC/B,cAAc;kBAAA8K,QAAA,EAAC;gBAAqD;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB,CACtF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACPzI,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,mBAAmB;gBACxB+D,KAAK,EAAC,mBAAmB;gBACzBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACU,iBAAkB;gBAClCoI,QAAQ,EAAElE,gBAAiB;gBAC3BhD,KAAK,EAAE,CAAC,CAACb,UAAU,CAACL,iBAAkB;gBACtCyI,UAAU,EAAEpI,UAAU,CAACL,iBAAiB,IAAI;cAAG;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPzI,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,WAAW;gBAChB+D,KAAK,EAAC,WAAW;gBACjBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACW,SAAU;gBAC1BmI,QAAQ,EAAElE,gBAAiB;gBAC3BhD,KAAK,EAAE,CAAC,CAACb,UAAU,CAACJ,SAAU;gBAC9BwI,UAAU,EAAEpI,UAAU,CAACJ,SAAS,IAAI;cAAG;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPzI,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,OAAO;gBACZ+D,KAAK,EAAC,YAAY;gBAClBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACY,KAAM;gBACtBkI,QAAQ,EAAElE,gBAAiB;gBAC3BhD,KAAK,EAAE,CAAC,CAACb,UAAU,CAACH,KAAM;gBAC1BuI,UAAU,EAAEpI,UAAU,CAACH,KAAK,IAAI;cAAG;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPzI,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,UAAU;gBACf+D,KAAK,EAAC,uBAAuB;gBAC7BpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACa,QAAS;gBACzBiI,QAAQ,EAAElE,gBAAiB;gBAC3B2E,WAAW,EAAC,YAAY;gBACxB3H,KAAK,EAAE,CAAC,CAACb,UAAU,CAACF,QAAS;gBAC7BsI,UAAU,EAAEpI,UAAU,CAACF,QAAQ,IAAI;cAAG;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPzI,OAAA,CAACpD,IAAI;cAACmN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvB/I,OAAA,CAAC5C,SAAS;gBACR+I,IAAI,EAAC,gBAAgB;gBACrB+D,KAAK,EAAC,yBAAsB;gBAC5BpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAE/E,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,YAAY,GAAG,SAAU;gBAClEmI,UAAU,EAAE;kBACVO,QAAQ,EAAE,IAAI;kBACd5B,EAAE,EAAE;oBACFsB,OAAO,EAAE;kBACX;gBACF,CAAE;gBACFC,UAAU,eACRxK,OAAA,CAACxD,GAAG;kBAACyM,EAAE,EAAE;oBAAEI,UAAU,EAAE,QAAQ;oBAAEyB,KAAK,EAAEzJ,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,cAAc,GAAG;kBAAY,CAAE;kBAAA4G,QAAA,EACtG1H,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC5B,mDAAmD,GACnD;gBAA6D;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChBzI,OAAA,CAAC7C,aAAa;UAAA4L,QAAA,gBACZ/I,OAAA,CAACtD,MAAM;YAACqO,OAAO,EAAEjF,iBAAkB;YAAAiD,QAAA,EAAC;UAAO;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDzI,OAAA,CAACtD,MAAM;YACLqO,OAAO,EAAElE,UAAW;YACpBuD,QAAQ,EAAE7J,OAAO,IAAI+I,MAAM,CAACC,IAAI,CAACnH,UAAU,CAAC,CAACuC,MAAM,GAAG,CAAE;YACxDqG,SAAS,EAAEzK,OAAO,gBAAGP,OAAA,CAAChC,gBAAgB;cAACiN,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGzI,OAAA,CAACd,QAAQ;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnEW,OAAO,EAAC,WAAW;YACnB0B,KAAK,EAAC,SAAS;YAAA/B,QAAA,EAChB;UAED;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI1H,UAAU,KAAK,iBAAiB,EAAE;MAC3C,oBACEf,OAAA,CAAChD,MAAM;QAAC2L,IAAI,EAAE9H,UAAW;QAAC+H,OAAO,EAAE9C,iBAAkB;QAAC+C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E/I,OAAA,CAAC/C,WAAW;UAAA8L,QAAA,EAAC;QAA8B;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzDzI,OAAA,CAAC9C,aAAa;UAAA6L,QAAA,EACXxI,OAAO,gBACNP,OAAA,CAAChC,gBAAgB;YAAAsK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBhI,MAAM,CAACkE,MAAM,KAAK,CAAC,gBACrB3E,OAAA,CAACjC,KAAK;YAACiL,QAAQ,EAAC,MAAM;YAAAD,QAAA,EAAC;UAA0B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzDzI,OAAA,CAACvC,IAAI;YAAAsL,QAAA,EACFtI,MAAM,CAACuE,GAAG,CAAEgB,MAAM,iBACjBhG,OAAA,CAACtC,QAAQ;cACPwN,MAAM;cAENH,OAAO,EAAEA,CAAA,KAAMhF,kBAAkB,CAACC,MAAM,CAAE;cAAA+C,QAAA,eAE1C/I,OAAA,CAACrC,YAAY;gBACXwN,OAAO,EAAE,WAAWnF,MAAM,CAACzE,aAAa,EAAG;gBAC3C6J,SAAS,EAAE,cAAcpF,MAAM,CAACvE,SAAS,IAAI,KAAK,eAAeuE,MAAM,CAACxE,OAAO,IAAI,KAAK,eAAewE,MAAM,CAACnE,aAAa,IAAI,KAAK;cAAK;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGzC,MAAM,CAACzE,aAAa;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBzI,OAAA,CAAC7C,aAAa;UAAA4L,QAAA,eACZ/I,OAAA,CAACtD,MAAM;YAACqO,OAAO,EAAEjF,iBAAkB;YAAAiD,QAAA,EAAC;UAAO;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI1H,UAAU,KAAK,eAAe,EAAE;MACzC,oBACEf,OAAA,CAAChD,MAAM;QAAC2L,IAAI,EAAE9H,UAAW;QAAC+H,OAAO,EAAE9C,iBAAkB;QAAC+C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E/I,OAAA,CAAC/C,WAAW;UAAA8L,QAAA,EAAC;QAAc;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzCzI,OAAA,CAAC9C,aAAa;UAAA6L,QAAA,EACX,CAAC9H,cAAc,GACdV,OAAO,gBACLP,OAAA,CAAChC,gBAAgB;YAAAsK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBhI,MAAM,CAACkE,MAAM,KAAK,CAAC,gBACrB3E,OAAA,CAACjC,KAAK;YAACiL,QAAQ,EAAC,MAAM;YAAAD,QAAA,EAAC;UAA0B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzDzI,OAAA,CAACvC,IAAI;YAAAsL,QAAA,EACFtI,MAAM,CAACuE,GAAG,CAAEgB,MAAM,iBACjBhG,OAAA,CAACtC,QAAQ;cACPwN,MAAM;cAENH,OAAO,EAAEA,CAAA,KAAM7J,iBAAiB,CAAC8E,MAAM,CAAE;cAAA+C,QAAA,eAEzC/I,OAAA,CAACrC,YAAY;gBACXwN,OAAO,EAAE,WAAWnF,MAAM,CAACzE,aAAa,EAAG;gBAC3C6J,SAAS,EAAE,cAAcpF,MAAM,CAACvE,SAAS,IAAI,KAAK,eAAeuE,MAAM,CAACxE,OAAO,IAAI,KAAK,eAAewE,MAAM,CAACnE,aAAa,IAAI,KAAK;cAAK;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGzC,MAAM,CAACzE,aAAa;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP,gBAEDzI,OAAA,CAACxD,GAAG;YAAAuM,QAAA,gBACF/I,OAAA,CAACjC,KAAK;cAACiL,QAAQ,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,GAAC,0CACC,EAAC9H,cAAc,CAACM,aAAa,EAAC,GACxE;YAAA;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzI,OAAA,CAACvD,UAAU;cAAC2M,OAAO,EAAC,OAAO;cAACH,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAE3C;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzI,OAAA,CAACjC,KAAK;cAACiL,QAAQ,EAAC,MAAM;cAACC,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACnC/I,OAAA,CAACvD,UAAU;gBAAC2M,OAAO,EAAC,WAAW;gBAACC,UAAU,EAAC,MAAM;gBAAAN,QAAA,EAAC;cAElD;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzI,OAAA;gBAAA+I,QAAA,gBACE/I,OAAA;kBAAA+I,QAAA,EAAI;gBAA0E;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFzI,OAAA;kBAAA+I,QAAA,EAAI;gBAA+C;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxDzI,OAAA;kBAAA+I,QAAA,EAAI;gBAAiD;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRzI,OAAA,CAACxD,GAAG;cAACyM,EAAE,EAAE;gBAAEoC,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE,QAAQ;gBAAEC,GAAG,EAAE,CAAC;gBAAErC,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACnE/I,OAAA,CAACvD,UAAU;gBAAC2M,OAAO,EAAC,OAAO;gBAAAL,QAAA,gBACzB/I,OAAA;kBAAA+I,QAAA,EAAQ;gBAAc;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxH,cAAc,CAACa,YAAY,IAAI,KAAK;cAAA;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACbzI,OAAA,CAACvD,UAAU;gBAAC2M,OAAO,EAAC,OAAO;gBAAAL,QAAA,gBACzB/I,OAAA;kBAAA+I,QAAA,EAAQ;gBAAa;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxH,cAAc,CAACW,YAAY,IAAI,KAAK;cAAA;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACbzI,OAAA,CAACvD,UAAU;gBAAC2M,OAAO,EAAC,OAAO;gBAAAL,QAAA,gBACzB/I,OAAA;kBAAA+I,QAAA,EAAQ;gBAAc;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxH,cAAc,CAACY,aAAa,IAAI,KAAK;cAAA;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBzI,OAAA,CAAC7C,aAAa;UAAA4L,QAAA,gBACZ/I,OAAA,CAACtD,MAAM;YAACqO,OAAO,EAAEjF,iBAAkB;YAAAiD,QAAA,EAAC;UAAO;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDxH,cAAc,iBACbjB,OAAA,CAACtD,MAAM;YACLqO,OAAO,EAAElE,UAAW;YACpBuD,QAAQ,EAAE7J,OAAQ;YAClBuK,KAAK,EAAC,OAAO;YACbE,SAAS,EAAEzK,OAAO,gBAAGP,OAAA,CAAChC,gBAAgB;cAACiN,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGzI,OAAA,CAAClB,UAAU;cAAAwJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAM,QAAA,EACtE;UAED;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI1H,UAAU,KAAK,mBAAmB,EAAE;MAC7C,oBACEf,OAAA,CAAChD,MAAM;QAAC2L,IAAI,EAAE9H,UAAW;QAAC+H,OAAO,EAAE9C,iBAAkB;QAAC+C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E/I,OAAA,CAAC/C,WAAW;UAAA8L,QAAA,EAAC;QAAuB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClDzI,OAAA,CAAC9C,aAAa;UAAA6L,QAAA,EACXxI,OAAO,gBACNP,OAAA,CAAChC,gBAAgB;YAAAsK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBjG,eAAe,CAACmC,MAAM,KAAK,CAAC,gBAC9B3E,OAAA,CAACjC,KAAK;YAACiL,QAAQ,EAAC,MAAM;YAAAD,QAAA,EAAC;UAA+B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE9DzI,OAAA,CAAC1B,cAAc;YAACkN,SAAS,EAAE7O,KAAM;YAACsM,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,eAC9C/I,OAAA,CAAC7B,KAAK;cAAC8M,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjB/I,OAAA,CAACzB,SAAS;gBAAAwK,QAAA,eACR/I,OAAA,CAACxB,QAAQ;kBAAAuK,QAAA,gBACP/I,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7BzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAC;kBAAO;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChCzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAC;kBAAU;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACjCzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAC;kBAAY;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnCzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAC;kBAAa;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpCzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAC;kBAAc;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZzI,OAAA,CAAC5B,SAAS;gBAAA2K,QAAA,EACPvG,eAAe,CAACwC,GAAG,CAAC,CAACyG,MAAM,EAAE7B,KAAK,kBACjC5J,OAAA,CAACxB,QAAQ;kBAAAuK,QAAA,gBACP/I,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAE0C,MAAM,CAAClK;kBAAa;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7CzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAE0C,MAAM,CAACjK;kBAAO;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvCzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAE0C,MAAM,CAAChK;kBAAS;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzCzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAE0C,MAAM,CAAC9J;kBAAO;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvCzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAE0C,MAAM,CAAC7J;kBAAY;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5CzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAE0C,MAAM,CAAC5J;kBAAa;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7CzI,OAAA,CAAC3B,SAAS;oBAAA0K,QAAA,EAAE0C,MAAM,CAACC,IAAI,CAAC/G;kBAAM;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAP9BmB,KAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBzI,OAAA,CAAC7C,aAAa;UAAA4L,QAAA,eACZ/I,OAAA,CAACtD,MAAM;YAACqO,OAAO,EAAEjF,iBAAkB;YAAAiD,QAAA,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACEzI,OAAA,CAACxD,GAAG;IAAAuM,QAAA,GACDpI,cAAc,KAAK,kBAAkB,IAAI,CAACE,UAAU,gBACnDb,OAAA,CAACrD,KAAK;MAACsM,EAAE,EAAE;QAAE0C,CAAC,EAAE;MAAE,CAAE;MAAA5C,QAAA,gBAClB/I,OAAA,CAACxD,GAAG;QAACyM,EAAE,EAAE;UAAEoC,OAAO,EAAE,MAAM;UAAEO,cAAc,EAAE,UAAU;UAAEC,UAAU,EAAE,QAAQ;UAAE3C,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eACpF/I,OAAA,CAACxD,GAAG;UAACyM,EAAE,EAAE;YAAEoC,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAE,CAAE;UAAAxC,QAAA,gBACnC/I,OAAA,CAACtD,MAAM;YACL0M,OAAO,EAAC,UAAU;YAClB0B,KAAK,EAAC,SAAS;YACfE,SAAS,eAAEhL,OAAA,CAAChB,WAAW;cAAAsJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BsC,OAAO,EAAEA,CAAA,KAAM;cACblF,mBAAmB,CAAC,CAAC;cACrB7E,aAAa,CAAC,mBAAmB,CAAC;cAClCF,aAAa,CAAC,IAAI,CAAC;YACrB,CAAE;YACFmI,EAAE,EAAE;cACFI,UAAU,EAAE,QAAQ;cACpByC,YAAY,EAAE;YAChB,CAAE;YAAA/C,QAAA,EACH;UAED;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzI,OAAA,CAACtD,MAAM;YACL0M,OAAO,EAAC,WAAW;YACnB0B,KAAK,EAAC,SAAS;YACfE,SAAS,eAAEhL,OAAA,CAACtB,OAAO;cAAA4J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBsC,OAAO,EAAEA,CAAA,KAAM/G,qBAAqB,CAAC,CAAE;YACvCiF,EAAE,EAAE;cACFI,UAAU,EAAE,QAAQ;cACpByC,YAAY,EAAE;YAChB,CAAE;YAAA/C,QAAA,EACH;UAED;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLlI,OAAO,gBACNP,OAAA,CAACxD,GAAG;QAACyM,EAAE,EAAE;UAAEoC,OAAO,EAAE,MAAM;UAAEO,cAAc,EAAE,QAAQ;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAhD,QAAA,eAC5D/I,OAAA,CAAChC,gBAAgB;UAAAsK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,gBAENzI,OAAA,CAACP,qBAAqB;QACpBgB,MAAM,EAAEA,MAAO;QACfF,OAAO,EAAEA,OAAQ;QACjByH,oBAAoB,EAAGC,YAAY,IAAK1E,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyE,YAAY,CAACtD,MAAM,CAAE;QAC7FuD,MAAM,EAAGlC,MAAM,IAAK;UAClB9E,iBAAiB,CAAC8E,MAAM,CAAC;UACzBhF,aAAa,CAAC,gBAAgB,CAAC;UAC/BF,aAAa,CAAC,IAAI,CAAC;QACrB,CAAE;QACFqH,QAAQ,EAAGnC,MAAM,IAAK;UACpB9E,iBAAiB,CAAC8E,MAAM,CAAC;UACzBhF,aAAa,CAAC,eAAe,CAAC;UAC9BF,aAAa,CAAC,IAAI,CAAC;QACrB,CAAE;QACFsH,aAAa,EAAGpC,MAAM,IAAK;UACzB9E,iBAAiB,CAAC8E,MAAM,CAAC;UACzBH,mBAAmB,CAAC,CAAC;UACrB7E,aAAa,CAAC,mBAAmB,CAAC;UAClCF,aAAa,CAAC,IAAI,CAAC;QACrB,CAAE;QACFuH,UAAU,EAAGrC,MAAM,IAAK;UACtB9E,iBAAiB,CAAC8E,MAAM,CAAC;UACzB5E,qBAAqB,CAAC,IAAI,CAAC;QAC7B;MAAE;QAAAkH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,CAAC5H,UAAU,gBACbb,OAAA,CAACrD,KAAK;MAACsM,EAAE,EAAE;QAAE0C,CAAC,EAAE,CAAC;QAAEK,SAAS,EAAE,OAAO;QAAEX,OAAO,EAAE,MAAM;QAAEQ,UAAU,EAAE,QAAQ;QAAED,cAAc,EAAE;MAAS,CAAE;MAAA7C,QAAA,EACtG,CAACpI,cAAc,gBACdX,OAAA,CAACvD,UAAU;QAAC2M,OAAO,EAAC,OAAO;QAAAL,QAAA,EAAC;MAE5B;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEbzI,OAAA,CAACxD,GAAG;QAACyM,EAAE,EAAE;UAAEgD,SAAS,EAAE;QAAS,CAAE;QAAAlD,QAAA,gBAC/B/I,OAAA,CAACvD,UAAU;UAAC2M,OAAO,EAAC,IAAI;UAAC8C,YAAY;UAAAnD,QAAA,GAClCpI,cAAc,KAAK,YAAY,IAAI,mBAAmB,EACtDA,cAAc,KAAK,gBAAgB,IAAI,iBAAiB,EACxDA,cAAc,KAAK,eAAe,IAAI,gBAAgB,EACtDA,cAAc,KAAK,mBAAmB,IAAI,6BAA6B;QAAA;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACbzI,OAAA,CAAChC,gBAAgB;UAACiL,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,IAAI,EAEPC,YAAY,CAAC,CAAC,eAGf1I,OAAA,CAACR,oBAAoB;MACnBmJ,IAAI,EAAEjG,gBAAiB;MACvBkG,OAAO,EAAEA,CAAA,KAAMjG,mBAAmB,CAAC,KAAK,CAAE;MAC1CwJ,SAAS,EAAE5G;IAAoB;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eAGFzI,OAAA,CAACN,oBAAoB;MACnBiJ,IAAI,EAAExH,kBAAmB;MACzByH,OAAO,EAAEA,CAAA,KAAM;QACbxH,qBAAqB,CAAC,KAAK,CAAC;QAC5B;QACA0B,UAAU,CAAC,CAAC;MACd,CAAE;MACFkD,MAAM,EAAE/E,cAAe;MACvBf,UAAU,EAAEA,UAAW;MACvBC,SAAS,EAAEA,SAAU;MACrBC,OAAO,EAAEA;IAAQ;MAAAkI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnI,EAAA,CAjpCIL,SAAS;AAAAmM,EAAA,GAATnM,SAAS;AAmpCf,eAAeA,SAAS;AAAC,IAAAmM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}