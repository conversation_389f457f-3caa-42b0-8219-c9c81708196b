import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  CircularProgress,
  Alert,
  Chip,
  IconButton,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Card,
  CardContent,
  CardHeader,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Warning as WarningIcon,
  Search as SearchIcon,
  Cable as CableIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import caviService from '../../services/caviService';
import { isCableInstalled } from '../../utils/stateUtils';
import IncompatibleReelDialog from './IncompatibleReelDialog';

// Utility per verificare compatibilità tra cavo e bobina
const isCompatible = (cavo, bobina) => {
  return cavo.tipologia === bobina.tipologia &&
         String(cavo.sezione) === String(bobina.sezione);
};

// Utility per ottenere il numero della bobina dall'ID
const getBobinaNumber = (idBobina) => {
  if (!idBobina) return '';
  const parts = idBobina.split('-');
  return parts.length > 1 ? parts[1] : idBobina;
};

/**
 * Componente per aggiungere rapidamente più cavi a una bobina
 * Implementa sistema di doppia lista per cavi compatibili/incompatibili
 * con gestione intelligente delle incompatibilità e dialog moderni
 */
const QuickAddCablesDialog = ({ open, onClose, bobina, cantiereId, onSuccess, onError }) => {
  // Stati per la gestione dei dati
  const [loading, setLoading] = useState(false);
  const [caviLoading, setCaviLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Stati per i dati separati
  const [allCavi, setAllCavi] = useState([]);
  const [caviCompatibili, setCaviCompatibili] = useState([]);
  const [caviIncompatibili, setCaviIncompatibili] = useState([]);
  const [selectedCavi, setSelectedCavi] = useState([]);
  const [caviMetri, setCaviMetri] = useState({});

  // Stati per la UI
  const [activeTab, setActiveTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);
  const [incompatibleSelection, setIncompatibleSelection] = useState(null);
  const [showOverDialog, setShowOverDialog] = useState(false);
  const [overDialogData, setOverDialogData] = useState(null);

  // Stati per la validazione
  const [errors, setErrors] = useState({});
  const [warnings, setWarnings] = useState({});

  // Carica i cavi disponibili quando il dialog viene aperto
  useEffect(() => {
    if (open && bobina && cantiereId) {
      loadCavi();
      // Reset stati quando si apre il dialog
      resetDialogState();
    }
  }, [open, bobina, cantiereId]);

  // Early return se bobina è null per evitare errori
  if (!bobina) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle>Errore</DialogTitle>
        <DialogContent>
          <Alert severity="error">Nessuna bobina selezionata</Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Chiudi</Button>
        </DialogActions>
      </Dialog>
    );
  }

  // Reset dello stato del dialog
  const resetDialogState = () => {
    setSelectedCavi([]);
    setCaviMetri({});
    setErrors({});
    setWarnings({});
    setSearchTerm('');
    setActiveTab(0);
    setShowIncompatibleDialog(false);
    setIncompatibleSelection(null);
    setShowOverDialog(false);
    setOverDialogData(null);
  };

  // Funzione per caricare i cavi con sistema di doppia lista
  const loadCavi = async () => {
    try {
      setCaviLoading(true);
      const caviData = await caviService.getCavi(cantiereId);

      // Filtra i cavi disponibili (non installati e non SPARE)
      const caviDisponibili = caviData.filter(cavo =>
        !isCableInstalled(cavo) &&
        cavo.modificato_manualmente !== 3
      );

      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)
      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));
      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));

      setAllCavi(caviDisponibili);
      setCaviCompatibili(compatibili);
      setCaviIncompatibili(incompatibili);

      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);
    } catch (error) {
      console.error('Errore nel caricamento dei cavi:', error);
      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setCaviLoading(false);
    }
  };

  // Gestisce la selezione di un cavo compatibile
  const handleCompatibleCavoSelect = (cavo) => {
    setSelectedCavi(prev => {
      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);

      if (isSelected) {
        // Conferma prima di rimuovere se ci sono metri inseriti
        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';
        if (hasMetri) {
          if (!window.confirm(`Rimuovere il cavo ${cavo.id_cavo} dalla selezione? I metri inseriti (${caviMetri[cavo.id_cavo]}m) andranno persi.`)) {
            return prev;
          }
        }

        // Rimuovi il cavo dalla selezione
        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);

        // Rimuovi anche i metri associati
        const newCaviMetri = { ...caviMetri };
        delete newCaviMetri[cavo.id_cavo];
        setCaviMetri(newCaviMetri);

        // Rimuovi errori e warning
        setErrors(prevErrors => {
          const newErrors = { ...prevErrors };
          delete newErrors[cavo.id_cavo];
          return newErrors;
        });
        setWarnings(prevWarnings => {
          const newWarnings = { ...prevWarnings };
          delete newWarnings[cavo.id_cavo];
          return newWarnings;
        });

        return newSelected;
      } else {
        // Aggiungi il cavo alla selezione
        return [...prev, cavo];
      }
    });
  };

  // Gestisce la selezione di un cavo incompatibile
  const handleIncompatibleCavoSelect = (cavo) => {
    setIncompatibleSelection({ cavo, bobina });
    setShowIncompatibleDialog(true);
  };

  // Gestisce l'uso di una bobina incompatibile
  const handleUseIncompatibleReel = () => {
    if (incompatibleSelection) {
      const { cavo } = incompatibleSelection;

      // Aggiungi il cavo alla selezione con flag di incompatibilità
      setSelectedCavi(prev => [...prev, { ...cavo, _isIncompatible: true }]);

      setShowIncompatibleDialog(false);
      setIncompatibleSelection(null);

      onSuccess?.(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione. Sarà utilizzato con force_over.`);
    }
  };

  // Gestisce l'input dei metri posati per un cavo
  const handleMetriChange = (cavoId, value) => {
    // Aggiorna i metri per il cavo
    setCaviMetri(prev => ({
      ...prev,
      [cavoId]: value
    }));

    // Valida il valore inserito in tempo reale
    validateMetri(cavoId, value);
  };

  // Valida i metri inseriti per un cavo con feedback migliorato
  const validateMetri = (cavoId, value) => {
    const cavo = allCavi.find(c => c.id_cavo === cavoId);
    if (!cavo) return;

    // Resetta gli errori e gli avvisi per questo cavo
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[cavoId];
      return newErrors;
    });

    setWarnings(prev => {
      const newWarnings = { ...prev };
      delete newWarnings[cavoId];
      return newWarnings;
    });

    // Controllo input vuoto
    if (!value || value.trim() === '') {
      return true; // Non mostrare errore per input vuoto durante la digitazione
    }

    // Controllo formato numerico
    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {
      setErrors(prev => ({
        ...prev,
        [cavoId]: 'Inserire un valore numerico positivo'
      }));
      return false;
    }

    const metriPosati = parseFloat(value);

    // Controllo metri teorici cavo
    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {
      setWarnings(prev => ({
        ...prev,
        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`
      }));
    }

    // Controllo metri residui bobina con calcolo in tempo reale
    const metriTotaliRichiesti = Object.entries(caviMetri)
      .filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente
      .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;

    const metriResiduiBobina = bobina?.metri_residui || 0;
    if (metriTotaliRichiesti > metriResiduiBobina) {
      setWarnings(prev => ({
        ...prev,
        [cavoId]: `Totale richiesto: ${metriTotaliRichiesti.toFixed(1)}m > Residui bobina: ${metriResiduiBobina.toFixed(1)}m (OVER)`
      }));
    }

    return true;
  };

  // Valida tutti i metri inseriti con dialog moderno per OVER
  const validateAllMetri = () => {
    let isValid = true;
    const newErrors = {};
    const newWarnings = {};

    // Verifica che ci siano cavi selezionati
    if (selectedCavi.length === 0) {
      onError('Seleziona almeno un cavo');
      return false;
    }

    // Verifica che tutti i cavi selezionati abbiano metri inseriti
    for (const cavo of selectedCavi) {
      const metri = caviMetri[cavo.id_cavo];

      // Controllo input vuoto
      if (!metri || metri.trim() === '') {
        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';
        isValid = false;
        continue;
      }

      // Controllo formato numerico
      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {
        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';
        isValid = false;
        continue;
      }

      const metriPosati = parseFloat(metri);

      // Controllo metri teorici cavo
      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {
        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;
      }
    }

    setErrors(newErrors);
    setWarnings(newWarnings);

    // Se ci sono errori, non procedere
    if (!isValid) {
      return false;
    }

    // Verifica stato OVER della bobina
    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);
    const metriResiduiBobina = bobina?.metri_residui || 0;
    if (metriTotaliRichiesti > metriResiduiBobina) {
      // Mostra dialog moderno invece di window.confirm
      setOverDialogData({
        metriTotaliRichiesti,
        metriResiduiBobina,
        selectedCavi: selectedCavi.length
      });
      setShowOverDialog(true);
      return false; // Interrompi qui, il salvataggio continuerà dal dialog
    }

    return true;
  };

  // Gestisce il salvataggio dei dati con gestione migliorata
  const handleSave = async (forceOver = false) => {
    try {
      // Validazione solo se non è un force over
      if (!forceOver && !validateAllMetri()) {
        return;
      }

      setSaving(true);

      // Aggiorna ogni cavo selezionato
      const results = [];
      let errors = [];

      for (const cavo of selectedCavi) {
        try {
          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);

          // Determina se è necessario forzare lo stato OVER della bobina o incompatibilità
          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);
          const metriResiduiBobina = bobina?.metri_residui || 0;
          const needsForceOver = forceOver || (metriGiàUtilizzati + metriPosati) > metriResiduiBobina || cavo._isIncompatible;

          // Aggiorna i metri posati del cavo
          const result = await caviService.updateMetriPosati(
            cantiereId,
            cavo.id_cavo,
            metriPosati,
            bobina.id_bobina,
            needsForceOver
          );

          results.push({
            cavo: cavo.id_cavo,
            metriPosati,
            success: true,
            wasIncompatible: cavo._isIncompatible
          });
        } catch (error) {
          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);
          errors.push({
            cavo: cavo.id_cavo,
            error: error.message || 'Errore sconosciuto'
          });
        }
      }

      // Gestione del risultato con messaggi migliorati
      if (errors.length === 0) {
        const incompatibleCount = results.filter(r => r.wasIncompatible).length;
        let message = `${results.length} cavi aggiornati con successo`;
        if (incompatibleCount > 0) {
          message += ` (${incompatibleCount} incompatibili con force_over)`;
        }
        onSuccess(message);
        onClose();
      } else if (results.length > 0) {
        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);
        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);
        onClose();
      } else {
        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);
      }
    } catch (error) {
      console.error('Errore durante il salvataggio:', error);
      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setSaving(false);
    }
  };

  // Gestisce la conferma del dialog OVER
  const handleOverDialogConfirm = () => {
    setShowOverDialog(false);
    setOverDialogData(null);
    handleSave(true); // Procedi con force_over
  };

  // Filtra i cavi in base al termine di ricerca
  const filterCavi = (caviList) => {
    if (!searchTerm) return caviList;

    return caviList.filter(cavo =>
      cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const filteredCompatibili = filterCavi(caviCompatibili);
  const filteredIncompatibili = filterCavi(caviIncompatibili);

  // Calcola statistiche con controlli di sicurezza
  const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => {
    const value = parseFloat(metri || 0);
    return isNaN(value) ? sum : sum + value;
  }, 0);

  const metriResiduiBobina = bobina?.metri_residui || 0;
  const isOverState = metriTotaliRichiesti > metriResiduiBobina;

  // Componente per renderizzare una lista di cavi
  const renderCaviList = (caviList, isCompatible = true) => {
    if (caviLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (caviList.length === 0) {
      return (
        <Alert severity="info" sx={{ my: 2 }}>
          {isCompatible ? 'Nessun cavo compatibile disponibile.' : 'Nessun cavo incompatibile trovato.'}
        </Alert>
      );
    }

    return (
      <List dense>
        {caviList.map((cavo) => {
          const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);
          const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';
          const hasError = errors[cavo.id_cavo];
          const hasWarning = warnings[cavo.id_cavo];

          return (
            <ListItem
              key={cavo.id_cavo}
              sx={{
                border: '1px solid #e0e0e0',
                borderRadius: 1,
                mb: 1,
                bgcolor: isSelected ? 'rgba(33, 150, 243, 0.1)' : '#f5f7fa',
                '&:hover': {
                  bgcolor: isSelected ? 'rgba(33, 150, 243, 0.2)' : 'rgba(33, 150, 243, 0.05)'
                }
              }}
            >
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CableIcon fontSize="small" color={isCompatible ? 'success' : 'warning'} />
                    <Typography variant="subtitle2">{cavo.id_cavo}</Typography>
                    <Chip
                      size="small"
                      label={cavo.tipologia || 'N/A'}
                      color={isCompatible ? 'success' : 'warning'}
                      variant="outlined"
                    />
                    {!isCompatible && (
                      <Chip
                        size="small"
                        label="INCOMPATIBILE"
                        color="error"
                        variant="filled"
                      />
                    )}
                  </Box>
                }
                secondary={
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      {cavo.ubicazione_partenza || 'N/A'} → {cavo.ubicazione_arrivo || 'N/A'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Formazione: {cavo.sezione || 'N/A'} | Metri teorici: {cavo.metri_teorici || 'N/A'}
                    </Typography>
                    {isSelected && (
                      <Box sx={{ mt: 1 }}>
                        <TextField
                          size="small"
                          type="number"
                          label="Metri posati"
                          value={caviMetri[cavo.id_cavo] || ''}
                          onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}
                          error={!!hasError}
                          helperText={hasError || hasWarning}
                          FormHelperTextProps={{
                            sx: { color: hasWarning && !hasError ? 'warning.main' : 'error.main' }
                          }}
                          sx={{ width: '200px' }}
                        />
                      </Box>
                    )}
                  </Box>
                }
              />
              <ListItemSecondaryAction>
                <Button
                  size="small"
                  variant={isSelected ? 'contained' : 'outlined'}
                  color={isCompatible ? 'primary' : 'warning'}
                  onClick={() => isCompatible ? handleCompatibleCavoSelect(cavo) : handleIncompatibleCavoSelect(cavo)}
                  startIcon={isSelected ? <CheckCircleIcon /> : <AddIcon />}
                >
                  {isSelected ? 'Selezionato' : 'Seleziona'}
                </Button>
              </ListItemSecondaryAction>
            </ListItem>
          );
        })}
      </List>
    );
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xl" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <CableIcon />
          <Typography variant="h6">
            Aggiungi cavi alla bobina {getBobinaNumber(bobina?.id_bobina)}
          </Typography>
        </Box>
      </DialogTitle>
      <DialogContent>
        {/* Informazioni sulla bobina */}
            <Card sx={{ mb: 3 }}>
              <CardHeader
                title="Dettagli bobina"
                titleTypographyProps={{ variant: 'subtitle1' }}
              />
              <CardContent sx={{ pt: 0 }}>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">ID Bobina</Typography>
                    <Typography variant="body1">{bobina.id_bobina}</Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">Tipologia</Typography>
                    <Typography variant="body1">{bobina.tipologia || 'N/A'}</Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">Formazione</Typography>
                    <Typography variant="body1">{bobina.sezione || 'N/A'}</Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">Metri residui</Typography>
                    <Typography
                      variant="body1"
                      color={isOverState ? 'error.main' : 'text.primary'}
                      sx={{ fontWeight: isOverState ? 'bold' : 'normal' }}
                    >
                      {metriResiduiBobina.toFixed(1)} m
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">Stato</Typography>
                    <Chip
                      label={bobina.stato_bobina || 'N/D'}
                      size="small"
                      color={
                        bobina.stato_bobina === 'Disponibile' ? 'success' :
                        bobina.stato_bobina === 'In uso' ? 'primary' :
                        bobina.stato_bobina === 'Over' ? 'error' :
                        bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'
                      }
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>

            {/* Ricerca cavi */}
            <TextField
              fullWidth
              label="Ricerca intelligente cavi"
              variant="outlined"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Cerca per ID, tipologia, ubicazione..."
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
              sx={{ mb: 3 }}
            />

            {/* Sistema di tabs per cavi compatibili/incompatibili */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
                <Tab
                  label={
                    <Badge badgeContent={filteredCompatibili.length} color="success">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckCircleIcon fontSize="small" />
                        Cavi compatibili
                      </Box>
                    </Badge>
                  }
                />
                <Tab
                  label={
                    <Badge badgeContent={filteredIncompatibili.length} color="warning">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <WarningIcon fontSize="small" />
                        Cavi incompatibili
                      </Box>
                    </Badge>
                  }
                />
              </Tabs>
            </Box>

            {/* Contenuto dei tabs */}
            <Box sx={{ minHeight: 300, maxHeight: 400, overflow: 'auto' }}>
              {activeTab === 0 && (
                <Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Cavi compatibili con tipologia <strong>{bobina.tipologia}</strong> e formazione <strong>{bobina.sezione}</strong>
                  </Typography>
                  {renderCaviList(filteredCompatibili, true)}
                </Box>
              )}
              {activeTab === 1 && (
                <Box>
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      I cavi incompatibili possono essere utilizzati con <strong>force_over</strong>,
                      ma potrebbero non rispettare le specifiche tecniche della bobina.
                    </Typography>
                  </Alert>
                  {renderCaviList(filteredIncompatibili, false)}
                </Box>
              )}
            </Box>

            {/* Riepilogo selezione migliorato */}
            {selectedCavi.length > 0 && (
              <Card sx={{ mt: 3, mb: 2 }}>
                <CardHeader
                  title={`Riepilogo selezione (${selectedCavi.length} cavi)`}
                  titleTypographyProps={{ variant: 'subtitle1' }}
                />
                <CardContent sx={{ pt: 0 }}>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Metri totali richiesti:</Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 'bold',
                          color: isOverState ? 'error.main' : 'text.primary'
                        }}
                      >
                        {metriTotaliRichiesti.toFixed(1)} m
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Metri residui bobina:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {metriResiduiBobina.toFixed(1)} m
                      </Typography>
                    </Box>
                    <Divider sx={{ my: 1 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Differenza:</Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 'bold',
                          color: isOverState ? 'error.main' : 'success.main'
                        }}
                      >
                        {(metriResiduiBobina - metriTotaliRichiesti).toFixed(1)} m
                        {isOverState && ' (OVER)'}
                      </Typography>
                    </Box>
                  </Box>

                  {isOverState && (
                    <Alert severity="warning" sx={{ mb: 2 }}>
                      <Typography variant="body2">
                        <strong>ATTENZIONE:</strong> I metri richiesti superano i metri residui della bobina.
                        La bobina andrà in stato OVER.
                      </Typography>
                    </Alert>
                  )}

                  <List dense>
                    {selectedCavi.map((cavo) => (
                      <ListItem
                        key={cavo.id_cavo}
                        sx={{
                          border: '1px solid #e0e0e0',
                          borderRadius: 1,
                          mb: 1,
                          bgcolor: cavo._isIncompatible ? 'warning.light' : 'background.paper'
                        }}
                      >
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle2">{cavo.id_cavo}</Typography>
                              {cavo._isIncompatible && (
                                <Chip size="small" label="INCOMPATIBILE" color="warning" />
                              )}
                            </Box>
                          }
                          secondary={`${caviMetri[cavo.id_cavo] || '0'} metri posati`}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleCompatibleCavoSelect(cavo)}
                            title="Rimuovi dalla selezione"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            )}

            {/* Avvisi globali */}
            {Object.keys(warnings).length > 0 && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>Avvisi:</Typography>
                <List dense>
                  {Object.entries(warnings).map(([cavoId, warning]) => (
                    <ListItem key={cavoId} sx={{ py: 0 }}>
                      <ListItemText
                        primary={`${cavoId}: ${warning}`}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Alert>
            )}

            {/* Istruzioni */}
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Istruzioni:</strong> Seleziona i cavi dalle liste sopra e inserisci i metri posati.
                I cavi compatibili sono consigliati, quelli incompatibili richiedono conferma esplicita.
              </Typography>
            </Alert>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={onClose} disabled={saving}>
          Annulla
        </Button>
        <Button
          onClick={() => handleSave(false)}
          color="primary"
          variant="contained"
          disabled={saving || selectedCavi.length === 0 || Object.keys(errors).length > 0}
          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
        >
          {saving ? 'Salvataggio...' : `Salva ${selectedCavi.length} cavi`}
        </Button>
      </DialogActions>

      {/* Dialog per incompatibilità */}
      <IncompatibleReelDialog
        open={showIncompatibleDialog}
        onClose={() => {
          setShowIncompatibleDialog(false);
          setIncompatibleSelection(null);
        }}
        cavo={incompatibleSelection?.cavo}
        bobina={incompatibleSelection?.bobina}
        onConfirm={handleUseIncompatibleReel}
      />

      {/* Dialog moderno per conferma OVER */}
      <Dialog
        open={showOverDialog}
        onClose={() => setShowOverDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WarningIcon color="warning" />
            <Typography variant="h6">Conferma stato OVER</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          {overDialogData && (
            <>
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="body1" gutterBottom>
                  <strong>ATTENZIONE:</strong> L'operazione porterà la bobina in stato OVER.
                </Typography>
              </Alert>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" gutterBottom>
                  <strong>Metri totali richiesti:</strong> {overDialogData.metriTotaliRichiesti.toFixed(1)} m
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Metri residui bobina:</strong> {overDialogData.metriResiduiBobina.toFixed(1)} m
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Eccedenza:</strong> {(overDialogData.metriTotaliRichiesti - overDialogData.metriResiduiBobina).toFixed(1)} m
                </Typography>
                <Typography variant="body2">
                  <strong>Cavi coinvolti:</strong> {overDialogData.selectedCavi}
                </Typography>
              </Box>
              <Typography variant="body2">
                Vuoi continuare con l'operazione? La bobina andrà in stato OVER.
              </Typography>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowOverDialog(false)}>
            Annulla
          </Button>
          <Button
            onClick={handleOverDialogConfirm}
            color="warning"
            variant="contained"
            startIcon={<WarningIcon />}
          >
            Continua con OVER
          </Button>
        </DialogActions>
      </Dialog>
    </Dialog>
  );
};

export default QuickAddCablesDialog;
