{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriDialogCompleto.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Typography, Box, Grid, Paper, Alert, CircularProgress, FormControl, InputLabel, Select, MenuItem, Chip } from '@mui/material';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\n\n/**\n * Dialog completo per inserire i metri posati di un cavo preselezionato\n * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Cavo preselezionato\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriDialogCompleto = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  cantiereId,\n  onSuccess = () => {},\n  onError = () => {},\n  loading = false\n}) => {\n  _s();\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n\n  // Stati per bobine\n  const [bobine, setBobine] = useState([]);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per dialoghi di conferma\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    open: false,\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      var _cavo$metratura_reale;\n      setFormData({\n        metri_posati: ((_cavo$metratura_reale = cavo.metratura_reale) === null || _cavo$metratura_reale === void 0 ? void 0 : _cavo$metratura_reale.toString()) || '',\n        id_bobina: ''\n      });\n      setFormErrors({});\n      setFormWarnings({});\n      setSaving(false);\n\n      // Carica le bobine disponibili\n      loadBobine();\n    }\n  }, [open, cavo, cantiereId]);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    if (!cantiereId || !cavo) return;\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId, 'e cavo:', cavo.id_cavo);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n\n      // Filtra le bobine compatibili con il cavo\n      const bobineCompatibili = bobineData.filter(bobina => {\n        const tipologiaCompatibile = !cavo.tipologia || !bobina.tipologia || cavo.tipologia.toLowerCase() === bobina.tipologia.toLowerCase();\n        const sezioneCompatibile = !cavo.sezione || !bobina.sezione || cavo.sezione.toLowerCase() === bobina.sezione.toLowerCase();\n        return tipologiaCompatibile && sezioneCompatibile && bobina.metri_residui > 0;\n      });\n      console.log('Bobine compatibili filtrate:', bobineCompatibili);\n      setBobine(bobineCompatibili || []);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      setBobine([]);\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce i cambiamenti del form\n  const handleFormChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Rimuovi errori quando l'utente inizia a digitare\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Validazione in tempo reale per metri posati\n    if (name === 'metri_posati' && value && cavo) {\n      const metri = parseFloat(value);\n      if (!isNaN(metri) && metri > cavo.metri_teorici) {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n        }));\n      } else {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: ''\n        }));\n      }\n    }\n  };\n\n  // Validazione del form\n  const validateForm = () => {\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'I metri posati sono obbligatori';\n    } else {\n      const metri = parseFloat(formData.metri_posati);\n      if (isNaN(metri) || metri < 0) {\n        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';\n      }\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina || formData.id_bobina === '') {\n      // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n      if (bobine.length === 0 && !bobineLoading) {\n        setFormData(prev => ({\n          ...prev,\n          id_bobina: 'BOBINA_VUOTA'\n        }));\n      } else {\n        errors.id_bobina = 'È necessario selezionare una bobina';\n      }\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      setSaving(true);\n      const metriPosati = parseFloat(formData.metri_posati);\n      let idBobina = formData.id_bobina;\n\n      // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n      if (bobine.length === 0 && !bobineLoading) {\n        idBobina = 'BOBINA_VUOTA';\n      }\n\n      // Assicurati che BOBINA_VUOTA venga passato come stringa\n      if (idBobina === 'BOBINA_VUOTA') {\n        idBobina = 'BOBINA_VUOTA';\n      }\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', cavo.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina);\n\n      // Chiamata API con la funzione originale completa\n      await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Successo\n      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;\n      onSuccess(successMessage);\n\n      // Chiudi il dialog\n      handleClose();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n        handleClose();\n        return;\n      }\n\n      // Gestione errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleClose = () => {\n    if (!saving && !loading) {\n      setFormErrors({});\n      setFormWarnings({});\n      setFormData({\n        metri_posati: '',\n        id_bobina: ''\n      });\n      onClose();\n    }\n  };\n  const handleKeyPress = event => {\n    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {\n      handleSave();\n    }\n  };\n  if (!cavo) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: handleClose,\n      maxWidth: \"md\",\n      fullWidth: true,\n      disableEscapeKeyDown: saving || loading,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Inserisci Metri Posati - \", cavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: [/*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            mb: 3,\n            bgcolor: 'grey.50'\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 45\n                }, this), \" \", cavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 45\n                }, this), \" \", cavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 45\n                }, this), \" \", cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Da:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 45\n                }, this), \" \", cavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"A:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 45\n                }, this), \" \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Attualmente posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 45\n                }, this), \" \", cavo.metratura_reale || 0, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            autoFocus: true,\n            label: \"Metri Posati\",\n            type: \"number\",\n            fullWidth: true,\n            name: \"metri_posati\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            onKeyPress: handleKeyPress,\n            error: Boolean(formErrors.metri_posati),\n            helperText: formErrors.metri_posati || formWarnings.metri_posati || `Metri teorici: ${cavo.metri_teorici}m`,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            disabled: saving || loading,\n            InputProps: {\n              endAdornment: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 31\n              }, this)\n            },\n            inputProps: {\n              max: 999999,\n              step: 0.1\n            },\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            error: Boolean(formErrors.id_bobina),\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              label: \"Bobina\",\n              disabled: saving || loading || bobineLoading,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"VUOTA\",\n                    size: \"small\",\n                    color: \"warning\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    children: \"BOBINA VUOTA (Posa senza bobina specifica)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.metri_residui ? `${bobina.metri_residui.toFixed(1)}m` : '0m',\n                    size: \"small\",\n                    color: bobina.metri_residui > 0 ? 'success' : 'error',\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    children: [bobina.id_bobina, \" - \", bobina.tipologia || 'N/A', \" \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this)\n              }, bobina.id_bobina, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              sx: {\n                mt: 0.5,\n                ml: 1.5\n              },\n              children: formErrors.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), bobineLoading && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Caricamento bobine...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this), !bobineLoading && bobine.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 1\n            },\n            children: \"Nessuna bobina compatibile disponibile. Verr\\xE0 utilizzata BOBINA_VUOTA.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          disabled: saving || loading,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"contained\",\n          disabled: saving || loading || !formData.metri_posati || !formData.id_bobina,\n          startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 33\n          }, this) : null,\n          children: saving ? 'Salvando...' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(InserisciMetriDialogCompleto, \"aaB+RYyohAk64KOBNhtrhsvl1nQ=\");\n_c = InserisciMetriDialogCompleto;\nexport default InserisciMetriDialogCompleto;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriDialogCompleto\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Typography", "Box", "Grid", "Paper", "<PERSON><PERSON>", "CircularProgress", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "caviService", "parcoCaviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriDialogCompleto", "open", "onClose", "cavo", "cantiereId", "onSuccess", "onError", "loading", "_s", "formData", "setFormData", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "saving", "setSaving", "bobine", "set<PERSON>ob<PERSON>", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "confirmDialogProps", "setConfirmDialogProps", "title", "message", "onConfirm", "_cavo$metratura_reale", "metratura_reale", "toString", "loadBobine", "console", "log", "id_cavo", "bobine<PERSON><PERSON>", "getBobine", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "tipologiaCompatibile", "tipologia", "toLowerCase", "sezioneCompatibile", "sezione", "metri_residui", "error", "handleFormChange", "event", "name", "value", "target", "prev", "metri", "parseFloat", "isNaN", "metri_te<PERSON>ci", "validateForm", "errors", "trim", "length", "Object", "keys", "handleSave", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "idBobina", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "handleClose", "_error$response", "_error$response$data", "success", "errorMessage", "response", "data", "detail", "handleKeyPress", "key", "children", "max<PERSON><PERSON><PERSON>", "fullWidth", "disableEscapeKeyDown", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dividers", "sx", "p", "mb", "bgcolor", "container", "spacing", "item", "xs", "md", "variant", "ubicazione_partenza", "ubicazione_arrivo", "autoFocus", "label", "type", "onChange", "onKeyPress", "Boolean", "helperText", "FormHelperTextProps", "color", "disabled", "InputProps", "endAdornment", "inputProps", "max", "step", "display", "alignItems", "gap", "size", "map", "toFixed", "mt", "ml", "severity", "onClick", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/InserisciMetriDialogCompleto.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Typography,\n  Box,\n  Grid,\n  Paper,\n  Alert,\n  CircularProgress,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip\n} from '@mui/material';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\n\n/**\n * Dialog completo per inserire i metri posati di un cavo preselezionato\n * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Cavo preselezionato\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nconst InserisciMetriDialogCompleto = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  cantiereId,\n  onSuccess = () => {},\n  onError = () => {},\n  loading = false\n}) => {\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n  \n  // Stati per bobine\n  const [bobine, setBobine] = useState([]);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  \n  // Stati per dialoghi di conferma\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    open: false,\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        metri_posati: cavo.metratura_reale?.toString() || '',\n        id_bobina: ''\n      });\n      setFormErrors({});\n      setFormWarnings({});\n      setSaving(false);\n      \n      // Carica le bobine disponibili\n      loadBobine();\n    }\n  }, [open, cavo, cantiereId]);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    if (!cantiereId || !cavo) return;\n    \n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId, 'e cavo:', cavo.id_cavo);\n      \n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n      \n      // Filtra le bobine compatibili con il cavo\n      const bobineCompatibili = bobineData.filter(bobina => {\n        const tipologiaCompatibile = !cavo.tipologia || !bobina.tipologia || \n          cavo.tipologia.toLowerCase() === bobina.tipologia.toLowerCase();\n        const sezioneCompatibile = !cavo.sezione || !bobina.sezione || \n          cavo.sezione.toLowerCase() === bobina.sezione.toLowerCase();\n        \n        return tipologiaCompatibile && sezioneCompatibile && bobina.metri_residui > 0;\n      });\n      \n      console.log('Bobine compatibili filtrate:', bobineCompatibili);\n      setBobine(bobineCompatibili || []);\n      \n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      setBobine([]);\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce i cambiamenti del form\n  const handleFormChange = (event) => {\n    const { name, value } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Rimuovi errori quando l'utente inizia a digitare\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n    \n    // Validazione in tempo reale per metri posati\n    if (name === 'metri_posati' && value && cavo) {\n      const metri = parseFloat(value);\n      if (!isNaN(metri) && metri > cavo.metri_teorici) {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n        }));\n      } else {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: ''\n        }));\n      }\n    }\n  };\n\n  // Validazione del form\n  const validateForm = () => {\n    const errors = {};\n    \n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'I metri posati sono obbligatori';\n    } else {\n      const metri = parseFloat(formData.metri_posati);\n      if (isNaN(metri) || metri < 0) {\n        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';\n      }\n    }\n    \n    // Validazione bobina\n    if (!formData.id_bobina || formData.id_bobina === '') {\n      // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n      if (bobine.length === 0 && !bobineLoading) {\n        setFormData(prev => ({ ...prev, id_bobina: 'BOBINA_VUOTA' }));\n      } else {\n        errors.id_bobina = 'È necessario selezionare una bobina';\n      }\n    }\n    \n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n    \n    try {\n      setSaving(true);\n      \n      const metriPosati = parseFloat(formData.metri_posati);\n      let idBobina = formData.id_bobina;\n      \n      // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n      if (bobine.length === 0 && !bobineLoading) {\n        idBobina = 'BOBINA_VUOTA';\n      }\n      \n      // Assicurati che BOBINA_VUOTA venga passato come stringa\n      if (idBobina === 'BOBINA_VUOTA') {\n        idBobina = 'BOBINA_VUOTA';\n      }\n      \n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', cavo.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina);\n      \n      // Chiamata API con la funzione originale completa\n      await caviService.updateMetriPosati(\n        cantiereId,\n        cavo.id_cavo,\n        metriPosati,\n        idBobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n      \n      // Successo\n      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;\n      onSuccess(successMessage);\n      \n      // Chiudi il dialog\n      handleClose();\n      \n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      \n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n        handleClose();\n        return;\n      }\n      \n      // Gestione errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response?.data?.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      \n      onError(errorMessage);\n      \n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!saving && !loading) {\n      setFormErrors({});\n      setFormWarnings({});\n      setFormData({ metri_posati: '', id_bobina: '' });\n      onClose();\n    }\n  };\n\n  const handleKeyPress = (event) => {\n    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {\n      handleSave();\n    }\n  };\n\n  if (!cavo) return null;\n\n  return (\n    <>\n      <Dialog \n        open={open} \n        onClose={handleClose} \n        maxWidth=\"md\" \n        fullWidth\n        disableEscapeKeyDown={saving || loading}\n      >\n        <DialogTitle>\n          Inserisci Metri Posati - {cavo.id_cavo}\n        </DialogTitle>\n        \n        <DialogContent dividers>\n          {/* Informazioni cavo */}\n          <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Sezione:</strong> {cavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Attualmente posati:</strong> {cavo.metratura_reale || 0} m</Typography>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          {/* Campo metri posati */}\n          <Box sx={{ mb: 3 }}>\n            <TextField\n              autoFocus\n              label=\"Metri Posati\"\n              type=\"number\"\n              fullWidth\n              name=\"metri_posati\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              onKeyPress={handleKeyPress}\n              error={Boolean(formErrors.metri_posati)}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati || `Metri teorici: ${cavo.metri_teorici}m`}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              disabled={saving || loading}\n              InputProps={{\n                endAdornment: <Typography variant=\"body2\" color=\"text.secondary\">m</Typography>\n              }}\n              inputProps={{\n                max: 999999,\n                step: 0.1\n              }}\n              sx={{ mb: 2 }}\n            />\n          </Box>\n\n          {/* Selezione bobina */}\n          <Box sx={{ mb: 3 }}>\n            <FormControl fullWidth error={Boolean(formErrors.id_bobina)}>\n              <InputLabel>Bobina</InputLabel>\n              <Select\n                name=\"id_bobina\"\n                value={formData.id_bobina}\n                onChange={handleFormChange}\n                label=\"Bobina\"\n                disabled={saving || loading || bobineLoading}\n              >\n                {/* Opzione BOBINA_VUOTA sempre disponibile */}\n                <MenuItem value=\"BOBINA_VUOTA\">\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <Chip label=\"VUOTA\" size=\"small\" color=\"warning\" variant=\"outlined\" />\n                    <Typography>BOBINA VUOTA (Posa senza bobina specifica)</Typography>\n                  </Box>\n                </MenuItem>\n\n                {/* Bobine disponibili */}\n                {bobine.map((bobina) => (\n                  <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      <Chip\n                        label={bobina.metri_residui ? `${bobina.metri_residui.toFixed(1)}m` : '0m'}\n                        size=\"small\"\n                        color={bobina.metri_residui > 0 ? 'success' : 'error'}\n                        variant=\"outlined\"\n                      />\n                      <Typography>\n                        {bobina.id_bobina} - {bobina.tipologia || 'N/A'} {bobina.sezione || 'N/A'}\n                      </Typography>\n                    </Box>\n                  </MenuItem>\n                ))}\n              </Select>\n              {formErrors.id_bobina && (\n                <Typography variant=\"caption\" color=\"error\" sx={{ mt: 0.5, ml: 1.5 }}>\n                  {formErrors.id_bobina}\n                </Typography>\n              )}\n            </FormControl>\n\n            {bobineLoading && (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>\n                <CircularProgress size={16} />\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Caricamento bobine...\n                </Typography>\n              </Box>\n            )}\n\n            {!bobineLoading && bobine.length === 0 && (\n              <Alert severity=\"info\" sx={{ mt: 1 }}>\n                Nessuna bobina compatibile disponibile. Verrà utilizzata BOBINA_VUOTA.\n              </Alert>\n            )}\n          </Box>\n        </DialogContent>\n\n        <DialogActions>\n          <Button\n            onClick={handleClose}\n            disabled={saving || loading}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            variant=\"contained\"\n            disabled={saving || loading || !formData.metri_posati || !formData.id_bobina}\n            startIcon={saving ? <CircularProgress size={20} /> : null}\n          >\n            {saving ? 'Salvando...' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </>\n  );\n};\n\nexport default InserisciMetriDialogCompleto;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,gBAAgB,EAChBC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,QACC,eAAe;AACtB,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAaA,MAAMC,4BAA4B,GAAGA,CAAC;EACpCC,IAAI,GAAG,KAAK;EACZC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,IAAI,GAAG,IAAI;EACXC,UAAU;EACVC,SAAS,GAAGA,CAAA,KAAM,CAAC,CAAC;EACpBC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC;IACvCoC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC0C,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACgD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjD,QAAQ,CAAC;IAC3D0B,IAAI,EAAE,KAAK;IACXwB,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;EACAnD,SAAS,CAAC,MAAM;IACd,IAAIyB,IAAI,IAAIE,IAAI,EAAE;MAAA,IAAAyB,qBAAA;MAChBlB,WAAW,CAAC;QACVC,YAAY,EAAE,EAAAiB,qBAAA,GAAAzB,IAAI,CAAC0B,eAAe,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,QAAQ,CAAC,CAAC,KAAI,EAAE;QACpDlB,SAAS,EAAE;MACb,CAAC,CAAC;MACFE,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBE,SAAS,CAAC,KAAK,CAAC;;MAEhB;MACAa,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC9B,IAAI,EAAEE,IAAI,EAAEC,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAM2B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC3B,UAAU,IAAI,CAACD,IAAI,EAAE;IAE1B,IAAI;MACFmB,gBAAgB,CAAC,IAAI,CAAC;MACtBU,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE7B,UAAU,EAAE,SAAS,EAAED,IAAI,CAAC+B,OAAO,CAAC;MAEpF,MAAMC,UAAU,GAAG,MAAMxC,gBAAgB,CAACyC,SAAS,CAAChC,UAAU,CAAC;MAC/D4B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,UAAU,CAAC;;MAE3C;MACA,MAAME,iBAAiB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAAI;QACpD,MAAMC,oBAAoB,GAAG,CAACrC,IAAI,CAACsC,SAAS,IAAI,CAACF,MAAM,CAACE,SAAS,IAC/DtC,IAAI,CAACsC,SAAS,CAACC,WAAW,CAAC,CAAC,KAAKH,MAAM,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC;QACjE,MAAMC,kBAAkB,GAAG,CAACxC,IAAI,CAACyC,OAAO,IAAI,CAACL,MAAM,CAACK,OAAO,IACzDzC,IAAI,CAACyC,OAAO,CAACF,WAAW,CAAC,CAAC,KAAKH,MAAM,CAACK,OAAO,CAACF,WAAW,CAAC,CAAC;QAE7D,OAAOF,oBAAoB,IAAIG,kBAAkB,IAAIJ,MAAM,CAACM,aAAa,GAAG,CAAC;MAC/E,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEI,iBAAiB,CAAC;MAC9DjB,SAAS,CAACiB,iBAAiB,IAAI,EAAE,CAAC;IAEpC,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D1B,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMyB,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,KAAK,CAACG,MAAM;IACpCzC,WAAW,CAAC0C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIrC,UAAU,CAACoC,IAAI,CAAC,EAAE;MACpBnC,aAAa,CAACsC,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIA,IAAI,KAAK,cAAc,IAAIC,KAAK,IAAI/C,IAAI,EAAE;MAC5C,MAAMkD,KAAK,GAAGC,UAAU,CAACJ,KAAK,CAAC;MAC/B,IAAI,CAACK,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAGlD,IAAI,CAACqD,aAAa,EAAE;QAC/CxC,eAAe,CAACoC,IAAI,KAAK;UACvB,GAAGA,IAAI;UACPzC,YAAY,EAAE,mBAAmB0C,KAAK,yCAAyClD,IAAI,CAACqD,aAAa;QACnG,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLxC,eAAe,CAACoC,IAAI,KAAK;UACvB,GAAGA,IAAI;UACPzC,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;;EAED;EACA,MAAM8C,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACjD,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACE,YAAY,CAACgD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjED,MAAM,CAAC/C,YAAY,GAAG,iCAAiC;IACzD,CAAC,MAAM;MACL,MAAM0C,KAAK,GAAGC,UAAU,CAAC7C,QAAQ,CAACE,YAAY,CAAC;MAC/C,IAAI4C,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QAC7BK,MAAM,CAAC/C,YAAY,GAAG,0DAA0D;MAClF;IACF;;IAEA;IACA,IAAI,CAACF,QAAQ,CAACG,SAAS,IAAIH,QAAQ,CAACG,SAAS,KAAK,EAAE,EAAE;MACpD;MACA,IAAIO,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACvC,aAAa,EAAE;QACzCX,WAAW,CAAC0C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAExC,SAAS,EAAE;QAAe,CAAC,CAAC,CAAC;MAC/D,CAAC,MAAM;QACL8C,MAAM,CAAC9C,SAAS,GAAG,qCAAqC;MAC1D;IACF;IAEAE,aAAa,CAAC4C,MAAM,CAAC;IACrB,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACE,MAAM,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACFvC,SAAS,CAAC,IAAI,CAAC;MAEf,MAAM8C,WAAW,GAAGV,UAAU,CAAC7C,QAAQ,CAACE,YAAY,CAAC;MACrD,IAAIsD,QAAQ,GAAGxD,QAAQ,CAACG,SAAS;;MAEjC;MACA,IAAIO,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACvC,aAAa,EAAE;QACzC4C,QAAQ,GAAG,cAAc;MAC3B;;MAEA;MACA,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAC/BA,QAAQ,GAAG,cAAc;MAC3B;MAEAjC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE7B,UAAU,CAAC;MACxC4B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE9B,IAAI,CAAC+B,OAAO,CAAC;MACvCF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+B,WAAW,CAAC;MAC3ChC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgC,QAAQ,CAAC;;MAErC;MACA,MAAMvE,WAAW,CAACwE,iBAAiB,CACjC9D,UAAU,EACVD,IAAI,CAAC+B,OAAO,EACZ8B,WAAW,EACXC,QAAQ,EACR,IAAI,CAAC;MACP,CAAC;;MAED;MACA,MAAME,cAAc,GAAG,oDAAoDhE,IAAI,CAAC+B,OAAO,KAAK8B,WAAW,GAAG;MAC1G3D,SAAS,CAAC8D,cAAc,CAAC;;MAEzB;MACAC,WAAW,CAAC,CAAC;IAEf,CAAC,CAAC,OAAOtB,KAAK,EAAE;MAAA,IAAAuB,eAAA,EAAAC,oBAAA;MACdtC,OAAO,CAACc,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAImB,QAAQ,KAAK,cAAc,IAAInB,KAAK,CAACyB,OAAO,EAAE;QAChD,MAAMJ,cAAc,GAAG,qEAAqE;QAC5F9D,SAAS,CAAC8D,cAAc,CAAC;QACzBC,WAAW,CAAC,CAAC;QACb;MACF;;MAEA;MACA,IAAII,YAAY,GAAG,kDAAkD;MACrE,KAAAH,eAAA,GAAIvB,KAAK,CAAC2B,QAAQ,cAAAJ,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBK,IAAI,cAAAJ,oBAAA,eAApBA,oBAAA,CAAsBK,MAAM,EAAE;QAChCH,YAAY,GAAG1B,KAAK,CAAC2B,QAAQ,CAACC,IAAI,CAACC,MAAM;MAC3C,CAAC,MAAM,IAAI7B,KAAK,CAACpB,OAAO,EAAE;QACxB8C,YAAY,GAAG1B,KAAK,CAACpB,OAAO;MAC9B;MAEApB,OAAO,CAACkE,YAAY,CAAC;IAEvB,CAAC,SAAS;MACRtD,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMkD,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACnD,MAAM,IAAI,CAACV,OAAO,EAAE;MACvBO,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBN,WAAW,CAAC;QAAEC,YAAY,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAC,CAAC;MAChDV,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAM0E,cAAc,GAAI5B,KAAK,IAAK;IAChC,IAAIA,KAAK,CAAC6B,GAAG,KAAK,OAAO,IAAI,CAAC5D,MAAM,IAAI,CAACV,OAAO,IAAIE,QAAQ,CAACE,YAAY,CAACgD,IAAI,CAAC,CAAC,IAAIlD,QAAQ,CAACG,SAAS,EAAE;MACtGmD,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,IAAI,CAAC5D,IAAI,EAAE,OAAO,IAAI;EAEtB,oBACEN,OAAA,CAAAE,SAAA;IAAA+E,QAAA,eACEjF,OAAA,CAACpB,MAAM;MACLwB,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEkE,WAAY;MACrBW,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,oBAAoB,EAAEhE,MAAM,IAAIV,OAAQ;MAAAuE,QAAA,gBAExCjF,OAAA,CAACnB,WAAW;QAAAoG,QAAA,GAAC,2BACc,EAAC3E,IAAI,CAAC+B,OAAO;MAAA;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAEdxF,OAAA,CAAClB,aAAa;QAAC2G,QAAQ;QAAAR,QAAA,gBAErBjF,OAAA,CAACX,KAAK;UAACqG,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAAZ,QAAA,eAC7CjF,OAAA,CAACZ,IAAI;YAAC0G,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAd,QAAA,gBACzBjF,OAAA,CAACZ,IAAI;cAAC4G,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAjB,QAAA,gBACvBjF,OAAA,CAACd,UAAU;gBAACiH,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAACsC,SAAS,IAAI,KAAK;cAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9FxF,OAAA,CAACd,UAAU;gBAACiH,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAACyC,OAAO,IAAI,KAAK;cAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1FxF,OAAA,CAACd,UAAU;gBAACiH,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAACqD,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG,CAAC,eACPxF,OAAA,CAACZ,IAAI;cAAC4G,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAjB,QAAA,gBACvBjF,OAAA,CAACd,UAAU;gBAACiH,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAG;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAAC8F,mBAAmB,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjGxF,OAAA,CAACd,UAAU;gBAACiH,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAAC+F,iBAAiB,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9FxF,OAAA,CAACd,UAAU;gBAACiH,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAACjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClF,IAAI,CAAC0B,eAAe,IAAI,CAAC,EAAC,IAAE;cAAA;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGRxF,OAAA,CAACb,GAAG;UAACuG,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,eACjBjF,OAAA,CAACf,SAAS;YACRqH,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBC,IAAI,EAAC,QAAQ;YACbrB,SAAS;YACT/B,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAEzC,QAAQ,CAACE,YAAa;YAC7B2F,QAAQ,EAAEvD,gBAAiB;YAC3BwD,UAAU,EAAE3B,cAAe;YAC3B9B,KAAK,EAAE0D,OAAO,CAAC3F,UAAU,CAACF,YAAY,CAAE;YACxC8F,UAAU,EAAE5F,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAY,IAAI,kBAAkBR,IAAI,CAACqD,aAAa,GAAI;YAC5GkD,mBAAmB,EAAE;cACnBnB,EAAE,EAAE;gBAAEoB,KAAK,EAAE5F,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACFiG,QAAQ,EAAE3F,MAAM,IAAIV,OAAQ;YAC5BsG,UAAU,EAAE;cACVC,YAAY,eAAEjH,OAAA,CAACd,UAAU;gBAACiH,OAAO,EAAC,OAAO;gBAACW,KAAK,EAAC,gBAAgB;gBAAA7B,QAAA,EAAC;cAAC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAChF,CAAE;YACF0B,UAAU,EAAE;cACVC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE;YACR,CAAE;YACF1B,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNxF,OAAA,CAACb,GAAG;UAACuG,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,gBACjBjF,OAAA,CAACR,WAAW;YAAC2F,SAAS;YAAClC,KAAK,EAAE0D,OAAO,CAAC3F,UAAU,CAACD,SAAS,CAAE;YAAAkE,QAAA,gBAC1DjF,OAAA,CAACP,UAAU;cAAAwF,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BxF,OAAA,CAACN,MAAM;cACL0D,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEzC,QAAQ,CAACG,SAAU;cAC1B0F,QAAQ,EAAEvD,gBAAiB;cAC3BqD,KAAK,EAAC,QAAQ;cACdQ,QAAQ,EAAE3F,MAAM,IAAIV,OAAO,IAAIc,aAAc;cAAAyD,QAAA,gBAG7CjF,OAAA,CAACL,QAAQ;gBAAC0D,KAAK,EAAC,cAAc;gBAAA4B,QAAA,eAC5BjF,OAAA,CAACb,GAAG;kBAACuG,EAAE,EAAE;oBAAE2B,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,gBACzDjF,OAAA,CAACJ,IAAI;oBAAC2G,KAAK,EAAC,OAAO;oBAACiB,IAAI,EAAC,OAAO;oBAACV,KAAK,EAAC,SAAS;oBAACX,OAAO,EAAC;kBAAU;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtExF,OAAA,CAACd,UAAU;oBAAA+F,QAAA,EAAC;kBAA0C;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGVlE,MAAM,CAACmG,GAAG,CAAE/E,MAAM,iBACjB1C,OAAA,CAACL,QAAQ;gBAAwB0D,KAAK,EAAEX,MAAM,CAAC3B,SAAU;gBAAAkE,QAAA,eACvDjF,OAAA,CAACb,GAAG;kBAACuG,EAAE,EAAE;oBAAE2B,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,gBACzDjF,OAAA,CAACJ,IAAI;oBACH2G,KAAK,EAAE7D,MAAM,CAACM,aAAa,GAAG,GAAGN,MAAM,CAACM,aAAa,CAAC0E,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAK;oBAC3EF,IAAI,EAAC,OAAO;oBACZV,KAAK,EAAEpE,MAAM,CAACM,aAAa,GAAG,CAAC,GAAG,SAAS,GAAG,OAAQ;oBACtDmD,OAAO,EAAC;kBAAU;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACFxF,OAAA,CAACd,UAAU;oBAAA+F,QAAA,GACRvC,MAAM,CAAC3B,SAAS,EAAC,KAAG,EAAC2B,MAAM,CAACE,SAAS,IAAI,KAAK,EAAC,GAAC,EAACF,MAAM,CAACK,OAAO,IAAI,KAAK;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAXO9C,MAAM,CAAC3B,SAAS;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYrB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACRxE,UAAU,CAACD,SAAS,iBACnBf,OAAA,CAACd,UAAU;cAACiH,OAAO,EAAC,SAAS;cAACW,KAAK,EAAC,OAAO;cAACpB,EAAE,EAAE;gBAAEiC,EAAE,EAAE,GAAG;gBAAEC,EAAE,EAAE;cAAI,CAAE;cAAA3C,QAAA,EAClEjE,UAAU,CAACD;YAAS;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,EAEbhE,aAAa,iBACZxB,OAAA,CAACb,GAAG;YAACuG,EAAE,EAAE;cAAE2B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE,CAAC;cAAEI,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,gBAChEjF,OAAA,CAACT,gBAAgB;cAACiI,IAAI,EAAE;YAAG;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BxF,OAAA,CAACd,UAAU;cAACiH,OAAO,EAAC,SAAS;cAACW,KAAK,EAAC,gBAAgB;cAAA7B,QAAA,EAAC;YAErD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN,EAEA,CAAChE,aAAa,IAAIF,MAAM,CAACyC,MAAM,KAAK,CAAC,iBACpC/D,OAAA,CAACV,KAAK;YAACuI,QAAQ,EAAC,MAAM;YAACnC,EAAE,EAAE;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,EAAC;UAEtC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEhBxF,OAAA,CAACjB,aAAa;QAAAkG,QAAA,gBACZjF,OAAA,CAAChB,MAAM;UACL8I,OAAO,EAAEvD,WAAY;UACrBwC,QAAQ,EAAE3F,MAAM,IAAIV,OAAQ;UAAAuE,QAAA,EAC7B;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxF,OAAA,CAAChB,MAAM;UACL8I,OAAO,EAAE5D,UAAW;UACpBiC,OAAO,EAAC,WAAW;UACnBY,QAAQ,EAAE3F,MAAM,IAAIV,OAAO,IAAI,CAACE,QAAQ,CAACE,YAAY,IAAI,CAACF,QAAQ,CAACG,SAAU;UAC7EgH,SAAS,EAAE3G,MAAM,gBAAGpB,OAAA,CAACT,gBAAgB;YAACiI,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAK;UAAAP,QAAA,EAEzD7D,MAAM,GAAG,aAAa,GAAG;QAAO;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC,gBACT,CAAC;AAEP,CAAC;AAAC7E,EAAA,CA3WIR,4BAA4B;AAAA6H,EAAA,GAA5B7H,4BAA4B;AA6WlC,eAAeA,4BAA4B;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}