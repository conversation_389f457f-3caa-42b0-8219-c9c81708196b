import React, { useState, useEffect } from 'react';
import { Box, Typography, Chip, TableRow, TableCell, IconButton, Tooltip } from '@mui/material';
import { Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';
import FilterableTable from '../common/FilterableTable';
import { REEL_STATES, getReelStateColor } from '../../utils/stateUtils';

/**
 * Componente per visualizzare la lista delle bobine con filtri in stile Excel
 *
 * @param {Object} props - Proprietà del componente
 * @param {Array} props.bobine - Lista delle bobine da visualizzare
 * @param {boolean} props.loading - Indica se i dati sono in caricamento
 * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano
 * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina
 * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina

 * @param {Function} props.onQuickAdd - Funzione chiamata quando si vuole aggiungere rapidamente cavi a una bobina
 */
const BobineFilterableTable = ({
  bobine = [],
  loading = false,
  onFilteredDataChange = null,
  onEdit = null,
  onDelete = null,
  onQuickAdd = null
}) => {
  const [filteredBobine, setFilteredBobine] = useState(bobine);

  // Aggiorna i dati filtrati quando cambiano le bobine
  useEffect(() => {
    setFilteredBobine(bobine);
  }, [bobine]);

  // Notifica il componente padre quando cambiano i dati filtrati
  const handleFilteredDataChange = (data) => {
    setFilteredBobine(data);
    if (onFilteredDataChange) {
      onFilteredDataChange(data);
    }
  };

  // Definizione delle colonne
  const columns = [
    {
      field: 'numero_bobina',
      headerName: 'ID Bobina',
      dataType: 'text',
      width: 120,
      headerStyle: { fontWeight: 'bold' },
      renderCell: (row) => (
        <Typography variant="body1" sx={{ fontSize: '0.95rem', fontWeight: 600 }}>
          {row.numero_bobina}
        </Typography>
      )
    },
    {
      field: 'utility',
      headerName: 'Utility',
      dataType: 'text',
      width: 100,
      renderCell: (row) => (
        <Typography variant="body1" sx={{ fontSize: '0.95rem' }}>
          {row.utility}
        </Typography>
      )
    },
    {
      field: 'tipologia',
      headerName: 'Tipologia',
      dataType: 'text',
      width: 150,
      renderCell: (row) => (
        <Typography variant="body1" sx={{ fontSize: '0.95rem' }}>
          {row.tipologia}
        </Typography>
      )
    },
    // n_conduttori field is now a spare field (kept in DB but hidden in UI)
    {
      field: 'sezione',
      headerName: 'Formazione',
      dataType: 'text',
      width: 120,
      align: 'center',
      renderCell: (row) => (
        <Typography variant="body1" sx={{ fontSize: '0.95rem', textAlign: 'center' }}>
          {row.sezione}
        </Typography>
      )
    },
    {
      field: 'metri_totali',
      headerName: 'Metri Totali',
      dataType: 'number',
      width: 110,
      align: 'center',
      renderCell: (row) => (
        <Typography variant="body1" sx={{ fontSize: '0.95rem', textAlign: 'center', fontWeight: 500 }}>
          {row.metri_totali ? row.metri_totali.toFixed(1) : '0'}
        </Typography>
      )
    },
    {
      field: 'metri_residui',
      headerName: 'Metri Residui',
      dataType: 'number',
      width: 110,
      align: 'center',
      renderCell: (row) => (
        <Typography variant="body1" sx={{ fontSize: '0.95rem', textAlign: 'center', fontWeight: 500 }}>
          {row.metri_residui ? row.metri_residui.toFixed(1) : '0'}
        </Typography>
      )
    },
    {
      field: 'stato_bobina',
      headerName: 'Stato',
      dataType: 'text',
      renderCell: (row) => {
        return (
          <Chip
            label={row.stato_bobina || 'N/D'}
            size="small"
            color={getReelStateColor(row.stato_bobina)}
            variant="outlined"
          />
        );
      }
    },
    {
      field: 'ubicazione_bobina',
      headerName: 'Ubicazione',
      dataType: 'text'
    },
    {
      field: 'fornitore',
      headerName: 'Fornitore',
      dataType: 'text'
    },
    {
      field: 'n_DDT',
      headerName: 'N° DDT',
      dataType: 'text'
    },
    {
      field: 'data_DDT',
      headerName: 'Data DDT',
      dataType: 'text'
    },
    {
      field: 'actions',
      headerName: 'Azioni',
      disableFilter: true,
      disableSort: true,
      align: 'center',
      renderCell: (row) => (
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          {onEdit && (
            <IconButton
              size="small"
              onClick={() => onEdit(row)}
              title="Modifica bobina"
              color="primary"
            >
              <EditIcon fontSize="small" />
            </IconButton>
          )}
          {onDelete && (
            <IconButton
              size="small"
              onClick={() => onDelete(row)}
              title="Elimina bobina"
              color="error"
              disabled={row.stato_bobina !== 'Disponibile' || row.metri_residui !== row.metri_totali}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          )}

          {onQuickAdd && row.stato_bobina !== 'Terminata' && row.stato_bobina !== 'Over' && (
            <Tooltip title="Aggiungi cavi a questa bobina">
              <IconButton
                size="small"
                onClick={() => onQuickAdd(row)}
                color="success"
              >
                <AddIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      )
    }
  ];

  // Renderizza una riga personalizzata
  const renderRow = (row, index) => {
    // Determina il colore di sfondo in base allo stato
    let bgColor = 'inherit';
    if (row.stato_bobina === REEL_STATES.DISPONIBILE) bgColor = 'rgba(76, 175, 80, 0.1)';
    else if (row.stato_bobina === REEL_STATES.IN_USO) bgColor = 'rgba(255, 152, 0, 0.1)';
    else if (row.stato_bobina === REEL_STATES.TERMINATA) bgColor = 'rgba(255, 152, 0, 0.2)';
    else if (row.stato_bobina === REEL_STATES.OVER) bgColor = 'rgba(244, 67, 54, 0.1)';

    return (
      <TableRow
        key={index}
        sx={{
          backgroundColor: bgColor,
          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
        }}
      >
        {columns.map((column) => (
          <TableCell
            key={column.field}
            align={column.align || 'left'}
            sx={column.cellStyle}
          >
            {column.renderCell ? column.renderCell(row) : row[column.field]}
          </TableCell>
        ))}
      </TableRow>
    );
  };

  // Calcola le statistiche
  const calculateStats = () => {
    if (!filteredBobine.length) return null;

    const totalBobine = filteredBobine.length;
    const disponibili = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.DISPONIBILE).length;
    const inUso = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.IN_USO).length;
    const terminate = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.TERMINATA).length;
    const over = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.OVER).length;

    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0);
    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0);
    const metriUtilizzati = metriTotali - metriResidui;

    const percentualeUtilizzo = metriTotali ? Math.round((metriUtilizzati / metriTotali) * 100) : 0;

    return {
      totalBobine,
      disponibili,
      inUso,
      terminate,
      over,
      metriTotali,
      metriResidui,
      metriUtilizzati,
      percentualeUtilizzo
    };
  };

  const stats = calculateStats();

  return (
    <Box>
      {stats && (
        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
          <Typography variant="subtitle1" gutterBottom>
            Statistiche ({filteredBobine.length} bobine visualizzate)
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <Box>
              <Typography variant="body2" color="text.secondary">Utilizzo</Typography>
              <Typography variant="h6">{stats.percentualeUtilizzo}%</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">Disponibili</Typography>
              <Typography variant="h6" color="success.main">{stats.disponibili}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">In uso</Typography>
              <Typography variant="h6" color="warning.main">{stats.inUso}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">Terminate</Typography>
              <Typography variant="h6" color="warning.main">{stats.terminate}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">Over</Typography>
              <Typography variant="h6" color="error.main">{stats.over}</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">Metri totali</Typography>
              <Typography variant="h6">{stats.metriTotali.toFixed(1)} m</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">Metri residui</Typography>
              <Typography variant="h6">{stats.metriResidui.toFixed(1)} m</Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">Metri utilizzati</Typography>
              <Typography variant="h6">{stats.metriUtilizzati.toFixed(1)} m</Typography>
            </Box>
          </Box>
        </Box>
      )}

      <FilterableTable
        data={bobine}
        columns={columns}
        onFilteredDataChange={handleFilteredDataChange}
        loading={loading}
        emptyMessage="Nessuna bobina disponibile"
        renderRow={renderRow}
      />
    </Box>
  );
};

export default BobineFilterableTable;
