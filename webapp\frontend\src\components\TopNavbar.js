import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Box,
  Button,
  Menu,
  MenuItem,
  Typography,
  IconButton,
  Divider,
  Avatar,
  Tooltip,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Home as HomeIcon,
  AdminPanelSettings as AdminIcon,
  Construction as ConstructionIcon,
  Cable as CableIcon,
  Description as ReportIcon,
  Logout as LogoutIcon,
  KeyboardArrowDown as ArrowDownIcon,
  FileUpload as FileUploadIcon,
  FileDownload as FileDownloadIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useGlobalContext } from '../context/GlobalContext';
import SelectedCantiereDisplay from './common/SelectedCantiereDisplay';
import ExcelPopup from './cavi/ExcelPopup';
import Logo from './Logo';
import './TopNavbar.css';

const TopNavbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, isImpersonating, impersonatedUser } = useAuth();
  const { setOpenEliminaCavoDialog, setOpenModificaCavoDialog } = useGlobalContext();

  // Stato per il popup Excel
  const [excelPopupOpen, setExcelPopupOpen] = useState(false);
  const [excelOperationType, setExcelOperationType] = useState('');

  // Stato per gli snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Recupera l'ID del cantiere dal localStorage
  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);

  // Stati per i menu a tendina
  const [homeAnchorEl, setHomeAnchorEl] = useState(null);
  const [adminAnchorEl, setAdminAnchorEl] = useState(null);
  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);
  const [caviAnchorEl, setCaviAnchorEl] = useState(null);
  // const [posaAnchorEl, setPosaAnchorEl] = useState(null); // OBSOLETO: Menu Posa rimosso
  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);
  const [excelAnchorEl, setExcelAnchorEl] = useState(null);



  // Recupera l'ID del cantiere selezionato dal localStorage
  const selectedCantiereId = localStorage.getItem('selectedCantiereId');
  const selectedCantiereName = localStorage.getItem('selectedCantiereName');

  // Funzioni per aprire/chiudere i menu
  const handleMenuOpen = (event, setAnchorEl) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = (setAnchorEl) => {
    setAnchorEl(null);
  };

  // Gestisce l'apertura del popup Excel
  const handleOpenExcelPopup = (operationType) => {
    setExcelOperationType(operationType);
    setExcelPopupOpen(true);
    handleMenuClose(setExcelAnchorEl);
  };

  // Gestisce la creazione diretta dei template senza popup
  const handleCreateTemplateDirect = async (templateType) => {
    try {
      handleMenuClose(setExcelAnchorEl);

      if (templateType === 'cavi') {
        const excelService = await import('../services/excelService');
        await excelService.default.createCaviTemplate();
        setSnackbar({
          open: true,
          message: 'Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.',
          severity: 'success'
        });
      } else if (templateType === 'parco-bobine') {
        const excelService = await import('../services/excelService');
        await excelService.default.createParcoBobineTemplate();
        setSnackbar({
          open: true,
          message: 'Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.',
          severity: 'success'
        });
      }
    } catch (error) {
      console.error(`Errore nella creazione del template ${templateType}:`, error);
      setSnackbar({
        open: true,
        message: `Errore nella creazione del template ${templateType}: ${error.message || 'Errore sconosciuto'}`,
        severity: 'error'
      });
    }
  };

  // Gestisce la chiusura del popup Excel
  const handleCloseExcelPopup = () => {
    setExcelPopupOpen(false);
  };

  // Gestisce il successo delle operazioni Excel
  const handleExcelSuccess = (message) => {
    setSnackbar({
      open: true,
      message,
      severity: 'success'
    });
  };

  // Gestisce gli errori delle operazioni Excel
  const handleExcelError = (message) => {
    setSnackbar({
      open: true,
      message,
      severity: 'error'
    });
  };

  // Gestisce la chiusura dello snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Naviga a un percorso
  const navigateTo = (path) => {
    // Gestione speciale per il percorso Home
    if (path === '/dashboard') {
      // Se l'utente è un amministratore che sta impersonando un utente
      if (isImpersonating) {
        navigate('/dashboard/admin');
      }
      // Se l'utente è un amministratore normale
      else if (user?.role === 'owner') {
        navigate('/dashboard/admin');
      }
      // Se l'utente è un utente standard
      else if (user?.role === 'user') {
        navigate('/dashboard/cantieri');
      }
      // Se l'utente è un utente cantiere
      else if (user?.role === 'cantieri_user') {
        // Reindirizza direttamente alla pagina di visualizzazione cavi
        navigate('/dashboard/cavi/visualizza');
      }
      // Fallback per altri tipi di utenti
      else {
        navigate(path);
      }
    } else {
      navigate(path);
    }

    // Chiudi tutti i menu
    handleMenuClose(setHomeAnchorEl);
    handleMenuClose(setAdminAnchorEl);
    handleMenuClose(setCantieriAnchorEl);
    handleMenuClose(setCaviAnchorEl);
    // handleMenuClose(setPosaAnchorEl); // OBSOLETO: Menu Posa rimosso - variabile eliminata
    handleMenuClose(setParcoAnchorEl);
    handleMenuClose(setExcelAnchorEl);


  };

  const handleLogout = () => {
    logout();
  };

  // Verifica se un percorso è attivo
  const isActive = (path) => {
    return location.pathname === path;
  };

  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)
  const isPartOfActive = (path) => {
    return location.pathname.startsWith(path);
  };

  return (
    <AppBar position="static" color="default" elevation={2} sx={{ zIndex: 1100, width: '100%', overflowX: 'hidden' }} className="excel-style-menu">
      <Toolbar sx={{ overflowX: 'hidden', height: '60px' }}>
        {/* CABLYS Logo */}
        <div style={{ display: 'flex', alignItems: 'center', marginRight: '16px' }}>
          <Logo width={32} height={32} />
          <Typography sx={{ fontWeight: 700, fontSize: '1.2rem', marginLeft: '8px', color: '#1976d2' }}>CABLYS</Typography>
        </div>

        {/* Home - Testo personalizzato in base al tipo di utente */}
        <Button
          color="inherit"
          onClick={() => navigateTo('/dashboard')}
          startIcon={<HomeIcon />}
          sx={{ mr: 1 }}
          className={isActive('/dashboard') ? 'active-button' : ''}
        >
          {isImpersonating ? "Torna al Menu Admin" :
           user?.role === 'owner' ? "Pannello Admin" :
           user?.role === 'user' ? "Lista Cantieri" :
           user?.role === 'cantieri_user' ? "Gestione Cavi" : "Home"}
        </Button>
        <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

        {/* Il menu Amministratore è stato rimosso perché ridondante con il pulsante Home per gli amministratori */}

        {/* Menu per utenti standard e cantieri */}
        {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (
          <>
            {/* Pulsante Lista Cantieri solo per utenti che impersonano */}
            {isImpersonating && (
              <Button
                color="inherit"
                onClick={() => navigateTo('/dashboard/cantieri')}
                sx={{ mr: 1 }}
                className={isActive('/dashboard/cantieri') ? 'active-button' : ''}
              >
                {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : "Lista Cantieri"}
              </Button>
            )}

            {/* Il cantiere selezionato è stato spostato nella parte destra della barra di navigazione */}

            {/* Menu di gestione cavi - visibile solo se un cantiere è selezionato */}
            {selectedCantiereId && (
              <>
                {/* Visualizza Cavi - nascosto per utenti cantiere perché ridondante con il tasto Home */}
                {user?.role !== 'cantieri_user' && (
                  <Button
                    color="inherit"
                    onClick={() => navigateTo('/dashboard/cavi/visualizza')}
                    sx={{ mr: 1 }}
                    className={isActive('/dashboard/cavi/visualizza') ? 'active-button' : ''}
                  >
                    Visualizza Cavi
                  </Button>
                )}

                {/* OBSOLETO: Pulsante "Posa e Collegamenti" rimosso - Funzionalità integrate in "Visualizza Cavi" */}

                {/* Parco Cavi */}
                <Button
                  color="inherit"
                  aria-controls="parco-menu"
                  aria-haspopup="true"
                  onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}
                  sx={{ mr: 1 }}
                  className={isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : ''}
                >
                  Parco Cavi
                </Button>

                {/* Gestione Excel */}
                <Button
                  color="inherit"
                  aria-controls="excel-menu"
                  aria-haspopup="true"
                  onClick={(e) => handleMenuOpen(e, setExcelAnchorEl)}
                  endIcon={<ArrowDownIcon />}
                  sx={{ mr: 1 }}
                  className={isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : ''}
                >
                  Gestione Excel
                </Button>

                {/* Report */}
                <Button
                  color="inherit"
                  onClick={() => navigateTo(`/dashboard/cavi/${selectedCantiereId}/report`)}
                  sx={{ mr: 1 }}
                  className={isPartOfActive('/dashboard/cavi/report') ? 'active-button' : ''}
                >
                  Report
                </Button>

                {/* Certificazione Cavi */}
                <Button
                  color="inherit"
                  onClick={() => navigateTo('/dashboard/cavi/certificazione')}
                  sx={{ mr: 1 }}
                  className={isPartOfActive('/dashboard/cavi/certificazione') ? 'active-button' : ''}
                >
                  Certificazione Cavi
                </Button>

                {/* Gestione Comande - semplificato */}
                <Button
                  color="inherit"
                  onClick={() => navigateTo('/dashboard/cavi/comande')}
                  sx={{ mr: 1 }}
                  className={isActive('/dashboard/cavi/comande') ? 'active-button' : ''}
                >
                  Gestione Comande
                </Button>

                {/* OBSOLETO: Menu "Posa e Collegamenti" rimosso - Funzionalità migrate ai popup nella pagina Visualizza Cavi */}

                {/* Sottomenu Parco Cavi */}
                <Menu
                  id="parco-menu"
                  anchorEl={parcoAnchorEl}
                  keepMounted
                  open={Boolean(parcoAnchorEl)}
                  onClose={() => handleMenuClose(setParcoAnchorEl)}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                  }}
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                  }}
                  className="excel-style-submenu"
                  elevation={3}
                  sx={{ mt: 0.5 }}
                >

                </Menu>

                {/* Sottomenu Gestione Excel */}
                <Menu
                  id="excel-menu"
                  anchorEl={excelAnchorEl}
                  keepMounted
                  open={Boolean(excelAnchorEl)}
                  onClose={() => handleMenuClose(setExcelAnchorEl)}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                  }}
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                  }}
                  className="excel-style-submenu"
                  elevation={3}
                  sx={{ mt: 0.5 }}
                >
                  <MenuItem onClick={() => handleOpenExcelPopup('importaCavi')}>
                    <FileUploadIcon fontSize="small" sx={{ mr: 1 }} />
                    Importa Cavi
                  </MenuItem>
                  <MenuItem onClick={() => handleOpenExcelPopup('importaParcoBobine')}>
                    <FileUploadIcon fontSize="small" sx={{ mr: 1 }} />
                    Importa Parco Bobine
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={() => handleCreateTemplateDirect('cavi')}>
                    <FileDownloadIcon fontSize="small" sx={{ mr: 1 }} />
                    Template Cavi
                  </MenuItem>
                  <MenuItem onClick={() => handleCreateTemplateDirect('parco-bobine')}>
                    <FileDownloadIcon fontSize="small" sx={{ mr: 1 }} />
                    Template Parco Bobine
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={() => handleOpenExcelPopup('esportaCavi')}>
                    <FileDownloadIcon fontSize="small" sx={{ mr: 1 }} />
                    Esporta Cavi
                  </MenuItem>
                  <MenuItem onClick={() => handleOpenExcelPopup('esportaParcoBobine')}>
                    <FileDownloadIcon fontSize="small" sx={{ mr: 1 }} />
                    Esporta Parco Bobine
                  </MenuItem>
                </Menu>






              </>
            )}
          </>
        )}

        {/* Spacer */}
        <Box sx={{ flexGrow: 1 }} />

        {/* Informazioni utente e logout */}
        <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
          {/* Mostra il cantiere selezionato */}
          <SelectedCantiereDisplay />

          {isImpersonating && impersonatedUser && (
            <Typography variant="body2" color="textSecondary" sx={{ mr: 2.5, fontSize: '1rem' }}>
              Accesso come: <b>{impersonatedUser.username}</b>
            </Typography>
          )}
          <Typography variant="body2" sx={{ mr: 2.5, fontWeight: 500, fontSize: '1rem' }}>
            {user?.username || ''}
          </Typography>
          <Tooltip title="Logout">
            <IconButton
              color="inherit"
              onClick={handleLogout}
              edge="end"
              sx={{ '&:hover': { backgroundColor: '#e9ecef' }, padding: '10px' }}
            >
              <LogoutIcon fontSize="medium" />
            </IconButton>
          </Tooltip>
        </Box>
      </Toolbar>

      {/* Excel Popup */}
      <ExcelPopup
        open={excelPopupOpen}
        onClose={handleCloseExcelPopup}
        operationType={excelOperationType}
        cantiereId={cantiereId}
        onSuccess={handleExcelSuccess}
        onError={handleExcelError}
      />

      {/* Snackbar per messaggi di successo/errore */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </AppBar>
  );
};

export default TopNavbar;
