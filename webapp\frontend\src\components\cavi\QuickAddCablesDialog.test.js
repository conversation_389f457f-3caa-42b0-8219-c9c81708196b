import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock completo del servizio cavi
const mockCaviService = {
  getCavi: jest.fn(),
  updateMetriPosati: jest.fn()
};

// Mock del modulo caviService
jest.mock('../../services/caviService', () => mockCaviService);

// Mock delle utility
jest.mock('../../utils/stateUtils', () => ({
  determineCableState: jest.fn(),
  getCableStateColor: jest.fn(() => 'default'),
  isCableInstalled: jest.fn((cavo) => cavo.stato_installazione === 'installato')
}));

import QuickAddCablesDialog from './QuickAddCablesDialog';

// Mock dei componenti Material-UI che potrebbero causare problemi nei test
jest.mock('./IncompatibleReelDialog', () => {
  return function MockIncompatibleReelDialog({ open, onClose, onConfirm }) {
    if (!open) return null;
    return (
      <div data-testid="incompatible-reel-dialog">
        <button onClick={onClose}>Annulla</button>
        <button onClick={onConfirm}>Usa Bobina Incompatibile</button>
      </div>
    );
  };
});

describe('QuickAddCablesDialog - Refactored', () => {
  const mockBobina = {
    id_bobina: 'C1_B001',
    numero_bobina: '001',
    tipologia: 'FG7OR',
    sezione: '4x16',
    metri_residui: 100,
    stato_bobina: 'Disponibile'
  };

  const mockCaviCompatibili = [
    {
      id_cavo: 'CAVO001',
      tipologia: 'FG7OR',
      sezione: '4x16',
      metri_teorici: 50,
      stato_installazione: 'Da installare',
      ubicazione_partenza: 'QE1',
      ubicazione_arrivo: 'QE2',
      modificato_manualmente: 0
    },
    {
      id_cavo: 'CAVO002',
      tipologia: 'FG7OR',
      sezione: '4x16',
      metri_teorici: 30,
      stato_installazione: 'Da installare',
      ubicazione_partenza: 'QE3',
      ubicazione_arrivo: 'QE4',
      modificato_manualmente: 0
    }
  ];

  const mockCaviIncompatibili = [
    {
      id_cavo: 'CAVO003',
      tipologia: 'FG16OR16',
      sezione: '4x25',
      metri_teorici: 40,
      stato_installazione: 'Da installare',
      ubicazione_partenza: 'QE5',
      ubicazione_arrivo: 'QE6',
      modificato_manualmente: 0
    }
  ];

  const defaultProps = {
    open: true,
    onClose: jest.fn(),
    bobina: mockBobina,
    cantiereId: '1',
    onSuccess: jest.fn(),
    onError: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockCaviService.getCavi.mockResolvedValue([...mockCaviCompatibili, ...mockCaviIncompatibili]);
    mockCaviService.updateMetriPosati.mockResolvedValue({});
  });

  test('renders dialog with bobina information', async () => {
    render(<QuickAddCablesDialog {...defaultProps} />);
    
    expect(screen.getByText(/Aggiungi cavi alla bobina 001/)).toBeInTheDocument();
    expect(screen.getByText('C1_B001')).toBeInTheDocument();
    expect(screen.getByText('FG7OR')).toBeInTheDocument();
    expect(screen.getByText('4x16')).toBeInTheDocument();
  });

  test('loads and separates compatible and incompatible cables', async () => {
    render(<QuickAddCablesDialog {...defaultProps} />);
    
    await waitFor(() => {
      expect(mockCaviService.getCavi).toHaveBeenCalledWith('1');
    });

    // Verifica che i tab siano presenti
    expect(screen.getByText(/Cavi compatibili/)).toBeInTheDocument();
    expect(screen.getByText(/Cavi incompatibili/)).toBeInTheDocument();
  });

  test('shows compatible cables by default', async () => {
    render(<QuickAddCablesDialog {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('CAVO001')).toBeInTheDocument();
      expect(screen.getByText('CAVO002')).toBeInTheDocument();
    });
  });

  test('switches to incompatible cables tab', async () => {
    render(<QuickAddCablesDialog {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText(/Cavi incompatibili/)).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText(/Cavi incompatibili/));
    
    await waitFor(() => {
      expect(screen.getByText('CAVO003')).toBeInTheDocument();
    });
  });

  test('handles compatible cable selection', async () => {
    render(<QuickAddCablesDialog {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('CAVO001')).toBeInTheDocument();
    });

    const selectButton = screen.getAllByText('Seleziona')[0];
    fireEvent.click(selectButton);

    expect(screen.getByText('Selezionato')).toBeInTheDocument();
  });

  test('shows incompatible dialog for incompatible cable selection', async () => {
    render(<QuickAddCablesDialog {...defaultProps} />);
    
    // Vai al tab incompatibili
    fireEvent.click(screen.getByText(/Cavi incompatibili/));
    
    await waitFor(() => {
      expect(screen.getByText('CAVO003')).toBeInTheDocument();
    });

    const selectButton = screen.getByText('Seleziona');
    fireEvent.click(selectButton);

    expect(screen.getByTestId('incompatible-reel-dialog')).toBeInTheDocument();
  });

  test('calculates total meters and shows OVER warning', async () => {
    render(<QuickAddCablesDialog {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('CAVO001')).toBeInTheDocument();
    });

    // Seleziona un cavo
    const selectButton = screen.getAllByText('Seleziona')[0];
    fireEvent.click(selectButton);

    // Inserisci metri che superano i residui
    const metriInput = screen.getByLabelText('Metri posati');
    fireEvent.change(metriInput, { target: { value: '150' } });

    await waitFor(() => {
      expect(screen.getByText(/OVER/)).toBeInTheDocument();
    });
  });

  test('validates required fields before saving', async () => {
    render(<QuickAddCablesDialog {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('CAVO001')).toBeInTheDocument();
    });

    // Seleziona un cavo ma non inserire metri
    const selectButton = screen.getAllByText('Seleziona')[0];
    fireEvent.click(selectButton);

    const saveButton = screen.getByText(/Salva/);
    expect(saveButton).toBeDisabled();
  });

  test('filters cables based on search term', async () => {
    render(<QuickAddCablesDialog {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('CAVO001')).toBeInTheDocument();
      expect(screen.getByText('CAVO002')).toBeInTheDocument();
    });

    const searchInput = screen.getByLabelText(/Ricerca intelligente cavi/);
    fireEvent.change(searchInput, { target: { value: 'CAVO001' } });

    await waitFor(() => {
      expect(screen.getByText('CAVO001')).toBeInTheDocument();
      expect(screen.queryByText('CAVO002')).not.toBeInTheDocument();
    });
  });
});
