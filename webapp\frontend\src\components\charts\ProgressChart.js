import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  ReferenceLine
} from 'recharts';
import { Box, Typography, Grid, Paper } from '@mui/material';

const COLORS = {
  primary: '#2c3e50',
  secondary: '#34495e',
  success: '#3498db',
  warning: '#5d6d7e',
  info: '#85929e',
  error: '#566573',
  light: '#ecf0f1',
  dark: '#2c3e50',
  accent: '#7fb3d3'
};

const ProgressChart = ({ data }) => {
  if (!data) return null;

  // Dati per il grafico a torta dell'avanzamento
  const progressData = [
    {
      name: 'Metri Posati',
      value: data.metri_posati,
      color: COLORS.success
    },
    {
      name: 'Metri Rimanenti',
      value: data.metri_da_posare,
      color: COLORS.warning
    }
  ];

  // Dati per il grafico a torta dei cavi
  const caviData = [
    {
      name: '<PERSON><PERSON>',
      value: data.cavi_posati,
      color: COLORS.success
    },
    {
      name: '<PERSON><PERSON>enti',
      value: data.cavi_rimanenti,
      color: COLORS.warning
    }
  ];

  // Dati per il grafico a barre delle metriche principali
  const metricsData = [
    {
      name: 'Metri',
      Totali: data.metri_totali,
      Posati: data.metri_posati,
      Rimanenti: data.metri_da_posare
    },
    {
      name: 'Cavi',
      Totali: data.totale_cavi,
      Posati: data.cavi_posati,
      Rimanenti: data.cavi_rimanenti
    }
  ];

  // Dati per il grafico temporale della posa recente (ordinati cronologicamente)
  const posaTrendData = data.posa_recente ?
    [...data.posa_recente]
      .sort((a, b) => new Date(a.data.split('/').reverse().join('-')) - new Date(b.data.split('/').reverse().join('-')))
      .map(posa => ({
        data: posa.data,
        metri: parseFloat(posa.metri) || 0
      })) : [];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`${label}`}</Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value}`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Paper className="report-chart-full-width" sx={{ p: 0, border: '1px solid #e0e0e0', borderRadius: 0, width: '100%' }}>
      <Grid container spacing={0}>
        {/* Grafico Posa Giornaliera - Andamento Temporale */}
        {data.posa_recente && data.posa_recente.length > 0 && (
          <Grid item xs={12}>
          <Box sx={{
            border: 'none',
            borderRadius: 0,
            overflow: 'hidden',
            height: '500px',
            width: '100%'
          }}>
            <Box sx={{
              bgcolor: '#f8f9fa',
              p: 2,
              borderBottom: '1px solid #e0e0e0'
            }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                  📈 Andamento Posa Giornaliera
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography variant="caption" sx={{ color: '#666' }}>
                    Media: {data.media_giornaliera || 0}m/giorno
                  </Typography>
                  {data.giorni_lavorativi_effettivi && (
                    <Typography variant="caption" sx={{ color: '#666' }}>
                      {data.giorni_lavorativi_effettivi} giorni di lavoro
                    </Typography>
                  )}
                </Box>
              </Box>
            </Box>
            <Box sx={{ p: 3, height: 'calc(100% - 80px)' }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={posaTrendData}
                  margin={{
                    top: 30,
                    right: 50,
                    left: 40,
                    bottom: 80
                  }}
                >
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="#e0e0e0"
                    horizontal={true}
                    vertical={false}
                  />
                  <XAxis
                    dataKey="data"
                    stroke="#666"
                    fontSize={12}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                    interval={0}
                  />
                  <YAxis
                    stroke="#666"
                    fontSize={12}
                    label={{
                      value: 'Metri Posati',
                      angle: -90,
                      position: 'insideLeft',
                      style: { textAnchor: 'middle', fill: '#666' }
                    }}
                  />
                  <Tooltip
                    content={({ active, payload, label }) => {
                      if (active && payload && payload.length) {
                        return (
                          <Box sx={{
                            bgcolor: 'white',
                            p: 2,
                            border: '1px solid #e0e0e0',
                            borderRadius: 1,
                            boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                          }}>
                            <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                              {label}
                            </Typography>
                            <Typography variant="body2" sx={{ color: COLORS.primary }}>
                              📏 {payload[0].value}m posati
                            </Typography>
                            {data.media_giornaliera && (
                              <Typography variant="caption" sx={{ color: '#666', display: 'block', mt: 0.5 }}>
                                Media: {data.media_giornaliera}m/giorno
                              </Typography>
                            )}
                          </Box>
                        );
                      }
                      return null;
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="metri"
                    stroke={COLORS.success}
                    strokeWidth={4}
                    dot={{
                      fill: COLORS.success,
                      strokeWidth: 3,
                      r: 6,
                      stroke: 'white'
                    }}
                    activeDot={{
                      r: 8,
                      fill: COLORS.success,
                      stroke: 'white',
                      strokeWidth: 3
                    }}
                  />
                  {/* Linea della media */}
                  {data.media_giornaliera && (
                    <ReferenceLine
                      y={data.media_giornaliera}
                      stroke={COLORS.warning}
                      strokeDasharray="8 4"
                      strokeWidth={3}
                      label={{
                        value: `Media: ${data.media_giornaliera}m`,
                        position: "topRight",
                        style: { fill: COLORS.warning, fontSize: '14px', fontWeight: 700 }
                      }}
                    />
                  )}
                </LineChart>
              </ResponsiveContainer>
            </Box>
          </Box>
        </Grid>
      )}

        {/* Messaggio quando non ci sono dati di posa */}
        {(!data.posa_recente || data.posa_recente.length === 0) && (
          <Grid item xs={12}>
            <Box sx={{
              border: 'none',
              borderRadius: 0,
              p: 4,
              textAlign: 'center',
              bgcolor: '#f8f9fa'
            }}>
              <Typography variant="h6" sx={{ color: '#666', mb: 1 }}>
                📊 Nessun Dato di Posa Disponibile
              </Typography>
              <Typography variant="body2" sx={{ color: '#999' }}>
                Il grafico dell'andamento temporale verrà visualizzato non appena saranno registrati dati di posa giornaliera.
              </Typography>
            </Box>
          </Grid>
        )}
      </Grid>
    </Paper>
  );
};

export default ProgressChart;
