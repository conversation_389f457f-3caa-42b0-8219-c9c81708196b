{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\QuickAddCablesDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, TextField, Checkbox, FormControlLabel, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Alert, IconButton, Chip, Tooltip, Tabs, Tab, List, ListItem, ListItemText, ListItemSecondaryAction, Divider, Card, CardContent, CardHeader, Badge } from '@mui/material';\nimport { Add as AddIcon, Delete as DeleteIcon, Save as SaveIcon, Info as InfoIcon, Warning as WarningIcon, Search as SearchIcon, Cable as CableIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n// Utility per verificare compatibilità tra cavo e bobina\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst isCompatible = (cavo, bobina) => {\n  return cavo.tipologia === bobina.tipologia && String(cavo.sezione) === String(bobina.sezione);\n};\n\n// Utility per ottenere il numero della bobina dall'ID\nconst getBobinaNumber = idBobina => {\n  if (!idBobina) return '';\n  const parts = idBobina.split('-');\n  return parts.length > 1 ? parts[1] : idBobina;\n};\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * Implementa sistema di doppia lista per cavi compatibili/incompatibili\n * con gestione intelligente delle incompatibilità e dialog moderni\n */\nconst QuickAddCablesDialog = ({\n  open,\n  onClose,\n  bobina,\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  var _bobina$metri_residui, _bobina$metri_residui2;\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per i dati separati\n  const [allCavi, setAllCavi] = useState([]);\n  const [caviCompatibili, setCaviCompatibili] = useState([]);\n  const [caviIncompatibili, setCaviIncompatibili] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n\n  // Stati per la UI\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);\n  const [incompatibleSelection, setIncompatibleSelection] = useState(null);\n  const [showOverDialog, setShowOverDialog] = useState(false);\n  const [overDialogData, setOverDialogData] = useState(null);\n\n  // Stati per la validazione\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina && cantiereId) {\n      loadCavi();\n      // Reset stati quando si apre il dialog\n      resetDialogState();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Reset dello stato del dialog\n  const resetDialogState = () => {\n    setSelectedCavi([]);\n    setCaviMetri({});\n    setErrors({});\n    setWarnings({});\n    setSearchTerm('');\n    setActiveTab(0);\n    setShowIncompatibleDialog(false);\n    setIncompatibleSelection(null);\n    setShowOverDialog(false);\n    setOverDialogData(null);\n  };\n\n  // Funzione per caricare i cavi con sistema di doppia lista\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi disponibili (non installati e non SPARE)\n      const caviDisponibili = caviData.filter(cavo => !isCableInstalled(cavo) && cavo.modificato_manualmente !== 3);\n\n      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)\n      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));\n      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));\n      setAllCavi(caviDisponibili);\n      setCaviCompatibili(compatibili);\n      setCaviIncompatibili(incompatibili);\n      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo compatibile\n  const handleCompatibleCavoSelect = cavo => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n      if (isSelected) {\n        // Conferma prima di rimuovere se ci sono metri inseriti\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        if (hasMetri) {\n          if (!window.confirm(`Rimuovere il cavo ${cavo.id_cavo} dalla selezione? I metri inseriti (${caviMetri[cavo.id_cavo]}m) andranno persi.`)) {\n            return prev;\n          }\n        }\n\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = {\n          ...caviMetri\n        };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n\n        // Rimuovi errori e warning\n        setErrors(prevErrors => {\n          const newErrors = {\n            ...prevErrors\n          };\n          delete newErrors[cavo.id_cavo];\n          return newErrors;\n        });\n        setWarnings(prevWarnings => {\n          const newWarnings = {\n            ...prevWarnings\n          };\n          delete newWarnings[cavo.id_cavo];\n          return newWarnings;\n        });\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce la selezione di un cavo incompatibile\n  const handleIncompatibleCavoSelect = cavo => {\n    setIncompatibleSelection({\n      cavo,\n      bobina\n    });\n    setShowIncompatibleDialog(true);\n  };\n\n  // Gestisce l'uso di una bobina incompatibile\n  const handleUseIncompatibleReel = () => {\n    if (incompatibleSelection) {\n      const {\n        cavo\n      } = incompatibleSelection;\n\n      // Aggiungi il cavo alla selezione con flag di incompatibilità\n      setSelectedCavi(prev => [...prev, {\n        ...cavo,\n        _isIncompatible: true\n      }]);\n      setShowIncompatibleDialog(false);\n      setIncompatibleSelection(null);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione. Sarà utilizzato con force_over.`);\n    }\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito in tempo reale\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo con feedback migliorato\n  const validateMetri = (cavoId, value) => {\n    const cavo = allCavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n    setWarnings(prev => {\n      const newWarnings = {\n        ...prev\n      };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      return true; // Non mostrare errore per input vuoto durante la digitazione\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina con calcolo in tempo reale\n    const metriTotaliRichiesti = Object.entries(caviMetri).filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n    .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n    const metriResiduiBobina = (bobina === null || bobina === void 0 ? void 0 : bobina.metri_residui) || 0;\n    if (metriTotaliRichiesti > metriResiduiBobina) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `Totale richiesto: ${metriTotaliRichiesti.toFixed(1)}m > Residui bobina: ${metriResiduiBobina.toFixed(1)}m (OVER)`\n      }));\n    }\n    return true;\n  };\n\n  // Valida tutti i metri inseriti con dialog moderno per OVER\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n      }\n    }\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n\n    // Se ci sono errori, non procedere\n    if (!isValid) {\n      return false;\n    }\n\n    // Verifica stato OVER della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    const metriResiduiBobina = (bobina === null || bobina === void 0 ? void 0 : bobina.metri_residui) || 0;\n    if (metriTotaliRichiesti > metriResiduiBobina) {\n      // Mostra dialog moderno invece di window.confirm\n      setOverDialogData({\n        metriTotaliRichiesti,\n        metriResiduiBobina,\n        selectedCavi: selectedCavi.length\n      });\n      setShowOverDialog(true);\n      return false; // Interrompi qui, il salvataggio continuerà dal dialog\n    }\n    return true;\n  };\n\n  // Gestisce il salvataggio dei dati con gestione migliorata\n  const handleSave = async (forceOver = false) => {\n    try {\n      // Validazione solo se non è un force over\n      if (!forceOver && !validateAllMetri()) {\n        return;\n      }\n      setSaving(true);\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina o incompatibilità\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const metriResiduiBobina = (bobina === null || bobina === void 0 ? void 0 : bobina.metri_residui) || 0;\n          const needsForceOver = forceOver || metriGiàUtilizzati + metriPosati > metriResiduiBobina || cavo._isIncompatible;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, bobina.id_bobina, needsForceOver);\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true,\n            wasIncompatible: cavo._isIncompatible\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato con messaggi migliorati\n      if (errors.length === 0) {\n        const incompatibleCount = results.filter(r => r.wasIncompatible).length;\n        let message = `${results.length} cavi aggiornati con successo`;\n        if (incompatibleCount > 0) {\n          message += ` (${incompatibleCount} incompatibili con force_over)`;\n        }\n        onSuccess(message);\n        onClose();\n      } else if (results.length > 0) {\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la conferma del dialog OVER\n  const handleOverDialogConfirm = () => {\n    setShowOverDialog(false);\n    setOverDialogData(null);\n    handleSave(true); // Procedi con force_over\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filterCavi = caviList => {\n    if (!searchTerm) return caviList;\n    return caviList.filter(cavo => cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()));\n  };\n  const filteredCompatibili = filterCavi(caviCompatibili);\n  const filteredIncompatibili = filterCavi(caviIncompatibili);\n\n  // Calcola statistiche con controlli di sicurezza\n  const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => {\n    const value = parseFloat(metri || 0);\n    return isNaN(value) ? sum : sum + value;\n  }, 0);\n  const metriResiduiBobina = (bobina === null || bobina === void 0 ? void 0 : bobina.metri_residui) || 0;\n  const isOverState = metriTotaliRichiesti > metriResiduiBobina;\n\n  // Componente per renderizzare una lista di cavi\n  const renderCaviList = (caviList, isCompatible = true) => {\n    if (caviLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this);\n    }\n    if (caviList.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          my: 2\n        },\n        children: isCompatible ? 'Nessun cavo compatibile disponibile.' : 'Nessun cavo incompatibile trovato.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(List, {\n      dense: true,\n      children: caviList.map(cavo => {\n        const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        const hasError = errors[cavo.id_cavo];\n        const hasWarning = warnings[cavo.id_cavo];\n        return /*#__PURE__*/_jsxDEV(ListItem, {\n          sx: {\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            mb: 1,\n            bgcolor: isSelected ? 'rgba(33, 150, 243, 0.1)' : '#f5f7fa',\n            '&:hover': {\n              bgcolor: isSelected ? 'rgba(33, 150, 243, 0.2)' : 'rgba(33, 150, 243, 0.05)'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                fontSize: \"small\",\n                color: isCompatible ? 'success' : 'warning'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                size: \"small\",\n                label: cavo.tipologia || 'N/A',\n                color: isCompatible ? 'success' : 'warning',\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 21\n              }, this), !isCompatible && /*#__PURE__*/_jsxDEV(Chip, {\n                size: \"small\",\n                label: \"INCOMPATIBILE\",\n                color: \"error\",\n                variant: \"filled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this),\n            secondary: /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [cavo.ubicazione_partenza || 'N/A', \" \\u2192 \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Formazione: \", cavo.sezione || 'N/A', \" | Metri teorici: \", cavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 21\n              }, this), isSelected && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  size: \"small\",\n                  type: \"number\",\n                  label: \"Metri posati\",\n                  value: caviMetri[cavo.id_cavo] || '',\n                  onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n                  error: !!hasError,\n                  helperText: hasError || hasWarning,\n                  FormHelperTextProps: {\n                    sx: {\n                      color: hasWarning && !hasError ? 'warning.main' : 'error.main'\n                    }\n                  },\n                  sx: {\n                    width: '200px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              variant: isSelected ? 'contained' : 'outlined',\n              color: isCompatible ? 'primary' : 'warning',\n              onClick: () => isCompatible ? handleCompatibleCavoSelect(cavo) : handleIncompatibleCavoSelect(cavo),\n              startIcon: isSelected ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 43\n              }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 65\n              }, this),\n              children: isSelected ? 'Selezionato' : 'Seleziona'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 15\n          }, this)]\n        }, cavo.id_cavo, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"xl\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Aggiungi cavi alla bobina \", getBobinaNumber(bobina === null || bobina === void 0 ? void 0 : bobina.id_bobina)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: !bobina ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: \"Nessuna bobina selezionata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            title: \"Dettagli bobina\",\n            titleTypographyProps: {\n              variant: 'subtitle1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pt: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"ID Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: bobina.id_bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: bobina.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Formazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: bobina.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Metri residui\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  color: isOverState ? 'error.main' : 'text.primary',\n                  sx: {\n                    fontWeight: isOverState ? 'bold' : 'normal'\n                  },\n                  children: [((_bobina$metri_residui = bobina.metri_residui) === null || _bobina$metri_residui === void 0 ? void 0 : _bobina$metri_residui.toFixed(1)) || '0', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: bobina.stato_bobina || 'N/D',\n                  size: \"small\",\n                  color: bobina.stato_bobina === 'Disponibile' ? 'success' : bobina.stato_bobina === 'In uso' ? 'primary' : bobina.stato_bobina === 'Over' ? 'error' : bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Ricerca intelligente cavi\",\n          variant: \"outlined\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"Cerca per ID, tipologia, ubicazione...\",\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(SearchIcon, {\n              sx: {\n                mr: 1,\n                color: 'text.secondary'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 33\n            }, this)\n          },\n          sx: {\n            mb: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            borderBottom: 1,\n            borderColor: 'divider',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            value: activeTab,\n            onChange: (e, newValue) => setActiveTab(newValue),\n            children: [/*#__PURE__*/_jsxDEV(Tab, {\n              label: /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: filteredCompatibili.length,\n                color: \"success\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 634,\n                    columnNumber: 25\n                  }, this), \"Cavi compatibili\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: filteredIncompatibili.length,\n                color: \"warning\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 25\n                  }, this), \"Cavi incompatibili\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            minHeight: 300,\n            maxHeight: 400,\n            overflow: 'auto'\n          },\n          children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Cavi compatibili con tipologia \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: bobina.tipologia\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 52\n              }, this), \" e formazione \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: bobina.sezione\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 101\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 19\n            }, this), renderCaviList(filteredCompatibili, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 17\n          }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [\"I cavi incompatibili possono essere utilizzati con \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"force_over\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 74\n                }, this), \", ma potrebbero non rispettare le specifiche tecniche della bobina.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 19\n            }, this), renderCaviList(filteredIncompatibili, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 13\n        }, this), selectedCavi.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            mt: 3,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            title: `Riepilogo selezione (${selectedCavi.length} cavi)`,\n            titleTypographyProps: {\n              variant: 'subtitle1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pt: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Metri totali richiesti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: isOverState ? 'error.main' : 'text.primary'\n                  },\n                  children: [metriTotaliRichiesti.toFixed(1), \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Metri residui bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'bold'\n                  },\n                  children: [((_bobina$metri_residui2 = bobina.metri_residui) === null || _bobina$metri_residui2 === void 0 ? void 0 : _bobina$metri_residui2.toFixed(1)) || '0', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Differenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: isOverState ? 'error.main' : 'success.main'\n                  },\n                  children: [(bobina.metri_residui - metriTotaliRichiesti).toFixed(1), \" m\", isOverState && ' (OVER)']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 19\n            }, this), isOverState && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"ATTENZIONE:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 25\n                }, this), \" I metri richiesti superano i metri residui della bobina. La bobina andr\\xE0 in stato OVER.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: selectedCavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1,\n                  mb: 1,\n                  bgcolor: cavo._isIncompatible ? 'warning.light' : 'background.paper'\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      children: cavo.id_cavo\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 742,\n                      columnNumber: 31\n                    }, this), cavo._isIncompatible && /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: \"INCOMPATIBILE\",\n                      color: \"warning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 744,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 29\n                  }, this),\n                  secondary: `${caviMetri[cavo.id_cavo] || '0'} metri posati`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    color: \"error\",\n                    onClick: () => handleCompatibleCavoSelect(cavo),\n                    title: \"Rimuovi dalla selezione\",\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 757,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 25\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 15\n        }, this), Object.keys(warnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Avvisi:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            dense: true,\n            children: Object.entries(warnings).map(([cavoId, warning]) => /*#__PURE__*/_jsxDEV(ListItem, {\n              sx: {\n                py: 0\n              },\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `${cavoId}: ${warning}`,\n                primaryTypographyProps: {\n                  variant: 'body2'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 23\n              }, this)\n            }, cavoId, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Istruzioni:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 17\n            }, this), \" Seleziona i cavi dalle liste sopra e inserisci i metri posati. I cavi compatibili sono consigliati, quelli incompatibili richiedono conferma esplicita.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        px: 3,\n        py: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        disabled: saving,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => handleSave(false),\n        color: \"primary\",\n        variant: \"contained\",\n        disabled: saving || selectedCavi.length === 0 || Object.keys(errors).length > 0,\n        startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 64\n        }, this),\n        children: saving ? 'Salvataggio...' : `Salva ${selectedCavi.length} cavi`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 799,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 795,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleDialog,\n      onClose: () => {\n        setShowIncompatibleDialog(false);\n        setIncompatibleSelection(null);\n      },\n      cavo: incompatibleSelection === null || incompatibleSelection === void 0 ? void 0 : incompatibleSelection.cavo,\n      bobina: incompatibleSelection === null || incompatibleSelection === void 0 ? void 0 : incompatibleSelection.bobina,\n      onConfirm: handleUseIncompatibleReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 811,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showOverDialog,\n      onClose: () => setShowOverDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 831,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Conferma stato OVER\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 830,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 829,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: overDialogData && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ATTENZIONE:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 19\n              }, this), \" L'operazione porter\\xE0 la bobina in stato OVER.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri totali richiesti:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 19\n              }, this), \" \", overDialogData.metriTotaliRichiesti.toFixed(1), \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri residui bobina:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 19\n              }, this), \" \", overDialogData.metriResiduiBobina.toFixed(1), \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Eccedenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 19\n              }, this), \" \", (overDialogData.metriTotaliRichiesti - overDialogData.metriResiduiBobina).toFixed(1), \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Cavi coinvolti:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 19\n              }, this), \" \", overDialogData.selectedCavi]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Vuoi continuare con l'operazione? La bobina andr\\xE0 in stato OVER.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 835,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowOverDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleOverDialogConfirm,\n          color: \"warning\",\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 24\n          }, this),\n          children: \"Continua con OVER\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 863,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 823,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 552,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickAddCablesDialog, \"AbIIuLGdIuO/w2uebXwSQvCbijs=\");\n_c = QuickAddCablesDialog;\nexport default QuickAddCablesDialog;\nvar _c;\n$RefreshReg$(_c, \"QuickAddCablesDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "TextField", "Checkbox", "FormControlLabel", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "CircularProgress", "<PERSON><PERSON>", "IconButton", "Chip", "<PERSON><PERSON><PERSON>", "Tabs", "Tab", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Divider", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Badge", "Add", "AddIcon", "Delete", "DeleteIcon", "Save", "SaveIcon", "Info", "InfoIcon", "Warning", "WarningIcon", "Search", "SearchIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "caviService", "determineCableState", "getCableStateColor", "isCableInstalled", "IncompatibleReelDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "isCompatible", "cavo", "bobina", "tipologia", "String", "sezione", "getBobinaNumber", "idBobina", "parts", "split", "length", "QuickAddCablesDialog", "open", "onClose", "cantiereId", "onSuccess", "onError", "_s", "_bobina$metri_residui", "_bobina$metri_residui2", "loading", "setLoading", "caviLoading", "setCaviLoading", "saving", "setSaving", "allCavi", "set<PERSON><PERSON><PERSON><PERSON>", "caviCompatibili", "setCaviCompatibili", "caviIncompatibili", "setCaviIncompatibili", "<PERSON><PERSON><PERSON>", "setSelectedCavi", "caviMetri", "setCaviMetri", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "showIncompatibleDialog", "setShowIncompatibleDialog", "incompatibleSelection", "setIncompatibleSelection", "showOverDialog", "setShowOverDialog", "overDialogData", "setOverDialogData", "errors", "setErrors", "warnings", "setWarnings", "loadCavi", "resetDialogState", "caviData", "get<PERSON><PERSON>", "caviDisponibili", "filter", "modificato_manualmente", "compatibili", "incompatibili", "console", "log", "error", "message", "handleCompatibleCavoSelect", "prev", "isSelected", "some", "c", "id_cavo", "has<PERSON><PERSON>ri", "trim", "window", "confirm", "newSelected", "newCaviMetri", "prevErrors", "newErrors", "prevWarnings", "newWarnings", "handleIncompatibleCavoSelect", "handleUseIncompatibleReel", "_isIncompatible", "handleMetriChange", "cavoId", "value", "validate<PERSON>etri", "find", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "metriTotaliRichiesti", "Object", "entries", "id", "_", "reduce", "sum", "metri", "metriResiduiBobina", "metri_residui", "toFixed", "validateAllMetri", "<PERSON><PERSON><PERSON><PERSON>", "values", "handleSave", "forceOver", "results", "metriGiàUtilizzati", "r", "needsForceOver", "result", "updateMetri<PERSON><PERSON><PERSON>", "id_bobina", "push", "success", "wasIncompatible", "incompatibleCount", "map", "e", "join", "handleOverDialogConfirm", "filterCavi", "caviList", "toLowerCase", "includes", "ubicazione_partenza", "ubicazione_arrivo", "filteredCompatibili", "filteredIncompatibili", "isOverState", "renderCaviList", "sx", "display", "justifyContent", "my", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "dense", "<PERSON><PERSON><PERSON><PERSON>", "hasWarning", "border", "borderRadius", "mb", "bgcolor", "primary", "alignItems", "gap", "fontSize", "color", "variant", "size", "label", "secondary", "mt", "type", "onChange", "target", "helperText", "FormHelperTextProps", "width", "onClick", "startIcon", "max<PERSON><PERSON><PERSON>", "fullWidth", "title", "titleTypographyProps", "pt", "flexWrap", "fontWeight", "stato_bobina", "placeholder", "InputProps", "startAdornment", "mr", "borderBottom", "borderColor", "newValue", "badgeContent", "minHeight", "maxHeight", "overflow", "keys", "gutterBottom", "warning", "py", "primaryTypographyProps", "px", "disabled", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/QuickAddCablesDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  TextField,\n  Checkbox,\n  FormControlLabel,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  CircularProgress,\n  Alert,\n  IconButton,\n  Chip,\n  Tooltip,\n  Tabs,\n  Tab,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Divider,\n  Card,\n  CardContent,\n  CardHeader,\n  Badge\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Delete as DeleteIcon,\n  Save as SaveIcon,\n  Info as InfoIcon,\n  Warning as WarningIcon,\n  Search as SearchIcon,\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n// Utility per verificare compatibilità tra cavo e bobina\nconst isCompatible = (cavo, bobina) => {\n  return cavo.tipologia === bobina.tipologia &&\n         String(cavo.sezione) === String(bobina.sezione);\n};\n\n// Utility per ottenere il numero della bobina dall'ID\nconst getBobinaNumber = (idBobina) => {\n  if (!idBobina) return '';\n  const parts = idBobina.split('-');\n  return parts.length > 1 ? parts[1] : idBobina;\n};\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * Implementa sistema di doppia lista per cavi compatibili/incompatibili\n * con gestione intelligente delle incompatibilità e dialog moderni\n */\nconst QuickAddCablesDialog = ({ open, onClose, bobina, cantiereId, onSuccess, onError }) => {\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per i dati separati\n  const [allCavi, setAllCavi] = useState([]);\n  const [caviCompatibili, setCaviCompatibili] = useState([]);\n  const [caviIncompatibili, setCaviIncompatibili] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n\n  // Stati per la UI\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);\n  const [incompatibleSelection, setIncompatibleSelection] = useState(null);\n  const [showOverDialog, setShowOverDialog] = useState(false);\n  const [overDialogData, setOverDialogData] = useState(null);\n\n  // Stati per la validazione\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina && cantiereId) {\n      loadCavi();\n      // Reset stati quando si apre il dialog\n      resetDialogState();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Reset dello stato del dialog\n  const resetDialogState = () => {\n    setSelectedCavi([]);\n    setCaviMetri({});\n    setErrors({});\n    setWarnings({});\n    setSearchTerm('');\n    setActiveTab(0);\n    setShowIncompatibleDialog(false);\n    setIncompatibleSelection(null);\n    setShowOverDialog(false);\n    setOverDialogData(null);\n  };\n\n  // Funzione per caricare i cavi con sistema di doppia lista\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi disponibili (non installati e non SPARE)\n      const caviDisponibili = caviData.filter(cavo =>\n        !isCableInstalled(cavo) &&\n        cavo.modificato_manualmente !== 3\n      );\n\n      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)\n      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));\n      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));\n\n      setAllCavi(caviDisponibili);\n      setCaviCompatibili(compatibili);\n      setCaviIncompatibili(incompatibili);\n\n      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo compatibile\n  const handleCompatibleCavoSelect = (cavo) => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n\n      if (isSelected) {\n        // Conferma prima di rimuovere se ci sono metri inseriti\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        if (hasMetri) {\n          if (!window.confirm(`Rimuovere il cavo ${cavo.id_cavo} dalla selezione? I metri inseriti (${caviMetri[cavo.id_cavo]}m) andranno persi.`)) {\n            return prev;\n          }\n        }\n\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = { ...caviMetri };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n\n        // Rimuovi errori e warning\n        setErrors(prevErrors => {\n          const newErrors = { ...prevErrors };\n          delete newErrors[cavo.id_cavo];\n          return newErrors;\n        });\n        setWarnings(prevWarnings => {\n          const newWarnings = { ...prevWarnings };\n          delete newWarnings[cavo.id_cavo];\n          return newWarnings;\n        });\n\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce la selezione di un cavo incompatibile\n  const handleIncompatibleCavoSelect = (cavo) => {\n    setIncompatibleSelection({ cavo, bobina });\n    setShowIncompatibleDialog(true);\n  };\n\n  // Gestisce l'uso di una bobina incompatibile\n  const handleUseIncompatibleReel = () => {\n    if (incompatibleSelection) {\n      const { cavo } = incompatibleSelection;\n\n      // Aggiungi il cavo alla selezione con flag di incompatibilità\n      setSelectedCavi(prev => [...prev, { ...cavo, _isIncompatible: true }]);\n\n      setShowIncompatibleDialog(false);\n      setIncompatibleSelection(null);\n\n      onSuccess?.(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione. Sarà utilizzato con force_over.`);\n    }\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito in tempo reale\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo con feedback migliorato\n  const validateMetri = (cavoId, value) => {\n    const cavo = allCavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = { ...prev };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n\n    setWarnings(prev => {\n      const newWarnings = { ...prev };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      return true; // Non mostrare errore per input vuoto durante la digitazione\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina con calcolo in tempo reale\n    const metriTotaliRichiesti = Object.entries(caviMetri)\n      .filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n      .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n\n    const metriResiduiBobina = bobina?.metri_residui || 0;\n    if (metriTotaliRichiesti > metriResiduiBobina) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `Totale richiesto: ${metriTotaliRichiesti.toFixed(1)}m > Residui bobina: ${metriResiduiBobina.toFixed(1)}m (OVER)`\n      }));\n    }\n\n    return true;\n  };\n\n  // Valida tutti i metri inseriti con dialog moderno per OVER\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n      }\n    }\n\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n\n    // Se ci sono errori, non procedere\n    if (!isValid) {\n      return false;\n    }\n\n    // Verifica stato OVER della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    const metriResiduiBobina = bobina?.metri_residui || 0;\n    if (metriTotaliRichiesti > metriResiduiBobina) {\n      // Mostra dialog moderno invece di window.confirm\n      setOverDialogData({\n        metriTotaliRichiesti,\n        metriResiduiBobina,\n        selectedCavi: selectedCavi.length\n      });\n      setShowOverDialog(true);\n      return false; // Interrompi qui, il salvataggio continuerà dal dialog\n    }\n\n    return true;\n  };\n\n  // Gestisce il salvataggio dei dati con gestione migliorata\n  const handleSave = async (forceOver = false) => {\n    try {\n      // Validazione solo se non è un force over\n      if (!forceOver && !validateAllMetri()) {\n        return;\n      }\n\n      setSaving(true);\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina o incompatibilità\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const metriResiduiBobina = bobina?.metri_residui || 0;\n          const needsForceOver = forceOver || (metriGiàUtilizzati + metriPosati) > metriResiduiBobina || cavo._isIncompatible;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(\n            cantiereId,\n            cavo.id_cavo,\n            metriPosati,\n            bobina.id_bobina,\n            needsForceOver\n          );\n\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true,\n            wasIncompatible: cavo._isIncompatible\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato con messaggi migliorati\n      if (errors.length === 0) {\n        const incompatibleCount = results.filter(r => r.wasIncompatible).length;\n        let message = `${results.length} cavi aggiornati con successo`;\n        if (incompatibleCount > 0) {\n          message += ` (${incompatibleCount} incompatibili con force_over)`;\n        }\n        onSuccess(message);\n        onClose();\n      } else if (results.length > 0) {\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la conferma del dialog OVER\n  const handleOverDialogConfirm = () => {\n    setShowOverDialog(false);\n    setOverDialogData(null);\n    handleSave(true); // Procedi con force_over\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filterCavi = (caviList) => {\n    if (!searchTerm) return caviList;\n\n    return caviList.filter(cavo =>\n      cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()))\n    );\n  };\n\n  const filteredCompatibili = filterCavi(caviCompatibili);\n  const filteredIncompatibili = filterCavi(caviIncompatibili);\n\n  // Calcola statistiche con controlli di sicurezza\n  const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => {\n    const value = parseFloat(metri || 0);\n    return isNaN(value) ? sum : sum + value;\n  }, 0);\n\n  const metriResiduiBobina = bobina?.metri_residui || 0;\n  const isOverState = metriTotaliRichiesti > metriResiduiBobina;\n\n  // Componente per renderizzare una lista di cavi\n  const renderCaviList = (caviList, isCompatible = true) => {\n    if (caviLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    if (caviList.length === 0) {\n      return (\n        <Alert severity=\"info\" sx={{ my: 2 }}>\n          {isCompatible ? 'Nessun cavo compatibile disponibile.' : 'Nessun cavo incompatibile trovato.'}\n        </Alert>\n      );\n    }\n\n    return (\n      <List dense>\n        {caviList.map((cavo) => {\n          const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n          const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n          const hasError = errors[cavo.id_cavo];\n          const hasWarning = warnings[cavo.id_cavo];\n\n          return (\n            <ListItem\n              key={cavo.id_cavo}\n              sx={{\n                border: '1px solid #e0e0e0',\n                borderRadius: 1,\n                mb: 1,\n                bgcolor: isSelected ? 'rgba(33, 150, 243, 0.1)' : '#f5f7fa',\n                '&:hover': {\n                  bgcolor: isSelected ? 'rgba(33, 150, 243, 0.2)' : 'rgba(33, 150, 243, 0.05)'\n                }\n              }}\n            >\n              <ListItemText\n                primary={\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <CableIcon fontSize=\"small\" color={isCompatible ? 'success' : 'warning'} />\n                    <Typography variant=\"subtitle2\">{cavo.id_cavo}</Typography>\n                    <Chip\n                      size=\"small\"\n                      label={cavo.tipologia || 'N/A'}\n                      color={isCompatible ? 'success' : 'warning'}\n                      variant=\"outlined\"\n                    />\n                    {!isCompatible && (\n                      <Chip\n                        size=\"small\"\n                        label=\"INCOMPATIBILE\"\n                        color=\"error\"\n                        variant=\"filled\"\n                      />\n                    )}\n                  </Box>\n                }\n                secondary={\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {cavo.ubicazione_partenza || 'N/A'} → {cavo.ubicazione_arrivo || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Formazione: {cavo.sezione || 'N/A'} | Metri teorici: {cavo.metri_teorici || 'N/A'}\n                    </Typography>\n                    {isSelected && (\n                      <Box sx={{ mt: 1 }}>\n                        <TextField\n                          size=\"small\"\n                          type=\"number\"\n                          label=\"Metri posati\"\n                          value={caviMetri[cavo.id_cavo] || ''}\n                          onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                          error={!!hasError}\n                          helperText={hasError || hasWarning}\n                          FormHelperTextProps={{\n                            sx: { color: hasWarning && !hasError ? 'warning.main' : 'error.main' }\n                          }}\n                          sx={{ width: '200px' }}\n                        />\n                      </Box>\n                    )}\n                  </Box>\n                }\n              />\n              <ListItemSecondaryAction>\n                <Button\n                  size=\"small\"\n                  variant={isSelected ? 'contained' : 'outlined'}\n                  color={isCompatible ? 'primary' : 'warning'}\n                  onClick={() => isCompatible ? handleCompatibleCavoSelect(cavo) : handleIncompatibleCavoSelect(cavo)}\n                  startIcon={isSelected ? <CheckCircleIcon /> : <AddIcon />}\n                >\n                  {isSelected ? 'Selezionato' : 'Seleziona'}\n                </Button>\n              </ListItemSecondaryAction>\n            </ListItem>\n          );\n        })}\n      </List>\n    );\n  };\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"xl\" fullWidth>\n      <DialogTitle>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <CableIcon />\n          <Typography variant=\"h6\">\n            Aggiungi cavi alla bobina {getBobinaNumber(bobina?.id_bobina)}\n          </Typography>\n        </Box>\n      </DialogTitle>\n      <DialogContent>\n        {!bobina ? (\n          <Alert severity=\"error\">Nessuna bobina selezionata</Alert>\n        ) : (\n          <>\n            {/* Informazioni sulla bobina */}\n            <Card sx={{ mb: 3 }}>\n              <CardHeader\n                title=\"Dettagli bobina\"\n                titleTypographyProps={{ variant: 'subtitle1' }}\n              />\n              <CardContent sx={{ pt: 0 }}>\n                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">ID Bobina</Typography>\n                    <Typography variant=\"body1\">{bobina.id_bobina}</Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Tipologia</Typography>\n                    <Typography variant=\"body1\">{bobina.tipologia || 'N/A'}</Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Formazione</Typography>\n                    <Typography variant=\"body1\">{bobina.sezione || 'N/A'}</Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n                    <Typography\n                      variant=\"body1\"\n                      color={isOverState ? 'error.main' : 'text.primary'}\n                      sx={{ fontWeight: isOverState ? 'bold' : 'normal' }}\n                    >\n                      {bobina.metri_residui?.toFixed(1) || '0'} m\n                    </Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Stato</Typography>\n                    <Chip\n                      label={bobina.stato_bobina || 'N/D'}\n                      size=\"small\"\n                      color={\n                        bobina.stato_bobina === 'Disponibile' ? 'success' :\n                        bobina.stato_bobina === 'In uso' ? 'primary' :\n                        bobina.stato_bobina === 'Over' ? 'error' :\n                        bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                      }\n                    />\n                  </Box>\n                </Box>\n              </CardContent>\n            </Card>\n\n            {/* Ricerca cavi */}\n            <TextField\n              fullWidth\n              label=\"Ricerca intelligente cavi\"\n              variant=\"outlined\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"Cerca per ID, tipologia, ubicazione...\"\n              InputProps={{\n                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />\n              }}\n              sx={{ mb: 3 }}\n            />\n\n            {/* Sistema di tabs per cavi compatibili/incompatibili */}\n            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>\n              <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>\n                <Tab\n                  label={\n                    <Badge badgeContent={filteredCompatibili.length} color=\"success\">\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                        <CheckCircleIcon fontSize=\"small\" />\n                        Cavi compatibili\n                      </Box>\n                    </Badge>\n                  }\n                />\n                <Tab\n                  label={\n                    <Badge badgeContent={filteredIncompatibili.length} color=\"warning\">\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                        <WarningIcon fontSize=\"small\" />\n                        Cavi incompatibili\n                      </Box>\n                    </Badge>\n                  }\n                />\n              </Tabs>\n            </Box>\n\n            {/* Contenuto dei tabs */}\n            <Box sx={{ minHeight: 300, maxHeight: 400, overflow: 'auto' }}>\n              {activeTab === 0 && (\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Cavi compatibili con tipologia <strong>{bobina.tipologia}</strong> e formazione <strong>{bobina.sezione}</strong>\n                  </Typography>\n                  {renderCaviList(filteredCompatibili, true)}\n                </Box>\n              )}\n              {activeTab === 1 && (\n                <Box>\n                  <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                    <Typography variant=\"body2\">\n                      I cavi incompatibili possono essere utilizzati con <strong>force_over</strong>,\n                      ma potrebbero non rispettare le specifiche tecniche della bobina.\n                    </Typography>\n                  </Alert>\n                  {renderCaviList(filteredIncompatibili, false)}\n                </Box>\n              )}\n            </Box>\n\n            {/* Riepilogo selezione migliorato */}\n            {selectedCavi.length > 0 && (\n              <Card sx={{ mt: 3, mb: 2 }}>\n                <CardHeader\n                  title={`Riepilogo selezione (${selectedCavi.length} cavi)`}\n                  titleTypographyProps={{ variant: 'subtitle1' }}\n                />\n                <CardContent sx={{ pt: 0 }}>\n                  <Box sx={{ mb: 2 }}>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                      <Typography variant=\"body2\">Metri totali richiesti:</Typography>\n                      <Typography\n                        variant=\"body2\"\n                        sx={{\n                          fontWeight: 'bold',\n                          color: isOverState ? 'error.main' : 'text.primary'\n                        }}\n                      >\n                        {metriTotaliRichiesti.toFixed(1)} m\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                      <Typography variant=\"body2\">Metri residui bobina:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                        {bobina.metri_residui?.toFixed(1) || '0'} m\n                      </Typography>\n                    </Box>\n                    <Divider sx={{ my: 1 }} />\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <Typography variant=\"body2\">Differenza:</Typography>\n                      <Typography\n                        variant=\"body2\"\n                        sx={{\n                          fontWeight: 'bold',\n                          color: isOverState ? 'error.main' : 'success.main'\n                        }}\n                      >\n                        {(bobina.metri_residui - metriTotaliRichiesti).toFixed(1)} m\n                        {isOverState && ' (OVER)'}\n                      </Typography>\n                    </Box>\n                  </Box>\n\n                  {isOverState && (\n                    <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                      <Typography variant=\"body2\">\n                        <strong>ATTENZIONE:</strong> I metri richiesti superano i metri residui della bobina.\n                        La bobina andrà in stato OVER.\n                      </Typography>\n                    </Alert>\n                  )}\n\n                  <List dense>\n                    {selectedCavi.map((cavo) => (\n                      <ListItem\n                        key={cavo.id_cavo}\n                        sx={{\n                          border: '1px solid #e0e0e0',\n                          borderRadius: 1,\n                          mb: 1,\n                          bgcolor: cavo._isIncompatible ? 'warning.light' : 'background.paper'\n                        }}\n                      >\n                        <ListItemText\n                          primary={\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                              <Typography variant=\"subtitle2\">{cavo.id_cavo}</Typography>\n                              {cavo._isIncompatible && (\n                                <Chip size=\"small\" label=\"INCOMPATIBILE\" color=\"warning\" />\n                              )}\n                            </Box>\n                          }\n                          secondary={`${caviMetri[cavo.id_cavo] || '0'} metri posati`}\n                        />\n                        <ListItemSecondaryAction>\n                          <IconButton\n                            size=\"small\"\n                            color=\"error\"\n                            onClick={() => handleCompatibleCavoSelect(cavo)}\n                            title=\"Rimuovi dalla selezione\"\n                          >\n                            <DeleteIcon fontSize=\"small\" />\n                          </IconButton>\n                        </ListItemSecondaryAction>\n                      </ListItem>\n                    ))}\n                  </List>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Avvisi globali */}\n            {Object.keys(warnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle2\" gutterBottom>Avvisi:</Typography>\n                <List dense>\n                  {Object.entries(warnings).map(([cavoId, warning]) => (\n                    <ListItem key={cavoId} sx={{ py: 0 }}>\n                      <ListItemText\n                        primary={`${cavoId}: ${warning}`}\n                        primaryTypographyProps={{ variant: 'body2' }}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Alert>\n            )}\n\n            {/* Istruzioni */}\n            <Alert severity=\"info\" sx={{ mt: 2 }}>\n              <Typography variant=\"body2\">\n                <strong>Istruzioni:</strong> Seleziona i cavi dalle liste sopra e inserisci i metri posati.\n                I cavi compatibili sono consigliati, quelli incompatibili richiedono conferma esplicita.\n              </Typography>\n            </Alert>\n          </>\n        )}\n      </DialogContent>\n\n      <DialogActions sx={{ px: 3, py: 2 }}>\n        <Button onClick={onClose} disabled={saving}>\n          Annulla\n        </Button>\n        <Button\n          onClick={() => handleSave(false)}\n          color=\"primary\"\n          variant=\"contained\"\n          disabled={saving || selectedCavi.length === 0 || Object.keys(errors).length > 0}\n          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}\n        >\n          {saving ? 'Salvataggio...' : `Salva ${selectedCavi.length} cavi`}\n        </Button>\n      </DialogActions>\n\n      {/* Dialog per incompatibilità */}\n      <IncompatibleReelDialog\n        open={showIncompatibleDialog}\n        onClose={() => {\n          setShowIncompatibleDialog(false);\n          setIncompatibleSelection(null);\n        }}\n        cavo={incompatibleSelection?.cavo}\n        bobina={incompatibleSelection?.bobina}\n        onConfirm={handleUseIncompatibleReel}\n      />\n\n      {/* Dialog moderno per conferma OVER */}\n      <Dialog\n        open={showOverDialog}\n        onClose={() => setShowOverDialog(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Conferma stato OVER</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {overDialogData && (\n            <>\n              <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                <Typography variant=\"body1\" gutterBottom>\n                  <strong>ATTENZIONE:</strong> L'operazione porterà la bobina in stato OVER.\n                </Typography>\n              </Alert>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\" gutterBottom>\n                  <strong>Metri totali richiesti:</strong> {overDialogData.metriTotaliRichiesti.toFixed(1)} m\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  <strong>Metri residui bobina:</strong> {overDialogData.metriResiduiBobina.toFixed(1)} m\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  <strong>Eccedenza:</strong> {(overDialogData.metriTotaliRichiesti - overDialogData.metriResiduiBobina).toFixed(1)} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Cavi coinvolti:</strong> {overDialogData.selectedCavi}\n                </Typography>\n              </Box>\n              <Typography variant=\"body2\">\n                Vuoi continuare con l'operazione? La bobina andrà in stato OVER.\n              </Typography>\n            </>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowOverDialog(false)}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleOverDialogConfirm}\n            color=\"warning\"\n            variant=\"contained\"\n            startIcon={<WarningIcon />}\n          >\n            Continua con OVER\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Dialog>\n  );\n};\n\nexport default QuickAddCablesDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,gBAAgB,QAAQ,wBAAwB;AAClG,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK;EACrC,OAAOD,IAAI,CAACE,SAAS,KAAKD,MAAM,CAACC,SAAS,IACnCC,MAAM,CAACH,IAAI,CAACI,OAAO,CAAC,KAAKD,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC;AACxD,CAAC;;AAED;AACA,MAAMC,eAAe,GAAIC,QAAQ,IAAK;EACpC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;EACxB,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;EACjC,OAAOD,KAAK,CAACE,MAAM,GAAG,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAGD,QAAQ;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMI,oBAAoB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEX,MAAM;EAAEY,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC1F;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsF,MAAM,EAAEC,SAAS,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACwF,OAAO,EAAEC,UAAU,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8F,YAAY,EAAEC,eAAe,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgG,SAAS,EAAEC,YAAY,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACkG,SAAS,EAAEC,YAAY,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACoG,UAAU,EAAEC,aAAa,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACwG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAAC0G,cAAc,EAAEC,iBAAiB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4G,cAAc,EAAEC,iBAAiB,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM,CAAC8G,MAAM,EAAEC,SAAS,CAAC,GAAG/G,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACgH,QAAQ,EAAEC,WAAW,CAAC,GAAGjH,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIyE,IAAI,IAAIV,MAAM,IAAIY,UAAU,EAAE;MAChCsC,QAAQ,CAAC,CAAC;MACV;MACAC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACzC,IAAI,EAAEV,MAAM,EAAEY,UAAU,CAAC,CAAC;;EAE9B;EACA,MAAMuC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpB,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,CAAC,CAAC,CAAC;IAChBc,SAAS,CAAC,CAAC,CAAC,CAAC;IACbE,WAAW,CAAC,CAAC,CAAC,CAAC;IACfZ,aAAa,CAAC,EAAE,CAAC;IACjBF,YAAY,CAAC,CAAC,CAAC;IACfI,yBAAyB,CAAC,KAAK,CAAC;IAChCE,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMK,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF7B,cAAc,CAAC,IAAI,CAAC;MACpB,MAAM+B,QAAQ,GAAG,MAAM/D,WAAW,CAACgE,OAAO,CAACzC,UAAU,CAAC;;MAEtD;MACA,MAAM0C,eAAe,GAAGF,QAAQ,CAACG,MAAM,CAACxD,IAAI,IAC1C,CAACP,gBAAgB,CAACO,IAAI,CAAC,IACvBA,IAAI,CAACyD,sBAAsB,KAAK,CAClC,CAAC;;MAED;MACA,MAAMC,WAAW,GAAGH,eAAe,CAACC,MAAM,CAACxD,IAAI,IAAID,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;MAC9E,MAAM0D,aAAa,GAAGJ,eAAe,CAACC,MAAM,CAACxD,IAAI,IAAI,CAACD,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;MAEjFyB,UAAU,CAAC6B,eAAe,CAAC;MAC3B3B,kBAAkB,CAAC8B,WAAW,CAAC;MAC/B5B,oBAAoB,CAAC6B,aAAa,CAAC;MAEnCC,OAAO,CAACC,GAAG,CAAC,kBAAkBH,WAAW,CAACjD,MAAM,iBAAiBkD,aAAa,CAAClD,MAAM,gBAAgB,CAAC;IACxG,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD/C,OAAO,CAAC,mCAAmC,IAAI+C,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACRzC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM0C,0BAA0B,GAAIhE,IAAI,IAAK;IAC3CgC,eAAe,CAACiC,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;MAE7D,IAAIH,UAAU,EAAE;QACd;QACA,MAAMI,QAAQ,GAAGrC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAIpC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE;QACjF,IAAID,QAAQ,EAAE;UACZ,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC,qBAAqBzE,IAAI,CAACqE,OAAO,uCAAuCpC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,oBAAoB,CAAC,EAAE;YACxI,OAAOJ,IAAI;UACb;QACF;;QAEA;QACA,MAAMS,WAAW,GAAGT,IAAI,CAACT,MAAM,CAACY,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;;QAEhE;QACA,MAAMM,YAAY,GAAG;UAAE,GAAG1C;QAAU,CAAC;QACrC,OAAO0C,YAAY,CAAC3E,IAAI,CAACqE,OAAO,CAAC;QACjCnC,YAAY,CAACyC,YAAY,CAAC;;QAE1B;QACA3B,SAAS,CAAC4B,UAAU,IAAI;UACtB,MAAMC,SAAS,GAAG;YAAE,GAAGD;UAAW,CAAC;UACnC,OAAOC,SAAS,CAAC7E,IAAI,CAACqE,OAAO,CAAC;UAC9B,OAAOQ,SAAS;QAClB,CAAC,CAAC;QACF3B,WAAW,CAAC4B,YAAY,IAAI;UAC1B,MAAMC,WAAW,GAAG;YAAE,GAAGD;UAAa,CAAC;UACvC,OAAOC,WAAW,CAAC/E,IAAI,CAACqE,OAAO,CAAC;UAChC,OAAOU,WAAW;QACpB,CAAC,CAAC;QAEF,OAAOL,WAAW;MACpB,CAAC,MAAM;QACL;QACA,OAAO,CAAC,GAAGT,IAAI,EAAEjE,IAAI,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMgF,4BAA4B,GAAIhF,IAAI,IAAK;IAC7C0C,wBAAwB,CAAC;MAAE1C,IAAI;MAAEC;IAAO,CAAC,CAAC;IAC1CuC,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMyC,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAIxC,qBAAqB,EAAE;MACzB,MAAM;QAAEzC;MAAK,CAAC,GAAGyC,qBAAqB;;MAEtC;MACAT,eAAe,CAACiC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE,GAAGjE,IAAI;QAAEkF,eAAe,EAAE;MAAK,CAAC,CAAC,CAAC;MAEtE1C,yBAAyB,CAAC,KAAK,CAAC;MAChCE,wBAAwB,CAAC,IAAI,CAAC;MAE9B5B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,sBAAsBd,IAAI,CAACqE,OAAO,2DAA2D,CAAC;IAC5G;EACF,CAAC;;EAED;EACA,MAAMc,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC3C;IACAnD,YAAY,CAAC+B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACmB,MAAM,GAAGC;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAC,aAAa,CAACF,MAAM,EAAEC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACF,MAAM,EAAEC,KAAK,KAAK;IACvC,MAAMrF,IAAI,GAAGyB,OAAO,CAAC8D,IAAI,CAACnB,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKe,MAAM,CAAC;IACpD,IAAI,CAACpF,IAAI,EAAE;;IAEX;IACAgD,SAAS,CAACiB,IAAI,IAAI;MAChB,MAAMY,SAAS,GAAG;QAAE,GAAGZ;MAAK,CAAC;MAC7B,OAAOY,SAAS,CAACO,MAAM,CAAC;MACxB,OAAOP,SAAS;IAClB,CAAC,CAAC;IAEF3B,WAAW,CAACe,IAAI,IAAI;MAClB,MAAMc,WAAW,GAAG;QAAE,GAAGd;MAAK,CAAC;MAC/B,OAAOc,WAAW,CAACK,MAAM,CAAC;MAC1B,OAAOL,WAAW;IACpB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACM,KAAK,IAAIA,KAAK,CAACd,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjC,OAAO,IAAI,CAAC,CAAC;IACf;;IAEA;IACA,IAAIiB,KAAK,CAACC,UAAU,CAACJ,KAAK,CAAC,CAAC,IAAII,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC,EAAE;MACtDrC,SAAS,CAACiB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG;MACZ,CAAC,CAAC,CAAC;MACH,OAAO,KAAK;IACd;IAEA,MAAMM,WAAW,GAAGD,UAAU,CAACJ,KAAK,CAAC;;IAErC;IACA,IAAIrF,IAAI,CAAC2F,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACzF,IAAI,CAAC2F,aAAa,CAAC,EAAE;MACtEzC,WAAW,CAACe,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG,mBAAmBM,WAAW,yCAAyC1F,IAAI,CAAC2F,aAAa;MACrG,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,MAAMC,oBAAoB,GAAGC,MAAM,CAACC,OAAO,CAAC7D,SAAS,CAAC,CACnDuB,MAAM,CAAC,CAAC,CAACuC,EAAE,EAAEC,CAAC,CAAC,KAAKD,EAAE,KAAKX,MAAM,CAAC,CAAC;IAAA,CACnCa,MAAM,CAAC,CAACC,GAAG,EAAE,CAACF,CAAC,EAAEG,KAAK,CAAC,KAAKD,GAAG,GAAGT,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGT,WAAW;IAE7E,MAAMU,kBAAkB,GAAG,CAAAnG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoG,aAAa,KAAI,CAAC;IACrD,IAAIT,oBAAoB,GAAGQ,kBAAkB,EAAE;MAC7ClD,WAAW,CAACe,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG,qBAAqBQ,oBAAoB,CAACU,OAAO,CAAC,CAAC,CAAC,uBAAuBF,kBAAkB,CAACE,OAAO,CAAC,CAAC,CAAC;MACpH,CAAC,CAAC,CAAC;IACL;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAM3B,SAAS,GAAG,CAAC,CAAC;IACpB,MAAME,WAAW,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAIhD,YAAY,CAACtB,MAAM,KAAK,CAAC,EAAE;MAC7BM,OAAO,CAAC,0BAA0B,CAAC;MACnC,OAAO,KAAK;IACd;;IAEA;IACA,KAAK,MAAMf,IAAI,IAAI+B,YAAY,EAAE;MAC/B,MAAMoE,KAAK,GAAGlE,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC;;MAErC;MACA,IAAI,CAAC8B,KAAK,IAAIA,KAAK,CAAC5B,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCM,SAAS,CAAC7E,IAAI,CAACqE,OAAO,CAAC,GAAG,uCAAuC;QACjEmC,OAAO,GAAG,KAAK;QACf;MACF;;MAEA;MACA,IAAIhB,KAAK,CAACC,UAAU,CAACU,KAAK,CAAC,CAAC,IAAIV,UAAU,CAACU,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDtB,SAAS,CAAC7E,IAAI,CAACqE,OAAO,CAAC,GAAG,sCAAsC;QAChEmC,OAAO,GAAG,KAAK;QACf;MACF;MAEA,MAAMd,WAAW,GAAGD,UAAU,CAACU,KAAK,CAAC;;MAErC;MACA,IAAInG,IAAI,CAAC2F,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACzF,IAAI,CAAC2F,aAAa,CAAC,EAAE;QACtEZ,WAAW,CAAC/E,IAAI,CAACqE,OAAO,CAAC,GAAG,mBAAmBqB,WAAW,yCAAyC1F,IAAI,CAAC2F,aAAa,IAAI;MAC3H;IACF;IAEA3C,SAAS,CAAC6B,SAAS,CAAC;IACpB3B,WAAW,CAAC6B,WAAW,CAAC;;IAExB;IACA,IAAI,CAACyB,OAAO,EAAE;MACZ,OAAO,KAAK;IACd;;IAEA;IACA,MAAMZ,oBAAoB,GAAGC,MAAM,CAACY,MAAM,CAACxE,SAAS,CAAC,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGT,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7G,MAAMC,kBAAkB,GAAG,CAAAnG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoG,aAAa,KAAI,CAAC;IACrD,IAAIT,oBAAoB,GAAGQ,kBAAkB,EAAE;MAC7C;MACAtD,iBAAiB,CAAC;QAChB8C,oBAAoB;QACpBQ,kBAAkB;QAClBrE,YAAY,EAAEA,YAAY,CAACtB;MAC7B,CAAC,CAAC;MACFmC,iBAAiB,CAAC,IAAI,CAAC;MACvB,OAAO,KAAK,CAAC,CAAC;IAChB;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM8D,UAAU,GAAG,MAAAA,CAAOC,SAAS,GAAG,KAAK,KAAK;IAC9C,IAAI;MACF;MACA,IAAI,CAACA,SAAS,IAAI,CAACJ,gBAAgB,CAAC,CAAC,EAAE;QACrC;MACF;MAEA/E,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,MAAMoF,OAAO,GAAG,EAAE;MAClB,IAAI7D,MAAM,GAAG,EAAE;MAEf,KAAK,MAAM/C,IAAI,IAAI+B,YAAY,EAAE;QAC/B,IAAI;UACF,MAAM2D,WAAW,GAAGD,UAAU,CAACxD,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,CAAC;;UAEvD;UACA,MAAMwC,kBAAkB,GAAGD,OAAO,CAACX,MAAM,CAAC,CAACC,GAAG,EAAEY,CAAC,KAAKZ,GAAG,GAAGY,CAAC,CAACpB,WAAW,EAAE,CAAC,CAAC;UAC7E,MAAMU,kBAAkB,GAAG,CAAAnG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoG,aAAa,KAAI,CAAC;UACrD,MAAMU,cAAc,GAAGJ,SAAS,IAAKE,kBAAkB,GAAGnB,WAAW,GAAIU,kBAAkB,IAAIpG,IAAI,CAACkF,eAAe;;UAEnH;UACA,MAAM8B,MAAM,GAAG,MAAM1H,WAAW,CAAC2H,iBAAiB,CAChDpG,UAAU,EACVb,IAAI,CAACqE,OAAO,EACZqB,WAAW,EACXzF,MAAM,CAACiH,SAAS,EAChBH,cACF,CAAC;UAEDH,OAAO,CAACO,IAAI,CAAC;YACXnH,IAAI,EAAEA,IAAI,CAACqE,OAAO;YAClBqB,WAAW;YACX0B,OAAO,EAAE,IAAI;YACbC,eAAe,EAAErH,IAAI,CAACkF;UACxB,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOpB,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,sCAAsC9D,IAAI,CAACqE,OAAO,GAAG,EAAEP,KAAK,CAAC;UAC3Ef,MAAM,CAACoE,IAAI,CAAC;YACVnH,IAAI,EAAEA,IAAI,CAACqE,OAAO;YAClBP,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;UAC1B,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAIhB,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE;QACvB,MAAM6G,iBAAiB,GAAGV,OAAO,CAACpD,MAAM,CAACsD,CAAC,IAAIA,CAAC,CAACO,eAAe,CAAC,CAAC5G,MAAM;QACvE,IAAIsD,OAAO,GAAG,GAAG6C,OAAO,CAACnG,MAAM,+BAA+B;QAC9D,IAAI6G,iBAAiB,GAAG,CAAC,EAAE;UACzBvD,OAAO,IAAI,KAAKuD,iBAAiB,gCAAgC;QACnE;QACAxG,SAAS,CAACiD,OAAO,CAAC;QAClBnD,OAAO,CAAC,CAAC;MACX,CAAC,MAAM,IAAIgG,OAAO,CAACnG,MAAM,GAAG,CAAC,EAAE;QAC7BK,SAAS,CAAC,GAAG8F,OAAO,CAACnG,MAAM,kCAAkCsC,MAAM,CAACtC,MAAM,UAAU,CAAC;QACrFM,OAAO,CAAC,WAAWgC,MAAM,CAACwE,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACxH,IAAI,KAAKwH,CAAC,CAAC1D,KAAK,EAAE,CAAC,CAAC2D,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACzE7G,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACLG,OAAO,CAAC,mCAAmCgC,MAAM,CAACwE,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACxH,IAAI,KAAKwH,CAAC,CAAC1D,KAAK,EAAE,CAAC,CAAC2D,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MACnG;IACF,CAAC,CAAC,OAAO3D,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD/C,OAAO,CAAC,iCAAiC,IAAI+C,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRvC,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMkG,uBAAuB,GAAGA,CAAA,KAAM;IACpC9E,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;IACvB4D,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAIC,QAAQ,IAAK;IAC/B,IAAI,CAACvF,UAAU,EAAE,OAAOuF,QAAQ;IAEhC,OAAOA,QAAQ,CAACpE,MAAM,CAACxD,IAAI,IACzBA,IAAI,CAACqE,OAAO,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CAAC,IAC5D7H,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACE,SAAS,CAAC2H,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CAAE,IAClF7H,IAAI,CAAC+H,mBAAmB,IAAI/H,IAAI,CAAC+H,mBAAmB,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CAAE,IACtG7H,IAAI,CAACgI,iBAAiB,IAAIhI,IAAI,CAACgI,iBAAiB,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CACnG,CAAC;EACH,CAAC;EAED,MAAMI,mBAAmB,GAAGN,UAAU,CAAChG,eAAe,CAAC;EACvD,MAAMuG,qBAAqB,GAAGP,UAAU,CAAC9F,iBAAiB,CAAC;;EAE3D;EACA,MAAM+D,oBAAoB,GAAGC,MAAM,CAACY,MAAM,CAACxE,SAAS,CAAC,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC3E,MAAMd,KAAK,GAAGI,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC;IACpC,OAAOX,KAAK,CAACH,KAAK,CAAC,GAAGa,GAAG,GAAGA,GAAG,GAAGb,KAAK;EACzC,CAAC,EAAE,CAAC,CAAC;EAEL,MAAMe,kBAAkB,GAAG,CAAAnG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoG,aAAa,KAAI,CAAC;EACrD,MAAM8B,WAAW,GAAGvC,oBAAoB,GAAGQ,kBAAkB;;EAE7D;EACA,MAAMgC,cAAc,GAAGA,CAACR,QAAQ,EAAE7H,YAAY,GAAG,IAAI,KAAK;IACxD,IAAIsB,WAAW,EAAE;MACf,oBACEzB,OAAA,CAACnD,GAAG;QAAC4L,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eAC5D7I,OAAA,CAACxC,gBAAgB;UAAAsL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;IAEA,IAAIjB,QAAQ,CAACnH,MAAM,KAAK,CAAC,EAAE;MACzB,oBACEb,OAAA,CAACvC,KAAK;QAACyL,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAClC1I,YAAY,GAAG,sCAAsC,GAAG;MAAoC;QAAA2I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF,CAAC;IAEZ;IAEA,oBACEjJ,OAAA,CAACjC,IAAI;MAACoL,KAAK;MAAAN,QAAA,EACRb,QAAQ,CAACL,GAAG,CAAEvH,IAAI,IAAK;QACtB,MAAMkE,UAAU,GAAGnC,YAAY,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;QACrE,MAAMC,QAAQ,GAAGrC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAIpC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE;QACjF,MAAMyE,QAAQ,GAAGjG,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC;QACrC,MAAM4E,UAAU,GAAGhG,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC;QAEzC,oBACEzE,OAAA,CAAChC,QAAQ;UAEPyK,EAAE,EAAE;YACFa,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,CAAC;YACfC,EAAE,EAAE,CAAC;YACLC,OAAO,EAAEnF,UAAU,GAAG,yBAAyB,GAAG,SAAS;YAC3D,SAAS,EAAE;cACTmF,OAAO,EAAEnF,UAAU,GAAG,yBAAyB,GAAG;YACpD;UACF,CAAE;UAAAuE,QAAA,gBAEF7I,OAAA,CAAC/B,YAAY;YACXyL,OAAO,eACL1J,OAAA,CAACnD,GAAG;cAAC4L,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEiB,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAAf,QAAA,gBACzD7I,OAAA,CAACX,SAAS;gBAACwK,QAAQ,EAAC,OAAO;gBAACC,KAAK,EAAE3J,YAAY,GAAG,SAAS,GAAG;cAAU;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3EjJ,OAAA,CAACpD,UAAU;gBAACmN,OAAO,EAAC,WAAW;gBAAAlB,QAAA,EAAEzI,IAAI,CAACqE;cAAO;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC3DjJ,OAAA,CAACrC,IAAI;gBACHqM,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAE7J,IAAI,CAACE,SAAS,IAAI,KAAM;gBAC/BwJ,KAAK,EAAE3J,YAAY,GAAG,SAAS,GAAG,SAAU;gBAC5C4J,OAAO,EAAC;cAAU;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,EACD,CAAC9I,YAAY,iBACZH,OAAA,CAACrC,IAAI;gBACHqM,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAC,eAAe;gBACrBH,KAAK,EAAC,OAAO;gBACbC,OAAO,EAAC;cAAQ;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;YACDiB,SAAS,eACPlK,OAAA,CAACnD,GAAG;cAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;gBAACmN,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,GAC/CzI,IAAI,CAAC+H,mBAAmB,IAAI,KAAK,EAAC,UAAG,EAAC/H,IAAI,CAACgI,iBAAiB,IAAI,KAAK;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACbjJ,OAAA,CAACpD,UAAU;gBAACmN,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,GAAC,cACrC,EAACzI,IAAI,CAACI,OAAO,IAAI,KAAK,EAAC,oBAAkB,EAACJ,IAAI,CAAC2F,aAAa,IAAI,KAAK;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,EACZ3E,UAAU,iBACTtE,OAAA,CAACnD,GAAG;gBAAC4L,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,eACjB7I,OAAA,CAAClD,SAAS;kBACRkN,IAAI,EAAC,OAAO;kBACZI,IAAI,EAAC,QAAQ;kBACbH,KAAK,EAAC,cAAc;kBACpBxE,KAAK,EAAEpD,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAI,EAAG;kBACrC4F,QAAQ,EAAGzC,CAAC,IAAKrC,iBAAiB,CAACnF,IAAI,CAACqE,OAAO,EAAEmD,CAAC,CAAC0C,MAAM,CAAC7E,KAAK,CAAE;kBACjEvB,KAAK,EAAE,CAAC,CAACkF,QAAS;kBAClBmB,UAAU,EAAEnB,QAAQ,IAAIC,UAAW;kBACnCmB,mBAAmB,EAAE;oBACnB/B,EAAE,EAAE;sBAAEqB,KAAK,EAAET,UAAU,IAAI,CAACD,QAAQ,GAAG,cAAc,GAAG;oBAAa;kBACvE,CAAE;kBACFX,EAAE,EAAE;oBAAEgC,KAAK,EAAE;kBAAQ;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjJ,OAAA,CAAC9B,uBAAuB;YAAA2K,QAAA,eACtB7I,OAAA,CAACrD,MAAM;cACLqN,IAAI,EAAC,OAAO;cACZD,OAAO,EAAEzF,UAAU,GAAG,WAAW,GAAG,UAAW;cAC/CwF,KAAK,EAAE3J,YAAY,GAAG,SAAS,GAAG,SAAU;cAC5CuK,OAAO,EAAEA,CAAA,KAAMvK,YAAY,GAAGiE,0BAA0B,CAAChE,IAAI,CAAC,GAAGgF,4BAA4B,CAAChF,IAAI,CAAE;cACpGuK,SAAS,EAAErG,UAAU,gBAAGtE,OAAA,CAACT,eAAe;gBAAAuJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGjJ,OAAA,CAACvB,OAAO;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAEzDvE,UAAU,GAAG,aAAa,GAAG;YAAW;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CAAC;QAAA,GAtErB7I,IAAI,CAACqE,OAAO;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuET,CAAC;MAEf,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;EAED,oBACEjJ,OAAA,CAACzD,MAAM;IAACwE,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC4J,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAhC,QAAA,gBAC3D7I,OAAA,CAACxD,WAAW;MAAAqM,QAAA,eACV7I,OAAA,CAACnD,GAAG;QAAC4L,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEiB,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAf,QAAA,gBACzD7I,OAAA,CAACX,SAAS;UAAAyJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACbjJ,OAAA,CAACpD,UAAU;UAACmN,OAAO,EAAC,IAAI;UAAAlB,QAAA,GAAC,4BACG,EAACpI,eAAe,CAACJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiH,SAAS,CAAC;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACdjJ,OAAA,CAACvD,aAAa;MAAAoM,QAAA,EACX,CAACxI,MAAM,gBACNL,OAAA,CAACvC,KAAK;QAACyL,QAAQ,EAAC,OAAO;QAAAL,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,gBAE1DjJ,OAAA,CAAAE,SAAA;QAAA2I,QAAA,gBAEE7I,OAAA,CAAC5B,IAAI;UAACqK,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,gBAClB7I,OAAA,CAAC1B,UAAU;YACTwM,KAAK,EAAC,iBAAiB;YACvBC,oBAAoB,EAAE;cAAEhB,OAAO,EAAE;YAAY;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACFjJ,OAAA,CAAC3B,WAAW;YAACoK,EAAE,EAAE;cAAEuC,EAAE,EAAE;YAAE,CAAE;YAAAnC,QAAA,eACzB7I,OAAA,CAACnD,GAAG;cAAC4L,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEuC,QAAQ,EAAE,MAAM;gBAAErB,GAAG,EAAE;cAAE,CAAE;cAAAf,QAAA,gBACrD7I,OAAA,CAACnD,GAAG;gBAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAjB,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzEjJ,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EAAExI,MAAM,CAACiH;gBAAS;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACNjJ,OAAA,CAACnD,GAAG;gBAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAjB,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzEjJ,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EAAExI,MAAM,CAACC,SAAS,IAAI;gBAAK;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNjJ,OAAA,CAACnD,GAAG;gBAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAjB,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1EjJ,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EAAExI,MAAM,CAACG,OAAO,IAAI;gBAAK;kBAAAsI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNjJ,OAAA,CAACnD,GAAG;gBAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAjB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7EjJ,OAAA,CAACpD,UAAU;kBACTmN,OAAO,EAAC,OAAO;kBACfD,KAAK,EAAEvB,WAAW,GAAG,YAAY,GAAG,cAAe;kBACnDE,EAAE,EAAE;oBAAEyC,UAAU,EAAE3C,WAAW,GAAG,MAAM,GAAG;kBAAS,CAAE;kBAAAM,QAAA,GAEnD,EAAAxH,qBAAA,GAAAhB,MAAM,CAACoG,aAAa,cAAApF,qBAAA,uBAApBA,qBAAA,CAAsBqF,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,IAC3C;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjJ,OAAA,CAACnD,GAAG;gBAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAjB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrEjJ,OAAA,CAACrC,IAAI;kBACHsM,KAAK,EAAE5J,MAAM,CAAC8K,YAAY,IAAI,KAAM;kBACpCnB,IAAI,EAAC,OAAO;kBACZF,KAAK,EACHzJ,MAAM,CAAC8K,YAAY,KAAK,aAAa,GAAG,SAAS,GACjD9K,MAAM,CAAC8K,YAAY,KAAK,QAAQ,GAAG,SAAS,GAC5C9K,MAAM,CAAC8K,YAAY,KAAK,MAAM,GAAG,OAAO,GACxC9K,MAAM,CAAC8K,YAAY,KAAK,WAAW,GAAG,SAAS,GAAG;gBACnD;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGPjJ,OAAA,CAAClD,SAAS;UACR+N,SAAS;UACTZ,KAAK,EAAC,2BAA2B;UACjCF,OAAO,EAAC,UAAU;UAClBtE,KAAK,EAAEhD,UAAW;UAClB4H,QAAQ,EAAGzC,CAAC,IAAKlF,aAAa,CAACkF,CAAC,CAAC0C,MAAM,CAAC7E,KAAK,CAAE;UAC/C2F,WAAW,EAAC,wCAAwC;UACpDC,UAAU,EAAE;YACVC,cAAc,eAAEtL,OAAA,CAACb,UAAU;cAACsJ,EAAE,EAAE;gBAAE8C,EAAE,EAAE,CAAC;gBAAEzB,KAAK,EAAE;cAAiB;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACvE,CAAE;UACFR,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAGFjJ,OAAA,CAACnD,GAAG;UAAC4L,EAAE,EAAE;YAAE+C,YAAY,EAAE,CAAC;YAAEC,WAAW,EAAE,SAAS;YAAEjC,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,eAC1D7I,OAAA,CAACnC,IAAI;YAAC4H,KAAK,EAAElD,SAAU;YAAC8H,QAAQ,EAAEA,CAACzC,CAAC,EAAE8D,QAAQ,KAAKlJ,YAAY,CAACkJ,QAAQ,CAAE;YAAA7C,QAAA,gBACxE7I,OAAA,CAAClC,GAAG;cACFmM,KAAK,eACHjK,OAAA,CAACzB,KAAK;gBAACoN,YAAY,EAAEtD,mBAAmB,CAACxH,MAAO;gBAACiJ,KAAK,EAAC,SAAS;gBAAAjB,QAAA,eAC9D7I,OAAA,CAACnD,GAAG;kBAAC4L,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEiB,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAAf,QAAA,gBACzD7I,OAAA,CAACT,eAAe;oBAACsK,QAAQ,EAAC;kBAAO;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACR;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFjJ,OAAA,CAAClC,GAAG;cACFmM,KAAK,eACHjK,OAAA,CAACzB,KAAK;gBAACoN,YAAY,EAAErD,qBAAqB,CAACzH,MAAO;gBAACiJ,KAAK,EAAC,SAAS;gBAAAjB,QAAA,eAChE7I,OAAA,CAACnD,GAAG;kBAAC4L,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEiB,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAAf,QAAA,gBACzD7I,OAAA,CAACf,WAAW;oBAAC4K,QAAQ,EAAC;kBAAO;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAElC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACR;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNjJ,OAAA,CAACnD,GAAG;UAAC4L,EAAE,EAAE;YAAEmD,SAAS,EAAE,GAAG;YAAEC,SAAS,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAjD,QAAA,GAC3DtG,SAAS,KAAK,CAAC,iBACdvC,OAAA,CAACnD,GAAG;YAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;cAACmN,OAAO,EAAC,OAAO;cAACD,KAAK,EAAC,gBAAgB;cAACrB,EAAE,EAAE;gBAAEe,EAAE,EAAE;cAAE,CAAE;cAAAX,QAAA,GAAC,iCACjC,eAAA7I,OAAA;gBAAA6I,QAAA,EAASxI,MAAM,CAACC;cAAS;gBAAAwI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,kBAAc,eAAAjJ,OAAA;gBAAA6I,QAAA,EAASxI,MAAM,CAACG;cAAO;gBAAAsI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC,EACZT,cAAc,CAACH,mBAAmB,EAAE,IAAI,CAAC;UAAA;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN,EACA1G,SAAS,KAAK,CAAC,iBACdvC,OAAA,CAACnD,GAAG;YAAAgM,QAAA,gBACF7I,OAAA,CAACvC,KAAK;cAACyL,QAAQ,EAAC,SAAS;cAACT,EAAE,EAAE;gBAAEe,EAAE,EAAE;cAAE,CAAE;cAAAX,QAAA,eACtC7I,OAAA,CAACpD,UAAU;gBAACmN,OAAO,EAAC,OAAO;gBAAAlB,QAAA,GAAC,qDACyB,eAAA7I,OAAA;kBAAA6I,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,uEAEhF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EACPT,cAAc,CAACF,qBAAqB,EAAE,KAAK,CAAC;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL9G,YAAY,CAACtB,MAAM,GAAG,CAAC,iBACtBb,OAAA,CAAC5B,IAAI;UAACqK,EAAE,EAAE;YAAE0B,EAAE,EAAE,CAAC;YAAEX,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,gBACzB7I,OAAA,CAAC1B,UAAU;YACTwM,KAAK,EAAE,wBAAwB3I,YAAY,CAACtB,MAAM,QAAS;YAC3DkK,oBAAoB,EAAE;cAAEhB,OAAO,EAAE;YAAY;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACFjJ,OAAA,CAAC3B,WAAW;YAACoK,EAAE,EAAE;cAAEuC,EAAE,EAAE;YAAE,CAAE;YAAAnC,QAAA,gBACzB7I,OAAA,CAACnD,GAAG;cAAC4L,EAAE,EAAE;gBAAEe,EAAE,EAAE;cAAE,CAAE;cAAAX,QAAA,gBACjB7I,OAAA,CAACnD,GAAG;gBAAC4L,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEa,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,gBACnE7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChEjJ,OAAA,CAACpD,UAAU;kBACTmN,OAAO,EAAC,OAAO;kBACftB,EAAE,EAAE;oBACFyC,UAAU,EAAE,MAAM;oBAClBpB,KAAK,EAAEvB,WAAW,GAAG,YAAY,GAAG;kBACtC,CAAE;kBAAAM,QAAA,GAED7C,oBAAoB,CAACU,OAAO,CAAC,CAAC,CAAC,EAAC,IACnC;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjJ,OAAA,CAACnD,GAAG;gBAAC4L,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEa,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,gBACnE7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9DjJ,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACtB,EAAE,EAAE;oBAAEyC,UAAU,EAAE;kBAAO,CAAE;kBAAArC,QAAA,GACpD,EAAAvH,sBAAA,GAAAjB,MAAM,CAACoG,aAAa,cAAAnF,sBAAA,uBAApBA,sBAAA,CAAsBoF,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,IAC3C;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjJ,OAAA,CAAC7B,OAAO;gBAACsK,EAAE,EAAE;kBAAEG,EAAE,EAAE;gBAAE;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1BjJ,OAAA,CAACnD,GAAG;gBAAC4L,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE;gBAAgB,CAAE;gBAAAE,QAAA,gBAC5D7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpDjJ,OAAA,CAACpD,UAAU;kBACTmN,OAAO,EAAC,OAAO;kBACftB,EAAE,EAAE;oBACFyC,UAAU,EAAE,MAAM;oBAClBpB,KAAK,EAAEvB,WAAW,GAAG,YAAY,GAAG;kBACtC,CAAE;kBAAAM,QAAA,GAED,CAACxI,MAAM,CAACoG,aAAa,GAAGT,oBAAoB,EAAEU,OAAO,CAAC,CAAC,CAAC,EAAC,IAC1D,EAAC6B,WAAW,IAAI,SAAS;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELV,WAAW,iBACVvI,OAAA,CAACvC,KAAK;cAACyL,QAAQ,EAAC,SAAS;cAACT,EAAE,EAAE;gBAAEe,EAAE,EAAE;cAAE,CAAE;cAAAX,QAAA,eACtC7I,OAAA,CAACpD,UAAU;gBAACmN,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBACzB7I,OAAA;kBAAA6I,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,+FAE9B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR,eAEDjJ,OAAA,CAACjC,IAAI;cAACoL,KAAK;cAAAN,QAAA,EACR1G,YAAY,CAACwF,GAAG,CAAEvH,IAAI,iBACrBJ,OAAA,CAAChC,QAAQ;gBAEPyK,EAAE,EAAE;kBACFa,MAAM,EAAE,mBAAmB;kBAC3BC,YAAY,EAAE,CAAC;kBACfC,EAAE,EAAE,CAAC;kBACLC,OAAO,EAAErJ,IAAI,CAACkF,eAAe,GAAG,eAAe,GAAG;gBACpD,CAAE;gBAAAuD,QAAA,gBAEF7I,OAAA,CAAC/B,YAAY;kBACXyL,OAAO,eACL1J,OAAA,CAACnD,GAAG;oBAAC4L,EAAE,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEiB,UAAU,EAAE,QAAQ;sBAAEC,GAAG,EAAE;oBAAE,CAAE;oBAAAf,QAAA,gBACzD7I,OAAA,CAACpD,UAAU;sBAACmN,OAAO,EAAC,WAAW;sBAAAlB,QAAA,EAAEzI,IAAI,CAACqE;oBAAO;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,EAC1D7I,IAAI,CAACkF,eAAe,iBACnBtF,OAAA,CAACrC,IAAI;sBAACqM,IAAI,EAAC,OAAO;sBAACC,KAAK,EAAC,eAAe;sBAACH,KAAK,EAAC;oBAAS;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC3D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN;kBACDiB,SAAS,EAAE,GAAG7H,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAI,GAAG;gBAAgB;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACFjJ,OAAA,CAAC9B,uBAAuB;kBAAA2K,QAAA,eACtB7I,OAAA,CAACtC,UAAU;oBACTsM,IAAI,EAAC,OAAO;oBACZF,KAAK,EAAC,OAAO;oBACbY,OAAO,EAAEA,CAAA,KAAMtG,0BAA0B,CAAChE,IAAI,CAAE;oBAChD0K,KAAK,EAAC,yBAAyB;oBAAAjC,QAAA,eAE/B7I,OAAA,CAACrB,UAAU;sBAACkL,QAAQ,EAAC;oBAAO;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACU,CAAC;cAAA,GA5BrB7I,IAAI,CAACqE,OAAO;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACP,EAGAhD,MAAM,CAAC8F,IAAI,CAAC1I,QAAQ,CAAC,CAACxC,MAAM,GAAG,CAAC,iBAC/Bb,OAAA,CAACvC,KAAK;UAACyL,QAAQ,EAAC,SAAS;UAACT,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAE,CAAE;UAAAtB,QAAA,gBACtC7I,OAAA,CAACpD,UAAU;YAACmN,OAAO,EAAC,WAAW;YAACiC,YAAY;YAAAnD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjEjJ,OAAA,CAACjC,IAAI;YAACoL,KAAK;YAAAN,QAAA,EACR5C,MAAM,CAACC,OAAO,CAAC7C,QAAQ,CAAC,CAACsE,GAAG,CAAC,CAAC,CAACnC,MAAM,EAAEyG,OAAO,CAAC,kBAC9CjM,OAAA,CAAChC,QAAQ;cAAcyK,EAAE,EAAE;gBAAEyD,EAAE,EAAE;cAAE,CAAE;cAAArD,QAAA,eACnC7I,OAAA,CAAC/B,YAAY;gBACXyL,OAAO,EAAE,GAAGlE,MAAM,KAAKyG,OAAO,EAAG;gBACjCE,sBAAsB,EAAE;kBAAEpC,OAAO,EAAE;gBAAQ;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC,GAJWzD,MAAM;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKX,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACR,eAGDjJ,OAAA,CAACvC,KAAK;UAACyL,QAAQ,EAAC,MAAM;UAACT,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAE,CAAE;UAAAtB,QAAA,eACnC7I,OAAA,CAACpD,UAAU;YAACmN,OAAO,EAAC,OAAO;YAAAlB,QAAA,gBACzB7I,OAAA;cAAA6I,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,4JAE9B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA,eACR;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAEhBjJ,OAAA,CAACtD,aAAa;MAAC+L,EAAE,EAAE;QAAE2D,EAAE,EAAE,CAAC;QAAEF,EAAE,EAAE;MAAE,CAAE;MAAArD,QAAA,gBAClC7I,OAAA,CAACrD,MAAM;QAAC+N,OAAO,EAAE1J,OAAQ;QAACqL,QAAQ,EAAE1K,MAAO;QAAAkH,QAAA,EAAC;MAE5C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACrD,MAAM;QACL+N,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAAC,KAAK,CAAE;QACjCgD,KAAK,EAAC,SAAS;QACfC,OAAO,EAAC,WAAW;QACnBsC,QAAQ,EAAE1K,MAAM,IAAIQ,YAAY,CAACtB,MAAM,KAAK,CAAC,IAAIoF,MAAM,CAAC8F,IAAI,CAAC5I,MAAM,CAAC,CAACtC,MAAM,GAAG,CAAE;QAChF8J,SAAS,EAAEhJ,MAAM,gBAAG3B,OAAA,CAACxC,gBAAgB;UAACwM,IAAI,EAAE;QAAG;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGjJ,OAAA,CAACnB,QAAQ;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAEjElH,MAAM,GAAG,gBAAgB,GAAG,SAASQ,YAAY,CAACtB,MAAM;MAAO;QAAAiI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGhBjJ,OAAA,CAACF,sBAAsB;MACrBiB,IAAI,EAAE4B,sBAAuB;MAC7B3B,OAAO,EAAEA,CAAA,KAAM;QACb4B,yBAAyB,CAAC,KAAK,CAAC;QAChCE,wBAAwB,CAAC,IAAI,CAAC;MAChC,CAAE;MACF1C,IAAI,EAAEyC,qBAAqB,aAArBA,qBAAqB,uBAArBA,qBAAqB,CAAEzC,IAAK;MAClCC,MAAM,EAAEwC,qBAAqB,aAArBA,qBAAqB,uBAArBA,qBAAqB,CAAExC,MAAO;MACtCiM,SAAS,EAAEjH;IAA0B;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGFjJ,OAAA,CAACzD,MAAM;MACLwE,IAAI,EAAEgC,cAAe;MACrB/B,OAAO,EAAEA,CAAA,KAAMgC,iBAAiB,CAAC,KAAK,CAAE;MACxC4H,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAhC,QAAA,gBAET7I,OAAA,CAACxD,WAAW;QAAAqM,QAAA,eACV7I,OAAA,CAACnD,GAAG;UAAC4L,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEiB,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAf,QAAA,gBACzD7I,OAAA,CAACf,WAAW;YAAC6K,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BjJ,OAAA,CAACpD,UAAU;YAACmN,OAAO,EAAC,IAAI;YAAAlB,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdjJ,OAAA,CAACvD,aAAa;QAAAoM,QAAA,EACX5F,cAAc,iBACbjD,OAAA,CAAAE,SAAA;UAAA2I,QAAA,gBACE7I,OAAA,CAACvC,KAAK;YAACyL,QAAQ,EAAC,SAAS;YAACT,EAAE,EAAE;cAAEe,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,eACtC7I,OAAA,CAACpD,UAAU;cAACmN,OAAO,EAAC,OAAO;cAACiC,YAAY;cAAAnD,QAAA,gBACtC7I,OAAA;gBAAA6I,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,qDAC9B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACRjJ,OAAA,CAACnD,GAAG;YAAC4L,EAAE,EAAE;cAAEe,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,gBACjB7I,OAAA,CAACpD,UAAU;cAACmN,OAAO,EAAC,OAAO;cAACiC,YAAY;cAAAnD,QAAA,gBACtC7I,OAAA;gBAAA6I,QAAA,EAAQ;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChG,cAAc,CAAC+C,oBAAoB,CAACU,OAAO,CAAC,CAAC,CAAC,EAAC,IAC3F;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjJ,OAAA,CAACpD,UAAU;cAACmN,OAAO,EAAC,OAAO;cAACiC,YAAY;cAAAnD,QAAA,gBACtC7I,OAAA;gBAAA6I,QAAA,EAAQ;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChG,cAAc,CAACuD,kBAAkB,CAACE,OAAO,CAAC,CAAC,CAAC,EAAC,IACvF;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjJ,OAAA,CAACpD,UAAU;cAACmN,OAAO,EAAC,OAAO;cAACiC,YAAY;cAAAnD,QAAA,gBACtC7I,OAAA;gBAAA6I,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,CAAChG,cAAc,CAAC+C,oBAAoB,GAAG/C,cAAc,CAACuD,kBAAkB,EAAEE,OAAO,CAAC,CAAC,CAAC,EAAC,IACpH;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjJ,OAAA,CAACpD,UAAU;cAACmN,OAAO,EAAC,OAAO;cAAAlB,QAAA,gBACzB7I,OAAA;gBAAA6I,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChG,cAAc,CAACd,YAAY;YAAA;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjJ,OAAA,CAACpD,UAAU;YAACmN,OAAO,EAAC,OAAO;YAAAlB,QAAA,EAAC;UAE5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA,eACb;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBjJ,OAAA,CAACtD,aAAa;QAAAmM,QAAA,gBACZ7I,OAAA,CAACrD,MAAM;UAAC+N,OAAO,EAAEA,CAAA,KAAM1H,iBAAiB,CAAC,KAAK,CAAE;UAAA6F,QAAA,EAAC;QAEjD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACrD,MAAM;UACL+N,OAAO,EAAE5C,uBAAwB;UACjCgC,KAAK,EAAC,SAAS;UACfC,OAAO,EAAC,WAAW;UACnBY,SAAS,eAAE3K,OAAA,CAACf,WAAW;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAAC7H,EAAA,CAzyBIN,oBAAoB;AAAAyL,EAAA,GAApBzL,oBAAoB;AA2yB1B,eAAeA,oBAAoB;AAAC,IAAAyL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}