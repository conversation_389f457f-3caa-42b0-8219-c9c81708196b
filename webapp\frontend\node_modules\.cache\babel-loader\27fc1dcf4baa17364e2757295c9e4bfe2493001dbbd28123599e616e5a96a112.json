{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriDialogCompleto.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Typography, Box, Grid, Paper, Alert, CircularProgress, FormControl, InputLabel, Select, MenuItem, Chip, InputAdornment, IconButton, List, ListItem, ListItemButton } from '@mui/material';\nimport { Search as SearchIcon, Cancel as CancelIcon, AddCircleOutline as AddCircleOutlineIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Dialog completo per inserire i metri posati di un cavo preselezionato\n * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Cavo preselezionato\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriDialogCompleto = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  cantiereId,\n  onSuccess = () => {},\n  onError = () => {},\n  loading = false\n}) => {\n  _s();\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n\n  // Stati per bobine\n  const [bobine, setBobine] = useState([]);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per la ricerca delle bobine\n  const [searchText, setSearchText] = useState('');\n  const [searchType, setSearchType] = useState('contains');\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = idBobina => {\n    console.log('Bobina selezionata:', idBobina);\n    setFormData(prev => ({\n      ...prev,\n      id_bobina: idBobina\n    }));\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: ''\n    }));\n\n    // Forza il re-render per mostrare le informazioni della bobina selezionata\n    const selectedBobina = bobine.find(b => b.id_bobina === idBobina);\n    if (selectedBobina) {\n      console.log('Dettagli bobina selezionata:', selectedBobina);\n    }\n  };\n\n  // Gestisce il cambio del testo di ricerca\n  const handleSearchTextChange = event => {\n    setSearchText(event.target.value);\n  };\n\n  // Carica le bobine disponibili\n  const loadBobine = useCallback(async () => {\n    if (!cantiereId || !cavo) return;\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina => bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over' && bobina.metri_residui > 0);\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (cavo && cavo.tipologia && cavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: cavo.id_cavo,\n          tipologia: cavo.tipologia,\n          sezione: cavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(cavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(cavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${cavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(cavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      setBobine([]);\n    } finally {\n      setBobineLoading(false);\n    }\n  }, [cantiereId, cavo]);\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      var _cavo$metratura_reale;\n      setFormData({\n        metri_posati: ((_cavo$metratura_reale = cavo.metratura_reale) === null || _cavo$metratura_reale === void 0 ? void 0 : _cavo$metratura_reale.toString()) || '',\n        id_bobina: ''\n      });\n      setFormErrors({});\n      setFormWarnings({});\n      setSaving(false);\n\n      // Carica le bobine disponibili\n      loadBobine();\n    }\n  }, [open, cavo, cantiereId, loadBobine]);\n\n  // Gestisce i cambiamenti del form\n  const handleFormChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Rimuovi errori quando l'utente inizia a digitare\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Validazione in tempo reale per metri posati\n    if (name === 'metri_posati' && value && cavo) {\n      const metri = parseFloat(value);\n      if (!isNaN(metri) && metri > cavo.metri_teorici) {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n        }));\n      } else {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: ''\n        }));\n      }\n    }\n  };\n\n  // Validazione del form\n  const validateForm = () => {\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'I metri posati sono obbligatori';\n    } else {\n      const metri = parseFloat(formData.metri_posati);\n      if (isNaN(metri) || metri < 0) {\n        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';\n      }\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina || formData.id_bobina === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina o utilizzare BOBINA VUOTA';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch\n    const metriPosati = parseFloat(formData.metri_posati);\n    let idBobina = formData.id_bobina;\n    try {\n      setSaving(true);\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', cavo.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina);\n\n      // Chiamata API con la funzione originale completa\n      await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Successo\n      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;\n      onSuccess(successMessage);\n\n      // Chiudi il dialog\n      handleClose();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n        handleClose();\n        return;\n      }\n\n      // Gestione errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleClose = () => {\n    if (!saving && !loading) {\n      setFormErrors({});\n      setFormWarnings({});\n      setFormData({\n        metri_posati: '',\n        id_bobina: ''\n      });\n      onClose();\n    }\n  };\n  const handleKeyPress = event => {\n    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {\n      handleSave();\n    }\n  };\n  if (!cavo) return null;\n\n  // Filtra le bobine in base al testo di ricerca (logica identica all'originale)\n  const bobineFiltrate = bobine.filter(bobina => {\n    // Se non c'è testo di ricerca, mostra tutte le bobine\n    if (!searchText) return true;\n\n    // Per la ricerca esatta, prima verifica se l'intero testo corrisponde esattamente\n    if (searchType === 'equals' && !searchText.includes(',')) {\n      const searchLower = searchText.trim().toLowerCase();\n      const bobinaId = getBobinaNumber(bobina.id_bobina).toLowerCase();\n      const bobinaTipologia = String(bobina.tipologia || '').toLowerCase();\n      const bobinaSezione = String(bobina.sezione || '').toLowerCase();\n\n      // Verifica corrispondenza esatta con l'intero testo di ricerca\n      return bobinaId === searchLower || bobinaTipologia === searchLower || bobinaSezione === searchLower;\n    }\n\n    // Gestione ricerca con virgole (termini multipli)\n    const searchTerms = searchText.split(',').map(term => term.trim().toLowerCase()).filter(term => term.length > 0);\n    if (searchTerms.length > 1) {\n      // Ricerca con termini multipli\n      return searchTerms.some(term => {\n        const termStr = String(term);\n        const bobinaId = getBobinaNumber(bobina.id_bobina).toLowerCase();\n        const bobinaTipologia = String(bobina.tipologia || '').toLowerCase();\n        const bobinaSezione = String(bobina.sezione || '').toLowerCase();\n\n        // Verifica se il termine è un numero\n        const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));\n        if (isNumericTerm) {\n          const numericTerm = parseFloat(termStr);\n          const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));\n\n          // Verifica corrispondenza esatta per numeri e ID\n          return bobinaSezioneNum === numericTerm || bobinaId === termStr || bobinaTipologia === term || bobinaSezione === term;\n        } else {\n          // Ricerca esatta per stringhe\n          return bobinaId === term || bobinaTipologia === term || bobinaSezione === term;\n        }\n      });\n    }\n\n    // Ricerca con singolo termine\n    const term = searchTerms[0] || searchText.trim().toLowerCase();\n    const termStr = String(term);\n    const bobinaId = getBobinaNumber(bobina.id_bobina).toLowerCase();\n    const bobinaTipologia = String(bobina.tipologia || '').toLowerCase();\n    const bobinaSezione = String(bobina.sezione || '').toLowerCase();\n\n    // Verifica se il termine è un numero\n    const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));\n    if (searchType === 'equals') {\n      // Ricerca esatta\n      if (isNumericTerm) {\n        const numericTerm = parseFloat(termStr);\n        const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));\n\n        // Verifica corrispondenza esatta per numeri e ID\n        return bobinaSezioneNum === numericTerm || bobinaId === termStr || bobinaTipologia === term || bobinaSezione === term;\n      } else {\n        // Ricerca esatta per stringhe\n        return bobinaId === term || bobinaTipologia === term || bobinaSezione === term;\n      }\n    } else {\n      // Ricerca con 'contains' (comportamento predefinito)\n      if (isNumericTerm) {\n        const numericTerm = parseFloat(termStr);\n        const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));\n\n        // Verifica corrispondenza esatta per numeri\n        if (bobinaSezioneNum === numericTerm) {\n          return true;\n        }\n\n        // Verifica se il numero è contenuto nell'ID\n        if (bobinaId.includes(termStr)) {\n          return true;\n        }\n      }\n\n      // Ricerca standard con includes\n      return bobinaId.includes(term) || bobinaTipologia.includes(term) || bobinaSezione.includes(term);\n    }\n  });\n\n  // Separa le bobine compatibili e non compatibili\n  const bobineCompatibili = cavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() === String(cavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(cavo.sezione || '0').trim()) : bobineFiltrate;\n  const bobineNonCompatibili = cavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() !== String(cavo.tipologia || '').trim() || String(bobina.sezione || '0').trim() !== String(cavo.sezione || '0').trim()) : [];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: handleClose,\n      maxWidth: \"xl\",\n      fullWidth: true,\n      disableEscapeKeyDown: saving || loading,\n      PaperProps: {\n        sx: {\n          minHeight: '700px',\n          maxHeight: '90vh'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"div\",\n          children: [\"Inserisci Metri Posati - \", cavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            mb: 4,\n            bgcolor: '#f8f9fa',\n            borderRadius: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 60\n                }, this), \" \", cavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 60\n                }, this), \" \", cavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 60\n                }, this), \" \", cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Da:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 60\n                }, this), \" \", cavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"A:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 60\n                }, this), \" \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Attualmente posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 60\n                }, this), \" \", cavo.metratura_reale || 0, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            autoFocus: true,\n            label: \"Metri Posati\",\n            type: \"number\",\n            fullWidth: true,\n            name: \"metri_posati\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            onKeyPress: handleKeyPress,\n            error: Boolean(formErrors.metri_posati),\n            helperText: formErrors.metri_posati || formWarnings.metri_posati || `Metri teorici: ${cavo.metri_teorici}m`,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            disabled: saving || loading,\n            InputProps: {\n              endAdornment: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 31\n              }, this)\n            },\n            inputProps: {\n              max: 999999,\n              step: 0.1\n            },\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 2\n            },\n            children: \"Selezione Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 4,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  size: \"small\",\n                  label: \"Cerca\",\n                  variant: \"outlined\",\n                  value: searchText,\n                  onChange: handleSearchTextChange,\n                  placeholder: \"ID, tipologia, formazione...\",\n                  fullWidth: true,\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                      position: \"start\",\n                      children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 564,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 25\n                    }, this),\n                    endAdornment: searchText ? /*#__PURE__*/_jsxDEV(InputAdornment, {\n                      position: \"end\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        \"aria-label\": \"clear search\",\n                        onClick: () => setSearchText(''),\n                        edge: \"end\",\n                        children: /*#__PURE__*/_jsxDEV(CancelIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 575,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 569,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 25\n                    }, this) : null\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  size: \"small\",\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    id: \"search-type-label\",\n                    children: \"Tipo ricerca\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    labelId: \"search-type-label\",\n                    value: searchType,\n                    label: \"Tipo ricerca\",\n                    onChange: e => setSearchType(e.target.value),\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"contains\",\n                      children: \"Contiene\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"equals\",\n                      children: \"Uguale a\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 12,\n                md: 5,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"medium\",\n                  onClick: () => handleSelectBobina('BOBINA_VUOTA'),\n                  fullWidth: true,\n                  sx: {\n                    height: '40px',\n                    fontWeight: formData.id_bobina === 'BOBINA_VUOTA' ? 'bold' : 'normal',\n                    bgcolor: formData.id_bobina === 'BOBINA_VUOTA' ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                    border: formData.id_bobina === 'BOBINA_VUOTA' ? '1px solid #4caf50' : undefined\n                  },\n                  children: \"BOBINA VUOTA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'center',\n              my: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Paper, {\n                  variant: \"outlined\",\n                  sx: {\n                    p: 2,\n                    height: '100%'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    sx: {\n                      fontWeight: 'bold',\n                      mb: 1\n                    },\n                    children: \"ELENCO BOBINE COMPATIBILI\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 23\n                  }, this), bobineCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        width: '100%',\n                        py: 1,\n                        px: 2,\n                        bgcolor: '#f5f5f5',\n                        borderRadius: 1,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '15%',\n                          mr: 1\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem'\n                          },\n                          children: \"ID\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 638,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 637,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '25%',\n                          mr: 1\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem'\n                          },\n                          children: \"Tipo\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 641,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '15%',\n                          mr: 1\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem'\n                          },\n                          children: \"Form.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 644,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 643,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '20%',\n                          mr: 1\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem'\n                          },\n                          children: \"Residui\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 647,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '20%'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem'\n                          },\n                          children: \"Stato\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 650,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 649,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '5%'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 652,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 636,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(List, {\n                      sx: {\n                        maxHeight: bobineCompatibili.length > 6 ? '300px' : 'auto',\n                        overflowY: bobineCompatibili.length > 6 ? 'auto' : 'visible',\n                        overflowX: 'hidden',\n                        bgcolor: 'background.paper'\n                      },\n                      children: bobineCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                        disablePadding: true,\n                        secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                          edge: \"end\",\n                          size: \"small\",\n                          onClick: () => handleSelectBobina(bobina.id_bobina),\n                          children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                            color: \"primary\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 667,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 662,\n                          columnNumber: 35\n                        }, this),\n                        sx: {\n                          bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                          borderRadius: '4px',\n                          mb: 0.5,\n                          border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                          dense: true,\n                          onClick: () => handleSelectBobina(bobina.id_bobina),\n                          children: /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              display: 'flex',\n                              alignItems: 'center',\n                              width: '100%',\n                              py: 0.8\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                width: '60px',\n                                mr: 2\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Typography, {\n                                variant: \"body2\",\n                                sx: {\n                                  fontWeight: 'bold',\n                                  fontSize: '0.9rem'\n                                },\n                                children: getBobinaNumber(bobina.id_bobina)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 683,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 682,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                width: '120px',\n                                mr: 2\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Typography, {\n                                variant: \"body2\",\n                                sx: {\n                                  fontSize: '0.85rem'\n                                },\n                                children: bobina.tipologia || 'N/A'\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 688,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 687,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                width: '100px',\n                                mr: 2\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Typography, {\n                                variant: \"body2\",\n                                sx: {\n                                  fontSize: '0.85rem'\n                                },\n                                children: bobina.sezione || 'N/A'\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 693,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 692,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                width: '100px',\n                                mr: 2\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Typography, {\n                                variant: \"body2\",\n                                sx: {\n                                  fontWeight: 'bold',\n                                  fontSize: '0.85rem',\n                                  color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                                },\n                                children: [bobina.metri_residui || 0, \" m\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 698,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 697,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                flexGrow: 0\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Chip, {\n                                size: \"small\",\n                                label: bobina.stato_bobina || 'N/D',\n                                color: getReelStateColor(bobina.stato_bobina),\n                                variant: \"outlined\",\n                                sx: {\n                                  height: 22,\n                                  fontSize: '0.8rem',\n                                  '& .MuiChip-label': {\n                                    px: 1,\n                                    py: 0\n                                  }\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 703,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 702,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 681,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 677,\n                          columnNumber: 33\n                        }, this)\n                      }, bobina.id_bobina, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 658,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 656,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n                    severity: \"info\",\n                    sx: {\n                      mt: 1\n                    },\n                    children: \"Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Paper, {\n                  variant: \"outlined\",\n                  sx: {\n                    p: 2,\n                    height: '100%'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    sx: {\n                      fontWeight: 'bold',\n                      mb: 1\n                    },\n                    children: \"ELENCO BOBINE NON COMPATIBILI\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 23\n                  }, this), bobineNonCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        width: '100%',\n                        py: 0.8,\n                        px: 1.8,\n                        bgcolor: '#f5f5f5',\n                        borderRadius: 1,\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '60px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem'\n                          },\n                          children: \"ID\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 736,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 735,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '120px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem'\n                          },\n                          children: \"Tipo\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 739,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 738,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem'\n                          },\n                          children: \"Form.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 742,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 741,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem'\n                          },\n                          children: \"Residui\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 745,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 744,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          flexGrow: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem'\n                          },\n                          children: \"Stato\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 748,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 747,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 734,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(List, {\n                      sx: {\n                        maxHeight: bobineNonCompatibili.length > 6 ? '300px' : 'auto',\n                        overflowY: bobineNonCompatibili.length > 6 ? 'auto' : 'visible',\n                        overflowX: 'hidden',\n                        bgcolor: 'background.paper'\n                      },\n                      children: bobineNonCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                        disablePadding: true,\n                        secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                          edge: \"end\",\n                          size: \"small\",\n                          onClick: () => handleSelectBobina(bobina.id_bobina),\n                          children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                            color: \"primary\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 762,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 757,\n                          columnNumber: 35\n                        }, this),\n                        sx: {\n                          bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                          borderRadius: '4px',\n                          mb: 0.5,\n                          border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                          dense: true,\n                          onClick: () => handleSelectBobina(bobina.id_bobina),\n                          children: /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              display: 'flex',\n                              alignItems: 'center',\n                              width: '100%',\n                              py: 0.8\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                width: '60px',\n                                mr: 2\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Typography, {\n                                variant: \"body2\",\n                                sx: {\n                                  fontWeight: 'bold',\n                                  fontSize: '0.9rem'\n                                },\n                                children: getBobinaNumber(bobina.id_bobina)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 778,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 777,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                width: '120px',\n                                mr: 2\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Typography, {\n                                variant: \"body2\",\n                                sx: {\n                                  fontSize: '0.85rem'\n                                },\n                                children: bobina.tipologia || 'N/A'\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 783,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 782,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                width: '100px',\n                                mr: 2\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Typography, {\n                                variant: \"body2\",\n                                sx: {\n                                  fontSize: '0.85rem'\n                                },\n                                children: bobina.sezione || 'N/A'\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 788,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 787,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                width: '100px',\n                                mr: 2\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Typography, {\n                                variant: \"body2\",\n                                sx: {\n                                  fontWeight: 'bold',\n                                  fontSize: '0.85rem',\n                                  color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                                },\n                                children: [bobina.metri_residui || 0, \" m\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 793,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 792,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                display: 'flex',\n                                gap: 1\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                                size: \"small\",\n                                label: bobina.stato_bobina || 'N/D',\n                                color: getReelStateColor(bobina.stato_bobina),\n                                variant: \"outlined\",\n                                sx: {\n                                  height: 22,\n                                  fontSize: '0.8rem',\n                                  '& .MuiChip-label': {\n                                    px: 1,\n                                    py: 0\n                                  }\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 798,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                                size: \"small\",\n                                label: \"Non comp.\",\n                                color: \"warning\",\n                                variant: \"outlined\",\n                                sx: {\n                                  height: 22,\n                                  fontSize: '0.8rem',\n                                  '& .MuiChip-label': {\n                                    px: 1,\n                                    py: 0\n                                  }\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 805,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 797,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 776,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 772,\n                          columnNumber: 33\n                        }, this)\n                      }, bobina.id_bobina, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 753,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 751,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n                    severity: \"info\",\n                    sx: {\n                      mt: 1\n                    },\n                    children: \"Nessuna bobina non compatibile disponibile con i filtri attuali.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 15\n          }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mt: 2\n            },\n            children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 831,\n            columnNumber: 15\n          }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"error\",\n            sx: {\n              mt: 1,\n              display: 'block'\n            },\n            children: formErrors.id_bobina\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 837,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          disabled: saving || loading,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"contained\",\n          disabled: saving || loading || !formData.metri_posati || !formData.id_bobina,\n          startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 33\n          }, this) : null,\n          children: saving ? 'Salvando...' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(InserisciMetriDialogCompleto, \"Mzb0Tt5AmTDwUZbcBcVDPJR74sk=\");\n_c = InserisciMetriDialogCompleto;\nexport default InserisciMetriDialogCompleto;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriDialogCompleto\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Typography", "Box", "Grid", "Paper", "<PERSON><PERSON>", "CircularProgress", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "InputAdornment", "IconButton", "List", "ListItem", "ListItemButton", "Search", "SearchIcon", "Cancel", "CancelIcon", "AddCircleOutline", "AddCircleOutlineIcon", "caviService", "parcoCaviService", "getReelStateColor", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriDialogCompleto", "open", "onClose", "cavo", "cantiereId", "onSuccess", "onError", "loading", "_s", "formData", "setFormData", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "saving", "setSaving", "bobine", "set<PERSON>ob<PERSON>", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchText", "setSearchText", "searchType", "setSearchType", "getBobinaNumber", "idBobina", "includes", "split", "handleSelectBobina", "console", "log", "prev", "<PERSON><PERSON><PERSON><PERSON>", "find", "b", "handleSearchTextChange", "event", "target", "value", "loadBobine", "bobine<PERSON><PERSON>", "getBobine", "length", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "stato_bobina", "metri_residui", "tipologia", "sezione", "id_cavo", "cavoTipologia", "String", "trim", "toLowerCase", "cavoSezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "bobineNonCompatibili", "for<PERSON>ach", "bobinaTipologia", "bobinaSezione", "tipologiaMatch", "sezioneMatch", "isCompatible", "push", "sort", "a", "bobineOrdinate", "error", "_cavo$metratura_reale", "metratura_reale", "toString", "handleFormChange", "name", "metri", "parseFloat", "isNaN", "metri_te<PERSON>ci", "validateForm", "errors", "Object", "keys", "handleSave", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "handleClose", "_error$response", "_error$response$data", "success", "errorMessage", "response", "data", "detail", "message", "handleKeyPress", "key", "bobineFiltrate", "searchLower", "bobina<PERSON>d", "searchTerms", "map", "term", "some", "termStr", "isNumericTerm", "numericTerm", "bobinaSezioneNum", "children", "max<PERSON><PERSON><PERSON>", "fullWidth", "disableEscapeKeyDown", "PaperProps", "sx", "minHeight", "maxHeight", "pb", "variant", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dividers", "p", "mb", "bgcolor", "borderRadius", "container", "spacing", "item", "xs", "md", "ubicazione_partenza", "ubicazione_arrivo", "autoFocus", "label", "type", "onChange", "onKeyPress", "Boolean", "helperText", "FormHelperTextProps", "color", "disabled", "InputProps", "endAdornment", "inputProps", "max", "step", "fontWeight", "alignItems", "sm", "size", "placeholder", "startAdornment", "position", "fontSize", "onClick", "edge", "id", "labelId", "e", "height", "border", "undefined", "display", "justifyContent", "my", "width", "py", "px", "mr", "overflowY", "overflowX", "disablePadding", "secondaryAction", "dense", "flexGrow", "severity", "mt", "gap", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/InserisciMetriDialogCompleto.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Typography,\n  Box,\n  Grid,\n  Paper,\n  Alert,\n  CircularProgress,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip,\n  InputAdornment,\n  IconButton,\n  List,\n  ListItem,\n  ListItemButton\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Cancel as CancelIcon,\n  AddCircleOutline as AddCircleOutlineIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Dialog completo per inserire i metri posati di un cavo preselezionato\n * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Cavo preselezionato\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nconst InserisciMetriDialogCompleto = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  cantiereId,\n  onSuccess = () => {},\n  onError = () => {},\n  loading = false\n}) => {\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n  \n  // Stati per bobine\n  const [bobine, setBobine] = useState([]);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per la ricerca delle bobine\n  const [searchText, setSearchText] = useState('');\n  const [searchType, setSearchType] = useState('contains');\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = (idBobina) => {\n    console.log('Bobina selezionata:', idBobina);\n    setFormData(prev => ({\n      ...prev,\n      id_bobina: idBobina\n    }));\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: ''\n    }));\n\n    // Forza il re-render per mostrare le informazioni della bobina selezionata\n    const selectedBobina = bobine.find(b => b.id_bobina === idBobina);\n    if (selectedBobina) {\n      console.log('Dettagli bobina selezionata:', selectedBobina);\n    }\n  };\n\n  // Gestisce il cambio del testo di ricerca\n  const handleSearchTextChange = (event) => {\n    setSearchText(event.target.value);\n  };\n\n  // Carica le bobine disponibili\n  const loadBobine = useCallback(async () => {\n    if (!cantiereId || !cavo) return;\n\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      );\n\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (cavo && cavo.tipologia && cavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: cavo.id_cavo,\n          tipologia: cavo.tipologia,\n          sezione: cavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(cavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(cavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${cavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(cavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      setBobine([]);\n    } finally {\n      setBobineLoading(false);\n    }\n  }, [cantiereId, cavo]);\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        metri_posati: cavo.metratura_reale?.toString() || '',\n        id_bobina: ''\n      });\n      setFormErrors({});\n      setFormWarnings({});\n      setSaving(false);\n\n      // Carica le bobine disponibili\n      loadBobine();\n    }\n  }, [open, cavo, cantiereId, loadBobine]);\n\n  // Gestisce i cambiamenti del form\n  const handleFormChange = (event) => {\n    const { name, value } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Rimuovi errori quando l'utente inizia a digitare\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n    \n    // Validazione in tempo reale per metri posati\n    if (name === 'metri_posati' && value && cavo) {\n      const metri = parseFloat(value);\n      if (!isNaN(metri) && metri > cavo.metri_teorici) {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n        }));\n      } else {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: ''\n        }));\n      }\n    }\n  };\n\n  // Validazione del form\n  const validateForm = () => {\n    const errors = {};\n    \n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'I metri posati sono obbligatori';\n    } else {\n      const metri = parseFloat(formData.metri_posati);\n      if (isNaN(metri) || metri < 0) {\n        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';\n      }\n    }\n    \n    // Validazione bobina\n    if (!formData.id_bobina || formData.id_bobina === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina o utilizzare BOBINA VUOTA';\n    }\n    \n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch\n    const metriPosati = parseFloat(formData.metri_posati);\n    let idBobina = formData.id_bobina;\n\n    try {\n      setSaving(true);\n      \n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', cavo.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina);\n      \n      // Chiamata API con la funzione originale completa\n      await caviService.updateMetriPosati(\n        cantiereId,\n        cavo.id_cavo,\n        metriPosati,\n        idBobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n      \n      // Successo\n      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;\n      onSuccess(successMessage);\n      \n      // Chiudi il dialog\n      handleClose();\n      \n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      \n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n        handleClose();\n        return;\n      }\n      \n      // Gestione errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response?.data?.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      \n      onError(errorMessage);\n      \n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!saving && !loading) {\n      setFormErrors({});\n      setFormWarnings({});\n      setFormData({ metri_posati: '', id_bobina: '' });\n      onClose();\n    }\n  };\n\n  const handleKeyPress = (event) => {\n    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {\n      handleSave();\n    }\n  };\n\n  if (!cavo) return null;\n\n  // Filtra le bobine in base al testo di ricerca (logica identica all'originale)\n  const bobineFiltrate = bobine.filter(bobina => {\n    // Se non c'è testo di ricerca, mostra tutte le bobine\n    if (!searchText) return true;\n\n    // Per la ricerca esatta, prima verifica se l'intero testo corrisponde esattamente\n    if (searchType === 'equals' && !searchText.includes(',')) {\n      const searchLower = searchText.trim().toLowerCase();\n      const bobinaId = getBobinaNumber(bobina.id_bobina).toLowerCase();\n      const bobinaTipologia = String(bobina.tipologia || '').toLowerCase();\n      const bobinaSezione = String(bobina.sezione || '').toLowerCase();\n\n      // Verifica corrispondenza esatta con l'intero testo di ricerca\n      return bobinaId === searchLower ||\n             bobinaTipologia === searchLower ||\n             bobinaSezione === searchLower;\n    }\n\n    // Gestione ricerca con virgole (termini multipli)\n    const searchTerms = searchText.split(',').map(term => term.trim().toLowerCase()).filter(term => term.length > 0);\n\n    if (searchTerms.length > 1) {\n      // Ricerca con termini multipli\n      return searchTerms.some(term => {\n        const termStr = String(term);\n        const bobinaId = getBobinaNumber(bobina.id_bobina).toLowerCase();\n        const bobinaTipologia = String(bobina.tipologia || '').toLowerCase();\n        const bobinaSezione = String(bobina.sezione || '').toLowerCase();\n\n        // Verifica se il termine è un numero\n        const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));\n\n        if (isNumericTerm) {\n          const numericTerm = parseFloat(termStr);\n          const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));\n\n          // Verifica corrispondenza esatta per numeri e ID\n          return bobinaSezioneNum === numericTerm ||\n                 bobinaId === termStr ||\n                 bobinaTipologia === term ||\n                 bobinaSezione === term;\n        } else {\n          // Ricerca esatta per stringhe\n          return bobinaId === term ||\n                 bobinaTipologia === term ||\n                 bobinaSezione === term;\n        }\n      });\n    }\n\n    // Ricerca con singolo termine\n    const term = searchTerms[0] || searchText.trim().toLowerCase();\n    const termStr = String(term);\n    const bobinaId = getBobinaNumber(bobina.id_bobina).toLowerCase();\n    const bobinaTipologia = String(bobina.tipologia || '').toLowerCase();\n    const bobinaSezione = String(bobina.sezione || '').toLowerCase();\n\n    // Verifica se il termine è un numero\n    const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));\n\n    if (searchType === 'equals') {\n      // Ricerca esatta\n      if (isNumericTerm) {\n        const numericTerm = parseFloat(termStr);\n        const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));\n\n        // Verifica corrispondenza esatta per numeri e ID\n        return bobinaSezioneNum === numericTerm ||\n               bobinaId === termStr ||\n               bobinaTipologia === term ||\n               bobinaSezione === term;\n      } else {\n        // Ricerca esatta per stringhe\n        return bobinaId === term ||\n               bobinaTipologia === term ||\n               bobinaSezione === term;\n      }\n    } else {\n      // Ricerca con 'contains' (comportamento predefinito)\n      if (isNumericTerm) {\n        const numericTerm = parseFloat(termStr);\n        const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));\n\n        // Verifica corrispondenza esatta per numeri\n        if (bobinaSezioneNum === numericTerm) {\n          return true;\n        }\n\n        // Verifica se il numero è contenuto nell'ID\n        if (bobinaId.includes(termStr)) {\n          return true;\n        }\n      }\n\n      // Ricerca standard con includes\n      return bobinaId.includes(term) ||\n             bobinaTipologia.includes(term) ||\n             bobinaSezione.includes(term);\n    }\n  });\n\n  // Separa le bobine compatibili e non compatibili\n  const bobineCompatibili = cavo\n    ? bobineFiltrate.filter(bobina =>\n        String(bobina.tipologia || '').trim() === String(cavo.tipologia || '').trim() &&\n        String(bobina.sezione || '0').trim() === String(cavo.sezione || '0').trim())\n    : bobineFiltrate;\n\n  const bobineNonCompatibili = cavo\n    ? bobineFiltrate.filter(bobina =>\n        String(bobina.tipologia || '').trim() !== String(cavo.tipologia || '').trim() ||\n        String(bobina.sezione || '0').trim() !== String(cavo.sezione || '0').trim())\n    : [];\n\n  return (\n    <>\n      <Dialog\n        open={open}\n        onClose={handleClose}\n        maxWidth=\"xl\"\n        fullWidth\n        disableEscapeKeyDown={saving || loading}\n        PaperProps={{\n          sx: {\n            minHeight: '700px',\n            maxHeight: '90vh'\n          }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" component=\"div\">\n            Inserisci Metri Posati - {cavo.id_cavo}\n          </Typography>\n        </DialogTitle>\n\n        <DialogContent dividers sx={{ p: 3 }}>\n          {/* Informazioni cavo */}\n          <Paper sx={{ p: 3, mb: 4, bgcolor: '#f8f9fa', borderRadius: 2 }}>\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\" sx={{ mb: 1 }}><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\" sx={{ mb: 1 }}><strong>Sezione:</strong> {cavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\" sx={{ mb: 1 }}><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\" sx={{ mb: 1 }}><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\" sx={{ mb: 1 }}><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\" sx={{ mb: 1 }}><strong>Attualmente posati:</strong> {cavo.metratura_reale || 0} m</Typography>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          {/* Campo metri posati */}\n          <Box sx={{ mb: 3 }}>\n            <TextField\n              autoFocus\n              label=\"Metri Posati\"\n              type=\"number\"\n              fullWidth\n              name=\"metri_posati\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              onKeyPress={handleKeyPress}\n              error={Boolean(formErrors.metri_posati)}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati || `Metri teorici: ${cavo.metri_teorici}m`}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              disabled={saving || loading}\n              InputProps={{\n                endAdornment: <Typography variant=\"body2\" color=\"text.secondary\">m</Typography>\n              }}\n              inputProps={{\n                max: 999999,\n                step: 0.1\n              }}\n              sx={{ mb: 2 }}\n            />\n          </Box>\n\n          {/* Selezione bobina con doppia lista */}\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold', mb: 2 }}>\n              Selezione Bobina\n            </Typography>\n\n            {/* Controlli di ricerca e BOBINA_VUOTA */}\n            <Box sx={{ mb: 3 }}>\n              <Grid container spacing={2} alignItems=\"center\">\n                {/* Campo di ricerca */}\n                <Grid item xs={12} sm={6} md={4}>\n                  <TextField\n                    size=\"small\"\n                    label=\"Cerca\"\n                    variant=\"outlined\"\n                    value={searchText}\n                    onChange={handleSearchTextChange}\n                    placeholder=\"ID, tipologia, formazione...\"\n                    fullWidth\n                    InputProps={{\n                      startAdornment: (\n                        <InputAdornment position=\"start\">\n                          <SearchIcon fontSize=\"small\" />\n                        </InputAdornment>\n                      ),\n                      endAdornment: searchText ? (\n                        <InputAdornment position=\"end\">\n                          <IconButton\n                            size=\"small\"\n                            aria-label=\"clear search\"\n                            onClick={() => setSearchText('')}\n                            edge=\"end\"\n                          >\n                            <CancelIcon fontSize=\"small\" />\n                          </IconButton>\n                        </InputAdornment>\n                      ) : null\n                    }}\n                  />\n                </Grid>\n\n                {/* Dropdown per il tipo di ricerca */}\n                <Grid item xs={12} sm={6} md={3}>\n                  <FormControl size=\"small\" fullWidth>\n                    <InputLabel id=\"search-type-label\">Tipo ricerca</InputLabel>\n                    <Select\n                      labelId=\"search-type-label\"\n                      value={searchType}\n                      label=\"Tipo ricerca\"\n                      onChange={(e) => setSearchType(e.target.value)}\n                    >\n                      <MenuItem value=\"contains\">Contiene</MenuItem>\n                      <MenuItem value=\"equals\">Uguale a</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Grid>\n\n                {/* Pulsante BOBINA VUOTA */}\n                <Grid item xs={12} sm={12} md={5}>\n                  <Button\n                    variant=\"outlined\"\n                    size=\"medium\"\n                    onClick={() => handleSelectBobina('BOBINA_VUOTA')}\n                    fullWidth\n                    sx={{\n                      height: '40px',\n                      fontWeight: formData.id_bobina === 'BOBINA_VUOTA' ? 'bold' : 'normal',\n                      bgcolor: formData.id_bobina === 'BOBINA_VUOTA' ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                      border: formData.id_bobina === 'BOBINA_VUOTA' ? '1px solid #4caf50' : undefined\n                    }}\n                  >\n                    BOBINA VUOTA\n                  </Button>\n                </Grid>\n              </Grid>\n            </Box>\n\n            {bobineLoading ? (\n              <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>\n                <CircularProgress size={24} />\n              </Box>\n            ) : (\n              <Box>\n                {/* Griglia per le due liste di bobine */}\n                <Grid container spacing={2}>\n                  {/* Colonna sinistra: Bobine compatibili */}\n                  <Grid item xs={12} md={6}>\n                    <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n                      <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                        ELENCO BOBINE COMPATIBILI\n                      </Typography>\n\n                      {bobineCompatibili.length > 0 ? (\n                        <>\n                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 1, px: 2, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>\n                            <Box sx={{ width: '15%', mr: 1 }}>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                            </Box>\n                            <Box sx={{ width: '25%', mr: 1 }}>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>\n                            </Box>\n                            <Box sx={{ width: '15%', mr: 1 }}>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>\n                            </Box>\n                            <Box sx={{ width: '20%', mr: 1 }}>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>\n                            </Box>\n                            <Box sx={{ width: '20%' }}>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                            </Box>\n                            <Box sx={{ width: '5%' }}>\n                              {/* Spazio per il pulsante */}\n                            </Box>\n                          </Box>\n                          <List sx={{ maxHeight: bobineCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>\n                            {bobineCompatibili.map((bobina) => (\n                              <ListItem\n                                key={bobina.id_bobina}\n                                disablePadding\n                                secondaryAction={\n                                  <IconButton\n                                    edge=\"end\"\n                                    size=\"small\"\n                                    onClick={() => handleSelectBobina(bobina.id_bobina)}\n                                  >\n                                    <AddCircleOutlineIcon color=\"primary\" />\n                                  </IconButton>\n                                }\n                                sx={{\n                                  bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                                  borderRadius: '4px',\n                                  mb: 0.5,\n                                  border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                                }}\n                              >\n                                <ListItemButton\n                                  dense\n                                  onClick={() => handleSelectBobina(bobina.id_bobina)}\n                                >\n                                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>\n                                    <Box sx={{ width: '60px', mr: 2 }}>\n                                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>\n                                        {getBobinaNumber(bobina.id_bobina)}\n                                      </Typography>\n                                    </Box>\n                                    <Box sx={{ width: '120px', mr: 2 }}>\n                                      <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                        {bobina.tipologia || 'N/A'}\n                                      </Typography>\n                                    </Box>\n                                    <Box sx={{ width: '100px', mr: 2 }}>\n                                      <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                        {bobina.sezione || 'N/A'}\n                                      </Typography>\n                                    </Box>\n                                    <Box sx={{ width: '100px', mr: 2 }}>\n                                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                        {bobina.metri_residui || 0} m\n                                      </Typography>\n                                    </Box>\n                                    <Box sx={{ flexGrow: 0 }}>\n                                      <Chip\n                                        size=\"small\"\n                                        label={bobina.stato_bobina || 'N/D'}\n                                        color={getReelStateColor(bobina.stato_bobina)}\n                                        variant=\"outlined\"\n                                        sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                                      />\n                                    </Box>\n                                  </Box>\n                                </ListItemButton>\n                              </ListItem>\n                            ))}\n                          </List>\n                        </>\n                      ) : (\n                        <Alert severity=\"info\" sx={{ mt: 1 }}>\n                          Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                        </Alert>\n                      )}\n                    </Paper>\n                  </Grid>\n\n                  {/* Colonna destra: Bobine non compatibili */}\n                  <Grid item xs={12} md={6}>\n                    <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n                      <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                        ELENCO BOBINE NON COMPATIBILI\n                      </Typography>\n\n                      {bobineNonCompatibili.length > 0 ? (\n                        <>\n                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>\n                            <Box sx={{ width: '60px', mr: 2 }}>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                            </Box>\n                            <Box sx={{ width: '120px', mr: 2 }}>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>\n                            </Box>\n                            <Box sx={{ width: '100px', mr: 2 }}>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>\n                            </Box>\n                            <Box sx={{ width: '100px', mr: 2 }}>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>\n                            </Box>\n                            <Box sx={{ flexGrow: 0 }}>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                            </Box>\n                          </Box>\n                          <List sx={{ maxHeight: bobineNonCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineNonCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>\n                            {bobineNonCompatibili.map((bobina) => (\n                              <ListItem\n                                key={bobina.id_bobina}\n                                disablePadding\n                                secondaryAction={\n                                  <IconButton\n                                    edge=\"end\"\n                                    size=\"small\"\n                                    onClick={() => handleSelectBobina(bobina.id_bobina)}\n                                  >\n                                    <AddCircleOutlineIcon color=\"primary\" />\n                                  </IconButton>\n                                }\n                                sx={{\n                                  bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                                  borderRadius: '4px',\n                                  mb: 0.5,\n                                  border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                                }}\n                              >\n                                <ListItemButton\n                                  dense\n                                  onClick={() => handleSelectBobina(bobina.id_bobina)}\n                                >\n                                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>\n                                    <Box sx={{ width: '60px', mr: 2 }}>\n                                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>\n                                        {getBobinaNumber(bobina.id_bobina)}\n                                      </Typography>\n                                    </Box>\n                                    <Box sx={{ width: '120px', mr: 2 }}>\n                                      <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                        {bobina.tipologia || 'N/A'}\n                                      </Typography>\n                                    </Box>\n                                    <Box sx={{ width: '100px', mr: 2 }}>\n                                      <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                        {bobina.sezione || 'N/A'}\n                                      </Typography>\n                                    </Box>\n                                    <Box sx={{ width: '100px', mr: 2 }}>\n                                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                        {bobina.metri_residui || 0} m\n                                      </Typography>\n                                    </Box>\n                                    <Box sx={{ display: 'flex', gap: 1 }}>\n                                      <Chip\n                                        size=\"small\"\n                                        label={bobina.stato_bobina || 'N/D'}\n                                        color={getReelStateColor(bobina.stato_bobina)}\n                                        variant=\"outlined\"\n                                        sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                                      />\n                                      <Chip\n                                        size=\"small\"\n                                        label=\"Non comp.\"\n                                        color=\"warning\"\n                                        variant=\"outlined\"\n                                        sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                                      />\n                                    </Box>\n                                  </Box>\n                                </ListItemButton>\n                              </ListItem>\n                            ))}\n                          </List>\n                        </>\n                      ) : (\n                        <Alert severity=\"info\" sx={{ mt: 1 }}>\n                          Nessuna bobina non compatibile disponibile con i filtri attuali.\n                        </Alert>\n                      )}\n                    </Paper>\n                  </Grid>\n                </Grid>\n              </Box>\n            )}\n\n            {bobine.length === 0 && !bobineLoading && (\n              <Alert severity=\"warning\" sx={{ mt: 2 }}>\n                Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n              </Alert>\n            )}\n\n            {formErrors.id_bobina && (\n              <Typography variant=\"caption\" color=\"error\" sx={{ mt: 1, display: 'block' }}>\n                {formErrors.id_bobina}\n              </Typography>\n            )}\n          </Box>\n        </DialogContent>\n\n        <DialogActions>\n          <Button\n            onClick={handleClose}\n            disabled={saving || loading}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            variant=\"contained\"\n            disabled={saving || loading || !formData.metri_posati || !formData.id_bobina}\n            startIcon={saving ? <CircularProgress size={20} /> : null}\n          >\n            {saving ? 'Salvando...' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </>\n  );\n};\n\nexport default InserisciMetriDialogCompleto;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,gBAAgB,EAChBC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,gBAAgB,IAAIC,oBAAoB,QACnC,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,iBAAiB,QAAQ,wBAAwB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAaA,MAAMC,4BAA4B,GAAGA,CAAC;EACpCC,IAAI,GAAG,KAAK;EACZC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,IAAI,GAAG,IAAI;EACXC,UAAU;EACVC,SAAS,GAAGA,CAAA,KAAM,CAAC,CAAC;EACpBC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC;IACvCiD,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACuD,MAAM,EAAEC,SAAS,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACyD,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,UAAU,CAAC;;EAExD;EACA,MAAMiE,eAAe,GAAIC,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,kBAAkB,GAAIH,QAAQ,IAAK;IACvCI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEL,QAAQ,CAAC;IAC5ClB,WAAW,CAACwB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPtB,SAAS,EAAEgB;IACb,CAAC,CAAC,CAAC;;IAEH;IACAd,aAAa,CAACoB,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPtB,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMuB,cAAc,GAAGhB,MAAM,CAACiB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzB,SAAS,KAAKgB,QAAQ,CAAC;IACjE,IAAIO,cAAc,EAAE;MAClBH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEE,cAAc,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMG,sBAAsB,GAAIC,KAAK,IAAK;IACxCf,aAAa,CAACe,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG9E,WAAW,CAAC,YAAY;IACzC,IAAI,CAACwC,UAAU,IAAI,CAACD,IAAI,EAAE;IAE1B,IAAI;MACFmB,gBAAgB,CAAC,IAAI,CAAC;MACtBU,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMU,UAAU,GAAG,MAAMjD,gBAAgB,CAACkD,SAAS,CAACxC,UAAU,CAAC;MAC/D4B,OAAO,CAACC,GAAG,CAAC,oBAAoBU,UAAU,CAACE,MAAM,EAAE,CAAC;;MAEpD;MACA,MAAMC,kBAAkB,GAAGH,UAAU,CAACI,MAAM,CAACC,MAAM,IACjDA,MAAM,CAACC,YAAY,KAAK,WAAW,IACnCD,MAAM,CAACC,YAAY,KAAK,MAAM,IAC9BD,MAAM,CAACE,aAAa,GAAG,CACzB,CAAC;MAEDlB,OAAO,CAACC,GAAG,CAAC,wBAAwBa,kBAAkB,CAACD,MAAM,EAAE,CAAC;;MAEhE;MACA,IAAI1C,IAAI,IAAIA,IAAI,CAACgD,SAAS,IAAIhD,IAAI,CAACiD,OAAO,EAAE;QAC1CpB,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnED,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;UACxBoB,OAAO,EAAElD,IAAI,CAACkD,OAAO;UACrBF,SAAS,EAAEhD,IAAI,CAACgD,SAAS;UACzBC,OAAO,EAAEjD,IAAI,CAACiD;QAChB,CAAC,CAAC;;QAEF;QACA,MAAME,aAAa,GAAGC,MAAM,CAACpD,IAAI,CAACgD,SAAS,IAAI,EAAE,CAAC,CAACK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACvE,MAAMC,WAAW,GAAGH,MAAM,CAACpD,IAAI,CAACiD,OAAO,IAAI,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC;;QAEtD;QACA,MAAMG,iBAAiB,GAAG,EAAE;QAC5B,MAAMC,oBAAoB,GAAG,EAAE;;QAE/B;QACAd,kBAAkB,CAACe,OAAO,CAACb,MAAM,IAAI;UACnC,MAAMc,eAAe,GAAGP,MAAM,CAACP,MAAM,CAACG,SAAS,IAAI,EAAE,CAAC,CAACK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAC3E,MAAMM,aAAa,GAAGR,MAAM,CAACP,MAAM,CAACI,OAAO,IAAI,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC;;UAE1D;UACA,MAAMQ,cAAc,GAAGF,eAAe,KAAKR,aAAa;UACxD,MAAMW,YAAY,GAAGF,aAAa,KAAKL,WAAW;UAClD,MAAMQ,YAAY,GAAGF,cAAc,IAAIC,YAAY;UAEnDjC,OAAO,CAACC,GAAG,CAAC,iCAAiCe,MAAM,CAACpC,SAAS,GAAG,EAAE;YAChE,kBAAkB,EAAE,IAAIoC,MAAM,CAACG,SAAS,GAAG;YAC3C,gBAAgB,EAAE,IAAIhD,IAAI,CAACgD,SAAS,GAAG;YACvC,mBAAmB,EAAEa,cAAc;YACnC,gBAAgB,EAAE,IAAIT,MAAM,CAACP,MAAM,CAACI,OAAO,CAAC,GAAG;YAC/C,cAAc,EAAE,IAAIG,MAAM,CAACpD,IAAI,CAACiD,OAAO,CAAC,GAAG;YAC3C,iBAAiB,EAAEa,YAAY;YAC/B,cAAc,EAAEjB,MAAM,CAACC,YAAY;YACnC,eAAe,EAAED,MAAM,CAACE,aAAa;YACrC,cAAc,EAAEgB;UAClB,CAAC,CAAC;UAEF,IAAIA,YAAY,EAAE;YAChBP,iBAAiB,CAACQ,IAAI,CAACnB,MAAM,CAAC;UAChC,CAAC,MAAM;YACLY,oBAAoB,CAACO,IAAI,CAACnB,MAAM,CAAC;UACnC;QACF,CAAC,CAAC;QAEFhB,OAAO,CAACC,GAAG,CAAC,+BAA+B0B,iBAAiB,CAACd,MAAM,EAAE,CAAC;QACtEb,OAAO,CAACC,GAAG,CAAC,2BAA2B2B,oBAAoB,CAACf,MAAM,EAAE,CAAC;;QAErE;QACA,IAAIc,iBAAiB,CAACd,MAAM,GAAG,CAAC,EAAE;UAChCb,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5C0B,iBAAiB,CAACE,OAAO,CAACb,MAAM,IAAI;YAClChB,OAAO,CAACC,GAAG,CAAC,KAAKe,MAAM,CAACpC,SAAS,KAAKoC,MAAM,CAACG,SAAS,MAAMH,MAAM,CAACI,OAAO,KAAKJ,MAAM,CAACE,aAAa,IAAI,CAAC;UAC1G,CAAC,CAAC;QACJ,CAAC,MAAM;UACLlB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAChE;;QAEA;QACA0B,iBAAiB,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEhC,CAAC,KAAKA,CAAC,CAACa,aAAa,GAAGmB,CAAC,CAACnB,aAAa,CAAC;QACnEU,oBAAoB,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEhC,CAAC,KAAKA,CAAC,CAACa,aAAa,GAAGmB,CAAC,CAACnB,aAAa,CAAC;;QAEtE;QACA,MAAMoB,cAAc,GAAG,CAAC,GAAGX,iBAAiB,EAAE,GAAGC,oBAAoB,CAAC;;QAEtE;QACAxC,SAAS,CAACkD,cAAc,CAAC;MAC3B,CAAC,MAAM;QACL;QACAtC,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpEa,kBAAkB,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEhC,CAAC,KAAKA,CAAC,CAACa,aAAa,GAAGmB,CAAC,CAACnB,aAAa,CAAC;QACpE9B,SAAS,CAAC0B,kBAAkB,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdvC,OAAO,CAACuC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DnD,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,CAAClB,UAAU,EAAED,IAAI,CAAC,CAAC;;EAEtB;EACAxC,SAAS,CAAC,MAAM;IACd,IAAIsC,IAAI,IAAIE,IAAI,EAAE;MAAA,IAAAqE,qBAAA;MAChB9D,WAAW,CAAC;QACVC,YAAY,EAAE,EAAA6D,qBAAA,GAAArE,IAAI,CAACsE,eAAe,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,QAAQ,CAAC,CAAC,KAAI,EAAE;QACpD9D,SAAS,EAAE;MACb,CAAC,CAAC;MACFE,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBE,SAAS,CAAC,KAAK,CAAC;;MAEhB;MACAwB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACzC,IAAI,EAAEE,IAAI,EAAEC,UAAU,EAAEsC,UAAU,CAAC,CAAC;;EAExC;EACA,MAAMiC,gBAAgB,GAAIpC,KAAK,IAAK;IAClC,MAAM;MAAEqC,IAAI;MAAEnC;IAAM,CAAC,GAAGF,KAAK,CAACC,MAAM;IACpC9B,WAAW,CAACwB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC0C,IAAI,GAAGnC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAI5B,UAAU,CAAC+D,IAAI,CAAC,EAAE;MACpB9D,aAAa,CAACoB,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAAC0C,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIA,IAAI,KAAK,cAAc,IAAInC,KAAK,IAAItC,IAAI,EAAE;MAC5C,MAAM0E,KAAK,GAAGC,UAAU,CAACrC,KAAK,CAAC;MAC/B,IAAI,CAACsC,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAG1E,IAAI,CAAC6E,aAAa,EAAE;QAC/ChE,eAAe,CAACkB,IAAI,KAAK;UACvB,GAAGA,IAAI;UACPvB,YAAY,EAAE,mBAAmBkE,KAAK,yCAAyC1E,IAAI,CAAC6E,aAAa;QACnG,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLhE,eAAe,CAACkB,IAAI,KAAK;UACvB,GAAGA,IAAI;UACPvB,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;;EAED;EACA,MAAMsE,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACzE,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACE,YAAY,CAAC6C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjE0B,MAAM,CAACvE,YAAY,GAAG,iCAAiC;IACzD,CAAC,MAAM;MACL,MAAMkE,KAAK,GAAGC,UAAU,CAACrE,QAAQ,CAACE,YAAY,CAAC;MAC/C,IAAIoE,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QAC7BK,MAAM,CAACvE,YAAY,GAAG,0DAA0D;MAClF;IACF;;IAEA;IACA,IAAI,CAACF,QAAQ,CAACG,SAAS,IAAIH,QAAQ,CAACG,SAAS,KAAK,EAAE,EAAE;MACpDsE,MAAM,CAACtE,SAAS,GAAG,+DAA+D;IACpF;IAEAE,aAAa,CAACoE,MAAM,CAAC;IACrB,OAAOC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACrC,MAAM,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMwC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACJ,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;;IAEA;IACA,MAAMK,WAAW,GAAGR,UAAU,CAACrE,QAAQ,CAACE,YAAY,CAAC;IACrD,IAAIiB,QAAQ,GAAGnB,QAAQ,CAACG,SAAS;IAEjC,IAAI;MACFM,SAAS,CAAC,IAAI,CAAC;MAEfc,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE7B,UAAU,CAAC;MACxC4B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE9B,IAAI,CAACkD,OAAO,CAAC;MACvCrB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqD,WAAW,CAAC;MAC3CtD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEL,QAAQ,CAAC;;MAErC;MACA,MAAMnC,WAAW,CAAC8F,iBAAiB,CACjCnF,UAAU,EACVD,IAAI,CAACkD,OAAO,EACZiC,WAAW,EACX1D,QAAQ,EACR,IAAI,CAAC;MACP,CAAC;;MAED;MACA,MAAM4D,cAAc,GAAG,oDAAoDrF,IAAI,CAACkD,OAAO,KAAKiC,WAAW,GAAG;MAC1GjF,SAAS,CAACmF,cAAc,CAAC;;MAEzB;MACAC,WAAW,CAAC,CAAC;IAEf,CAAC,CAAC,OAAOlB,KAAK,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACd3D,OAAO,CAACuC,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAI3C,QAAQ,KAAK,cAAc,IAAI2C,KAAK,CAACqB,OAAO,EAAE;QAChD,MAAMJ,cAAc,GAAG,qEAAqE;QAC5FnF,SAAS,CAACmF,cAAc,CAAC;QACzBC,WAAW,CAAC,CAAC;QACb;MACF;;MAEA;MACA,IAAII,YAAY,GAAG,kDAAkD;MACrE,KAAAH,eAAA,GAAInB,KAAK,CAACuB,QAAQ,cAAAJ,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBK,IAAI,cAAAJ,oBAAA,eAApBA,oBAAA,CAAsBK,MAAM,EAAE;QAChCH,YAAY,GAAGtB,KAAK,CAACuB,QAAQ,CAACC,IAAI,CAACC,MAAM;MAC3C,CAAC,MAAM,IAAIzB,KAAK,CAAC0B,OAAO,EAAE;QACxBJ,YAAY,GAAGtB,KAAK,CAAC0B,OAAO;MAC9B;MAEA3F,OAAO,CAACuF,YAAY,CAAC;IAEvB,CAAC,SAAS;MACR3E,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMuE,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACxE,MAAM,IAAI,CAACV,OAAO,EAAE;MACvBO,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBN,WAAW,CAAC;QAAEC,YAAY,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAC,CAAC;MAChDV,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAMgG,cAAc,GAAI3D,KAAK,IAAK;IAChC,IAAIA,KAAK,CAAC4D,GAAG,KAAK,OAAO,IAAI,CAAClF,MAAM,IAAI,CAACV,OAAO,IAAIE,QAAQ,CAACE,YAAY,CAAC6C,IAAI,CAAC,CAAC,IAAI/C,QAAQ,CAACG,SAAS,EAAE;MACtGyE,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,IAAI,CAAClF,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMiG,cAAc,GAAGjF,MAAM,CAAC4B,MAAM,CAACC,MAAM,IAAI;IAC7C;IACA,IAAI,CAACzB,UAAU,EAAE,OAAO,IAAI;;IAE5B;IACA,IAAIE,UAAU,KAAK,QAAQ,IAAI,CAACF,UAAU,CAACM,QAAQ,CAAC,GAAG,CAAC,EAAE;MACxD,MAAMwE,WAAW,GAAG9E,UAAU,CAACiC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnD,MAAM6C,QAAQ,GAAG3E,eAAe,CAACqB,MAAM,CAACpC,SAAS,CAAC,CAAC6C,WAAW,CAAC,CAAC;MAChE,MAAMK,eAAe,GAAGP,MAAM,CAACP,MAAM,CAACG,SAAS,IAAI,EAAE,CAAC,CAACM,WAAW,CAAC,CAAC;MACpE,MAAMM,aAAa,GAAGR,MAAM,CAACP,MAAM,CAACI,OAAO,IAAI,EAAE,CAAC,CAACK,WAAW,CAAC,CAAC;;MAEhE;MACA,OAAO6C,QAAQ,KAAKD,WAAW,IACxBvC,eAAe,KAAKuC,WAAW,IAC/BtC,aAAa,KAAKsC,WAAW;IACtC;;IAEA;IACA,MAAME,WAAW,GAAGhF,UAAU,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC0E,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACjD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAACV,MAAM,CAAC0D,IAAI,IAAIA,IAAI,CAAC5D,MAAM,GAAG,CAAC,CAAC;IAEhH,IAAI0D,WAAW,CAAC1D,MAAM,GAAG,CAAC,EAAE;MAC1B;MACA,OAAO0D,WAAW,CAACG,IAAI,CAACD,IAAI,IAAI;QAC9B,MAAME,OAAO,GAAGpD,MAAM,CAACkD,IAAI,CAAC;QAC5B,MAAMH,QAAQ,GAAG3E,eAAe,CAACqB,MAAM,CAACpC,SAAS,CAAC,CAAC6C,WAAW,CAAC,CAAC;QAChE,MAAMK,eAAe,GAAGP,MAAM,CAACP,MAAM,CAACG,SAAS,IAAI,EAAE,CAAC,CAACM,WAAW,CAAC,CAAC;QACpE,MAAMM,aAAa,GAAGR,MAAM,CAACP,MAAM,CAACI,OAAO,IAAI,EAAE,CAAC,CAACK,WAAW,CAAC,CAAC;;QAEhE;QACA,MAAMmD,aAAa,GAAG,CAAC7B,KAAK,CAAC4B,OAAO,CAAC,IAAI,CAAC5B,KAAK,CAACD,UAAU,CAAC6B,OAAO,CAAC,CAAC;QAEpE,IAAIC,aAAa,EAAE;UACjB,MAAMC,WAAW,GAAG/B,UAAU,CAAC6B,OAAO,CAAC;UACvC,MAAMG,gBAAgB,GAAGhC,UAAU,CAACvB,MAAM,CAACP,MAAM,CAACI,OAAO,IAAI,GAAG,CAAC,CAAC;;UAElE;UACA,OAAO0D,gBAAgB,KAAKD,WAAW,IAChCP,QAAQ,KAAKK,OAAO,IACpB7C,eAAe,KAAK2C,IAAI,IACxB1C,aAAa,KAAK0C,IAAI;QAC/B,CAAC,MAAM;UACL;UACA,OAAOH,QAAQ,KAAKG,IAAI,IACjB3C,eAAe,KAAK2C,IAAI,IACxB1C,aAAa,KAAK0C,IAAI;QAC/B;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMA,IAAI,GAAGF,WAAW,CAAC,CAAC,CAAC,IAAIhF,UAAU,CAACiC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC9D,MAAMkD,OAAO,GAAGpD,MAAM,CAACkD,IAAI,CAAC;IAC5B,MAAMH,QAAQ,GAAG3E,eAAe,CAACqB,MAAM,CAACpC,SAAS,CAAC,CAAC6C,WAAW,CAAC,CAAC;IAChE,MAAMK,eAAe,GAAGP,MAAM,CAACP,MAAM,CAACG,SAAS,IAAI,EAAE,CAAC,CAACM,WAAW,CAAC,CAAC;IACpE,MAAMM,aAAa,GAAGR,MAAM,CAACP,MAAM,CAACI,OAAO,IAAI,EAAE,CAAC,CAACK,WAAW,CAAC,CAAC;;IAEhE;IACA,MAAMmD,aAAa,GAAG,CAAC7B,KAAK,CAAC4B,OAAO,CAAC,IAAI,CAAC5B,KAAK,CAACD,UAAU,CAAC6B,OAAO,CAAC,CAAC;IAEpE,IAAIlF,UAAU,KAAK,QAAQ,EAAE;MAC3B;MACA,IAAImF,aAAa,EAAE;QACjB,MAAMC,WAAW,GAAG/B,UAAU,CAAC6B,OAAO,CAAC;QACvC,MAAMG,gBAAgB,GAAGhC,UAAU,CAACvB,MAAM,CAACP,MAAM,CAACI,OAAO,IAAI,GAAG,CAAC,CAAC;;QAElE;QACA,OAAO0D,gBAAgB,KAAKD,WAAW,IAChCP,QAAQ,KAAKK,OAAO,IACpB7C,eAAe,KAAK2C,IAAI,IACxB1C,aAAa,KAAK0C,IAAI;MAC/B,CAAC,MAAM;QACL;QACA,OAAOH,QAAQ,KAAKG,IAAI,IACjB3C,eAAe,KAAK2C,IAAI,IACxB1C,aAAa,KAAK0C,IAAI;MAC/B;IACF,CAAC,MAAM;MACL;MACA,IAAIG,aAAa,EAAE;QACjB,MAAMC,WAAW,GAAG/B,UAAU,CAAC6B,OAAO,CAAC;QACvC,MAAMG,gBAAgB,GAAGhC,UAAU,CAACvB,MAAM,CAACP,MAAM,CAACI,OAAO,IAAI,GAAG,CAAC,CAAC;;QAElE;QACA,IAAI0D,gBAAgB,KAAKD,WAAW,EAAE;UACpC,OAAO,IAAI;QACb;;QAEA;QACA,IAAIP,QAAQ,CAACzE,QAAQ,CAAC8E,OAAO,CAAC,EAAE;UAC9B,OAAO,IAAI;QACb;MACF;;MAEA;MACA,OAAOL,QAAQ,CAACzE,QAAQ,CAAC4E,IAAI,CAAC,IACvB3C,eAAe,CAACjC,QAAQ,CAAC4E,IAAI,CAAC,IAC9B1C,aAAa,CAAClC,QAAQ,CAAC4E,IAAI,CAAC;IACrC;EACF,CAAC,CAAC;;EAEF;EACA,MAAM9C,iBAAiB,GAAGxD,IAAI,GAC1BiG,cAAc,CAACrD,MAAM,CAACC,MAAM,IAC1BO,MAAM,CAACP,MAAM,CAACG,SAAS,IAAI,EAAE,CAAC,CAACK,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpD,IAAI,CAACgD,SAAS,IAAI,EAAE,CAAC,CAACK,IAAI,CAAC,CAAC,IAC7ED,MAAM,CAACP,MAAM,CAACI,OAAO,IAAI,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpD,IAAI,CAACiD,OAAO,IAAI,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC,GAC9E4C,cAAc;EAElB,MAAMxC,oBAAoB,GAAGzD,IAAI,GAC7BiG,cAAc,CAACrD,MAAM,CAACC,MAAM,IAC1BO,MAAM,CAACP,MAAM,CAACG,SAAS,IAAI,EAAE,CAAC,CAACK,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpD,IAAI,CAACgD,SAAS,IAAI,EAAE,CAAC,CAACK,IAAI,CAAC,CAAC,IAC7ED,MAAM,CAACP,MAAM,CAACI,OAAO,IAAI,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpD,IAAI,CAACiD,OAAO,IAAI,GAAG,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC,GAC9E,EAAE;EAEN,oBACE3D,OAAA,CAAAE,SAAA;IAAAgH,QAAA,eACElH,OAAA,CAAChC,MAAM;MACLoC,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEuF,WAAY;MACrBuB,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,oBAAoB,EAAEjG,MAAM,IAAIV,OAAQ;MACxC4G,UAAU,EAAE;QACVC,EAAE,EAAE;UACFC,SAAS,EAAE,OAAO;UAClBC,SAAS,EAAE;QACb;MACF,CAAE;MAAAP,QAAA,gBAEFlH,OAAA,CAAC/B,WAAW;QAACsJ,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,eACzBlH,OAAA,CAAC1B,UAAU;UAACqJ,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,KAAK;UAAAV,QAAA,GAAC,2BACd,EAAC5G,IAAI,CAACkD,OAAO;QAAA;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEdhI,OAAA,CAAC9B,aAAa;QAAC+J,QAAQ;QAACV,EAAE,EAAE;UAAEW,CAAC,EAAE;QAAE,CAAE;QAAAhB,QAAA,gBAEnClH,OAAA,CAACvB,KAAK;UAAC8I,EAAE,EAAE;YAAEW,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAnB,QAAA,eAC9DlH,OAAA,CAACxB,IAAI;YAAC8J,SAAS;YAACC,OAAO,EAAE,CAAE;YAAArB,QAAA,gBACzBlH,OAAA,CAACxB,IAAI;cAACgK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAxB,QAAA,gBACvBlH,OAAA,CAAC1B,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,gBAAClH,OAAA;kBAAAkH,QAAA,EAAQ;gBAAU;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAACgD,SAAS,IAAI,KAAK;cAAA;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7GhI,OAAA,CAAC1B,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,gBAAClH,OAAA;kBAAAkH,QAAA,EAAQ;gBAAQ;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAACiD,OAAO,IAAI,KAAK;cAAA;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGhI,OAAA,CAAC1B,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,gBAAClH,OAAA;kBAAAkH,QAAA,EAAQ;gBAAc;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAAC6E,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnH,CAAC,eACPhI,OAAA,CAACxB,IAAI;cAACgK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAxB,QAAA,gBACvBlH,OAAA,CAAC1B,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,gBAAClH,OAAA;kBAAAkH,QAAA,EAAQ;gBAAG;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAACqI,mBAAmB,IAAI,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHhI,OAAA,CAAC1B,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,gBAAClH,OAAA;kBAAAkH,QAAA,EAAQ;gBAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAACsI,iBAAiB,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7GhI,OAAA,CAAC1B,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,gBAAClH,OAAA;kBAAAkH,QAAA,EAAQ;gBAAmB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAACsE,eAAe,IAAI,CAAC,EAAC,IAAE;cAAA;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGRhI,OAAA,CAACzB,GAAG;UAACgJ,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAjB,QAAA,eACjBlH,OAAA,CAAC3B,SAAS;YACRwK,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBC,IAAI,EAAC,QAAQ;YACb3B,SAAS;YACTrC,IAAI,EAAC,cAAc;YACnBnC,KAAK,EAAEhC,QAAQ,CAACE,YAAa;YAC7BkI,QAAQ,EAAElE,gBAAiB;YAC3BmE,UAAU,EAAE5C,cAAe;YAC3B3B,KAAK,EAAEwE,OAAO,CAAClI,UAAU,CAACF,YAAY,CAAE;YACxCqI,UAAU,EAAEnI,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAY,IAAI,kBAAkBR,IAAI,CAAC6E,aAAa,GAAI;YAC5GiE,mBAAmB,EAAE;cACnB7B,EAAE,EAAE;gBAAE8B,KAAK,EAAEnI,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACFwI,QAAQ,EAAElI,MAAM,IAAIV,OAAQ;YAC5B6I,UAAU,EAAE;cACVC,YAAY,eAAExJ,OAAA,CAAC1B,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAAC0B,KAAK,EAAC,gBAAgB;gBAAAnC,QAAA,EAAC;cAAC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAChF,CAAE;YACFyB,UAAU,EAAE;cACVC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE;YACR,CAAE;YACFpC,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNhI,OAAA,CAACzB,GAAG;UAACgJ,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAjB,QAAA,gBACjBlH,OAAA,CAAC1B,UAAU;YAACqJ,OAAO,EAAC,WAAW;YAACJ,EAAE,EAAE;cAAEqC,UAAU,EAAE,MAAM;cAAEzB,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,EAAC;UAEnE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAGbhI,OAAA,CAACzB,GAAG;YAACgJ,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,eACjBlH,OAAA,CAACxB,IAAI;cAAC8J,SAAS;cAACC,OAAO,EAAE,CAAE;cAACsB,UAAU,EAAC,QAAQ;cAAA3C,QAAA,gBAE7ClH,OAAA,CAACxB,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACqB,EAAE,EAAE,CAAE;gBAACpB,EAAE,EAAE,CAAE;gBAAAxB,QAAA,eAC9BlH,OAAA,CAAC3B,SAAS;kBACR0L,IAAI,EAAC,OAAO;kBACZjB,KAAK,EAAC,OAAO;kBACbnB,OAAO,EAAC,UAAU;kBAClB/E,KAAK,EAAElB,UAAW;kBAClBsH,QAAQ,EAAEvG,sBAAuB;kBACjCuH,WAAW,EAAC,8BAA8B;kBAC1C5C,SAAS;kBACTmC,UAAU,EAAE;oBACVU,cAAc,eACZjK,OAAA,CAACf,cAAc;sBAACiL,QAAQ,EAAC,OAAO;sBAAAhD,QAAA,eAC9BlH,OAAA,CAACT,UAAU;wBAAC4K,QAAQ,EAAC;sBAAO;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CACjB;oBACDwB,YAAY,EAAE9H,UAAU,gBACtB1B,OAAA,CAACf,cAAc;sBAACiL,QAAQ,EAAC,KAAK;sBAAAhD,QAAA,eAC5BlH,OAAA,CAACd,UAAU;wBACT6K,IAAI,EAAC,OAAO;wBACZ,cAAW,cAAc;wBACzBK,OAAO,EAAEA,CAAA,KAAMzI,aAAa,CAAC,EAAE,CAAE;wBACjC0I,IAAI,EAAC,KAAK;wBAAAnD,QAAA,eAEVlH,OAAA,CAACP,UAAU;0BAAC0K,QAAQ,EAAC;wBAAO;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,GACf;kBACN;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGPhI,OAAA,CAACxB,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACqB,EAAE,EAAE,CAAE;gBAACpB,EAAE,EAAE,CAAE;gBAAAxB,QAAA,eAC9BlH,OAAA,CAACpB,WAAW;kBAACmL,IAAI,EAAC,OAAO;kBAAC3C,SAAS;kBAAAF,QAAA,gBACjClH,OAAA,CAACnB,UAAU;oBAACyL,EAAE,EAAC,mBAAmB;oBAAApD,QAAA,EAAC;kBAAY;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5DhI,OAAA,CAAClB,MAAM;oBACLyL,OAAO,EAAC,mBAAmB;oBAC3B3H,KAAK,EAAEhB,UAAW;oBAClBkH,KAAK,EAAC,cAAc;oBACpBE,QAAQ,EAAGwB,CAAC,IAAK3I,aAAa,CAAC2I,CAAC,CAAC7H,MAAM,CAACC,KAAK,CAAE;oBAAAsE,QAAA,gBAE/ClH,OAAA,CAACjB,QAAQ;sBAAC6D,KAAK,EAAC,UAAU;sBAAAsE,QAAA,EAAC;oBAAQ;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAC9ChI,OAAA,CAACjB,QAAQ;sBAAC6D,KAAK,EAAC,QAAQ;sBAAAsE,QAAA,EAAC;oBAAQ;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGPhI,OAAA,CAACxB,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACqB,EAAE,EAAE,EAAG;gBAACpB,EAAE,EAAE,CAAE;gBAAAxB,QAAA,eAC/BlH,OAAA,CAAC5B,MAAM;kBACLuJ,OAAO,EAAC,UAAU;kBAClBoC,IAAI,EAAC,QAAQ;kBACbK,OAAO,EAAEA,CAAA,KAAMlI,kBAAkB,CAAC,cAAc,CAAE;kBAClDkF,SAAS;kBACTG,EAAE,EAAE;oBACFkD,MAAM,EAAE,MAAM;oBACdb,UAAU,EAAEhJ,QAAQ,CAACG,SAAS,KAAK,cAAc,GAAG,MAAM,GAAG,QAAQ;oBACrEqH,OAAO,EAAExH,QAAQ,CAACG,SAAS,KAAK,cAAc,GAAG,yBAAyB,GAAG,SAAS;oBACtF2J,MAAM,EAAE9J,QAAQ,CAACG,SAAS,KAAK,cAAc,GAAG,mBAAmB,GAAG4J;kBACxE,CAAE;kBAAAzD,QAAA,EACH;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAELxG,aAAa,gBACZxB,OAAA,CAACzB,GAAG;YAACgJ,EAAE,EAAE;cAAEqD,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA5D,QAAA,eAC5DlH,OAAA,CAACrB,gBAAgB;cAACoL,IAAI,EAAE;YAAG;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,gBAENhI,OAAA,CAACzB,GAAG;YAAA2I,QAAA,eAEFlH,OAAA,CAACxB,IAAI;cAAC8J,SAAS;cAACC,OAAO,EAAE,CAAE;cAAArB,QAAA,gBAEzBlH,OAAA,CAACxB,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxB,QAAA,eACvBlH,OAAA,CAACvB,KAAK;kBAACkJ,OAAO,EAAC,UAAU;kBAACJ,EAAE,EAAE;oBAAEW,CAAC,EAAE,CAAC;oBAAEuC,MAAM,EAAE;kBAAO,CAAE;kBAAAvD,QAAA,gBACrDlH,OAAA,CAAC1B,UAAU;oBAACqJ,OAAO,EAAC,WAAW;oBAACJ,EAAE,EAAE;sBAAEqC,UAAU,EAAE,MAAM;sBAAEzB,EAAE,EAAE;oBAAE,CAAE;oBAAAjB,QAAA,EAAC;kBAEnE;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EAEZlE,iBAAiB,CAACd,MAAM,GAAG,CAAC,gBAC3BhD,OAAA,CAAAE,SAAA;oBAAAgH,QAAA,gBACElH,OAAA,CAACzB,GAAG;sBAACgJ,EAAE,EAAE;wBAAEqD,OAAO,EAAE,MAAM;wBAAEf,UAAU,EAAE,QAAQ;wBAAEkB,KAAK,EAAE,MAAM;wBAAEC,EAAE,EAAE,CAAC;wBAAEC,EAAE,EAAE,CAAC;wBAAE7C,OAAO,EAAE,SAAS;wBAAEC,YAAY,EAAE,CAAC;wBAAEF,EAAE,EAAE;sBAAE,CAAE;sBAAAjB,QAAA,gBAC1HlH,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEwD,KAAK,EAAE,KAAK;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAhE,QAAA,eAC/BlH,OAAA,CAAC1B,UAAU;0BAACqJ,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAEqC,UAAU,EAAE,MAAM;4BAAEO,QAAQ,EAAE;0BAAU,CAAE;0BAAAjD,QAAA,EAAC;wBAAE;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3F,CAAC,eACNhI,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEwD,KAAK,EAAE,KAAK;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAhE,QAAA,eAC/BlH,OAAA,CAAC1B,UAAU;0BAACqJ,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAEqC,UAAU,EAAE,MAAM;4BAAEO,QAAQ,EAAE;0BAAU,CAAE;0BAAAjD,QAAA,EAAC;wBAAI;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7F,CAAC,eACNhI,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEwD,KAAK,EAAE,KAAK;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAhE,QAAA,eAC/BlH,OAAA,CAAC1B,UAAU;0BAACqJ,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAEqC,UAAU,EAAE,MAAM;4BAAEO,QAAQ,EAAE;0BAAU,CAAE;0BAAAjD,QAAA,EAAC;wBAAK;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9F,CAAC,eACNhI,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEwD,KAAK,EAAE,KAAK;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAhE,QAAA,eAC/BlH,OAAA,CAAC1B,UAAU;0BAACqJ,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAEqC,UAAU,EAAE,MAAM;4BAAEO,QAAQ,EAAE;0BAAU,CAAE;0BAAAjD,QAAA,EAAC;wBAAO;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChG,CAAC,eACNhI,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEwD,KAAK,EAAE;wBAAM,CAAE;wBAAA7D,QAAA,eACxBlH,OAAA,CAAC1B,UAAU;0BAACqJ,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAEqC,UAAU,EAAE,MAAM;4BAAEO,QAAQ,EAAE;0BAAU,CAAE;0BAAAjD,QAAA,EAAC;wBAAK;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9F,CAAC,eACNhI,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEwD,KAAK,EAAE;wBAAK;sBAAE;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEpB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhI,OAAA,CAACb,IAAI;sBAACoI,EAAE,EAAE;wBAAEE,SAAS,EAAE3D,iBAAiB,CAACd,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;wBAAEmI,SAAS,EAAErH,iBAAiB,CAACd,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS;wBAAEoI,SAAS,EAAE,QAAQ;wBAAEhD,OAAO,EAAE;sBAAmB,CAAE;sBAAAlB,QAAA,EACtLpD,iBAAiB,CAAC6C,GAAG,CAAExD,MAAM,iBAC5BnD,OAAA,CAACZ,QAAQ;wBAEPiM,cAAc;wBACdC,eAAe,eACbtL,OAAA,CAACd,UAAU;0BACTmL,IAAI,EAAC,KAAK;0BACVN,IAAI,EAAC,OAAO;0BACZK,OAAO,EAAEA,CAAA,KAAMlI,kBAAkB,CAACiB,MAAM,CAACpC,SAAS,CAAE;0BAAAmG,QAAA,eAEpDlH,OAAA,CAACL,oBAAoB;4BAAC0J,KAAK,EAAC;0BAAS;4BAAAxB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CACb;wBACDT,EAAE,EAAE;0BACFa,OAAO,EAAExH,QAAQ,CAACG,SAAS,KAAKoC,MAAM,CAACpC,SAAS,GAAG,yBAAyB,GAAG,SAAS;0BACxFsH,YAAY,EAAE,KAAK;0BACnBF,EAAE,EAAE,GAAG;0BACPuC,MAAM,EAAE9J,QAAQ,CAACG,SAAS,KAAKoC,MAAM,CAACpC,SAAS,GAAG,mBAAmB,GAAG;wBAC1E,CAAE;wBAAAmG,QAAA,eAEFlH,OAAA,CAACX,cAAc;0BACbkM,KAAK;0BACLnB,OAAO,EAAEA,CAAA,KAAMlI,kBAAkB,CAACiB,MAAM,CAACpC,SAAS,CAAE;0BAAAmG,QAAA,eAEpDlH,OAAA,CAACzB,GAAG;4BAACgJ,EAAE,EAAE;8BAAEqD,OAAO,EAAE,MAAM;8BAAEf,UAAU,EAAE,QAAQ;8BAAEkB,KAAK,EAAE,MAAM;8BAAEC,EAAE,EAAE;4BAAI,CAAE;4BAAA9D,QAAA,gBACzElH,OAAA,CAACzB,GAAG;8BAACgJ,EAAE,EAAE;gCAAEwD,KAAK,EAAE,MAAM;gCAAEG,EAAE,EAAE;8BAAE,CAAE;8BAAAhE,QAAA,eAChClH,OAAA,CAAC1B,UAAU;gCAACqJ,OAAO,EAAC,OAAO;gCAACJ,EAAE,EAAE;kCAAEqC,UAAU,EAAE,MAAM;kCAAEO,QAAQ,EAAE;gCAAS,CAAE;gCAAAjD,QAAA,EACxEpF,eAAe,CAACqB,MAAM,CAACpC,SAAS;8BAAC;gCAAA8G,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACxB;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACNhI,OAAA,CAACzB,GAAG;8BAACgJ,EAAE,EAAE;gCAAEwD,KAAK,EAAE,OAAO;gCAAEG,EAAE,EAAE;8BAAE,CAAE;8BAAAhE,QAAA,eACjClH,OAAA,CAAC1B,UAAU;gCAACqJ,OAAO,EAAC,OAAO;gCAACJ,EAAE,EAAE;kCAAE4C,QAAQ,EAAE;gCAAU,CAAE;gCAAAjD,QAAA,EACrD/D,MAAM,CAACG,SAAS,IAAI;8BAAK;gCAAAuE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAChB;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACNhI,OAAA,CAACzB,GAAG;8BAACgJ,EAAE,EAAE;gCAAEwD,KAAK,EAAE,OAAO;gCAAEG,EAAE,EAAE;8BAAE,CAAE;8BAAAhE,QAAA,eACjClH,OAAA,CAAC1B,UAAU;gCAACqJ,OAAO,EAAC,OAAO;gCAACJ,EAAE,EAAE;kCAAE4C,QAAQ,EAAE;gCAAU,CAAE;gCAAAjD,QAAA,EACrD/D,MAAM,CAACI,OAAO,IAAI;8BAAK;gCAAAsE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACd;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACNhI,OAAA,CAACzB,GAAG;8BAACgJ,EAAE,EAAE;gCAAEwD,KAAK,EAAE,OAAO;gCAAEG,EAAE,EAAE;8BAAE,CAAE;8BAAAhE,QAAA,eACjClH,OAAA,CAAC1B,UAAU;gCAACqJ,OAAO,EAAC,OAAO;gCAACJ,EAAE,EAAE;kCAAEqC,UAAU,EAAE,MAAM;kCAAEO,QAAQ,EAAE,SAAS;kCAAEd,KAAK,EAAElG,MAAM,CAACE,aAAa,GAAG4B,UAAU,CAACrE,QAAQ,CAACE,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;gCAAe,CAAE;gCAAAoG,QAAA,GAC/K/D,MAAM,CAACE,aAAa,IAAI,CAAC,EAAC,IAC7B;8BAAA;gCAAAwE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAY;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACNhI,OAAA,CAACzB,GAAG;8BAACgJ,EAAE,EAAE;gCAAEiE,QAAQ,EAAE;8BAAE,CAAE;8BAAAtE,QAAA,eACvBlH,OAAA,CAAChB,IAAI;gCACH+K,IAAI,EAAC,OAAO;gCACZjB,KAAK,EAAE3F,MAAM,CAACC,YAAY,IAAI,KAAM;gCACpCiG,KAAK,EAAEvJ,iBAAiB,CAACqD,MAAM,CAACC,YAAY,CAAE;gCAC9CuE,OAAO,EAAC,UAAU;gCAClBJ,EAAE,EAAE;kCAAEkD,MAAM,EAAE,EAAE;kCAAEN,QAAQ,EAAE,QAAQ;kCAAE,kBAAkB,EAAE;oCAAEc,EAAE,EAAE,CAAC;oCAAED,EAAE,EAAE;kCAAE;gCAAE;8BAAE;gCAAAnD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9E;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC,GArDZ7E,MAAM,CAACpC,SAAS;wBAAA8G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAsDb,CACX;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA,eACP,CAAC,gBAEHhI,OAAA,CAACtB,KAAK;oBAAC+M,QAAQ,EAAC,MAAM;oBAAClE,EAAE,EAAE;sBAAEmE,EAAE,EAAE;oBAAE,CAAE;oBAAAxE,QAAA,EAAC;kBAEtC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACR;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAGPhI,OAAA,CAACxB,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxB,QAAA,eACvBlH,OAAA,CAACvB,KAAK;kBAACkJ,OAAO,EAAC,UAAU;kBAACJ,EAAE,EAAE;oBAAEW,CAAC,EAAE,CAAC;oBAAEuC,MAAM,EAAE;kBAAO,CAAE;kBAAAvD,QAAA,gBACrDlH,OAAA,CAAC1B,UAAU;oBAACqJ,OAAO,EAAC,WAAW;oBAACJ,EAAE,EAAE;sBAAEqC,UAAU,EAAE,MAAM;sBAAEzB,EAAE,EAAE;oBAAE,CAAE;oBAAAjB,QAAA,EAAC;kBAEnE;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EAEZjE,oBAAoB,CAACf,MAAM,GAAG,CAAC,gBAC9BhD,OAAA,CAAAE,SAAA;oBAAAgH,QAAA,gBACElH,OAAA,CAACzB,GAAG;sBAACgJ,EAAE,EAAE;wBAAEqD,OAAO,EAAE,MAAM;wBAAEf,UAAU,EAAE,QAAQ;wBAAEkB,KAAK,EAAE,MAAM;wBAAEC,EAAE,EAAE,GAAG;wBAAEC,EAAE,EAAE,GAAG;wBAAE7C,OAAO,EAAE,SAAS;wBAAEC,YAAY,EAAE,CAAC;wBAAEF,EAAE,EAAE;sBAAE,CAAE;sBAAAjB,QAAA,gBAC9HlH,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEwD,KAAK,EAAE,MAAM;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAhE,QAAA,eAChClH,OAAA,CAAC1B,UAAU;0BAACqJ,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAEqC,UAAU,EAAE,MAAM;4BAAEO,QAAQ,EAAE;0BAAU,CAAE;0BAAAjD,QAAA,EAAC;wBAAE;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3F,CAAC,eACNhI,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEwD,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAhE,QAAA,eACjClH,OAAA,CAAC1B,UAAU;0BAACqJ,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAEqC,UAAU,EAAE,MAAM;4BAAEO,QAAQ,EAAE;0BAAU,CAAE;0BAAAjD,QAAA,EAAC;wBAAI;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7F,CAAC,eACNhI,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEwD,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAhE,QAAA,eACjClH,OAAA,CAAC1B,UAAU;0BAACqJ,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAEqC,UAAU,EAAE,MAAM;4BAAEO,QAAQ,EAAE;0BAAU,CAAE;0BAAAjD,QAAA,EAAC;wBAAK;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9F,CAAC,eACNhI,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEwD,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAhE,QAAA,eACjClH,OAAA,CAAC1B,UAAU;0BAACqJ,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAEqC,UAAU,EAAE,MAAM;4BAAEO,QAAQ,EAAE;0BAAU,CAAE;0BAAAjD,QAAA,EAAC;wBAAO;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChG,CAAC,eACNhI,OAAA,CAACzB,GAAG;wBAACgJ,EAAE,EAAE;0BAAEiE,QAAQ,EAAE;wBAAE,CAAE;wBAAAtE,QAAA,eACvBlH,OAAA,CAAC1B,UAAU;0BAACqJ,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAEqC,UAAU,EAAE,MAAM;4BAAEO,QAAQ,EAAE;0BAAU,CAAE;0BAAAjD,QAAA,EAAC;wBAAK;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9F,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhI,OAAA,CAACb,IAAI;sBAACoI,EAAE,EAAE;wBAAEE,SAAS,EAAE1D,oBAAoB,CAACf,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;wBAAEmI,SAAS,EAAEpH,oBAAoB,CAACf,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS;wBAAEoI,SAAS,EAAE,QAAQ;wBAAEhD,OAAO,EAAE;sBAAmB,CAAE;sBAAAlB,QAAA,EAC5LnD,oBAAoB,CAAC4C,GAAG,CAAExD,MAAM,iBAC/BnD,OAAA,CAACZ,QAAQ;wBAEPiM,cAAc;wBACdC,eAAe,eACbtL,OAAA,CAACd,UAAU;0BACTmL,IAAI,EAAC,KAAK;0BACVN,IAAI,EAAC,OAAO;0BACZK,OAAO,EAAEA,CAAA,KAAMlI,kBAAkB,CAACiB,MAAM,CAACpC,SAAS,CAAE;0BAAAmG,QAAA,eAEpDlH,OAAA,CAACL,oBAAoB;4BAAC0J,KAAK,EAAC;0BAAS;4BAAAxB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CACb;wBACDT,EAAE,EAAE;0BACFa,OAAO,EAAExH,QAAQ,CAACG,SAAS,KAAKoC,MAAM,CAACpC,SAAS,GAAG,yBAAyB,GAAG,SAAS;0BACxFsH,YAAY,EAAE,KAAK;0BACnBF,EAAE,EAAE,GAAG;0BACPuC,MAAM,EAAE9J,QAAQ,CAACG,SAAS,KAAKoC,MAAM,CAACpC,SAAS,GAAG,mBAAmB,GAAG;wBAC1E,CAAE;wBAAAmG,QAAA,eAEFlH,OAAA,CAACX,cAAc;0BACbkM,KAAK;0BACLnB,OAAO,EAAEA,CAAA,KAAMlI,kBAAkB,CAACiB,MAAM,CAACpC,SAAS,CAAE;0BAAAmG,QAAA,eAEpDlH,OAAA,CAACzB,GAAG;4BAACgJ,EAAE,EAAE;8BAAEqD,OAAO,EAAE,MAAM;8BAAEf,UAAU,EAAE,QAAQ;8BAAEkB,KAAK,EAAE,MAAM;8BAAEC,EAAE,EAAE;4BAAI,CAAE;4BAAA9D,QAAA,gBACzElH,OAAA,CAACzB,GAAG;8BAACgJ,EAAE,EAAE;gCAAEwD,KAAK,EAAE,MAAM;gCAAEG,EAAE,EAAE;8BAAE,CAAE;8BAAAhE,QAAA,eAChClH,OAAA,CAAC1B,UAAU;gCAACqJ,OAAO,EAAC,OAAO;gCAACJ,EAAE,EAAE;kCAAEqC,UAAU,EAAE,MAAM;kCAAEO,QAAQ,EAAE;gCAAS,CAAE;gCAAAjD,QAAA,EACxEpF,eAAe,CAACqB,MAAM,CAACpC,SAAS;8BAAC;gCAAA8G,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACxB;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACNhI,OAAA,CAACzB,GAAG;8BAACgJ,EAAE,EAAE;gCAAEwD,KAAK,EAAE,OAAO;gCAAEG,EAAE,EAAE;8BAAE,CAAE;8BAAAhE,QAAA,eACjClH,OAAA,CAAC1B,UAAU;gCAACqJ,OAAO,EAAC,OAAO;gCAACJ,EAAE,EAAE;kCAAE4C,QAAQ,EAAE;gCAAU,CAAE;gCAAAjD,QAAA,EACrD/D,MAAM,CAACG,SAAS,IAAI;8BAAK;gCAAAuE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAChB;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACNhI,OAAA,CAACzB,GAAG;8BAACgJ,EAAE,EAAE;gCAAEwD,KAAK,EAAE,OAAO;gCAAEG,EAAE,EAAE;8BAAE,CAAE;8BAAAhE,QAAA,eACjClH,OAAA,CAAC1B,UAAU;gCAACqJ,OAAO,EAAC,OAAO;gCAACJ,EAAE,EAAE;kCAAE4C,QAAQ,EAAE;gCAAU,CAAE;gCAAAjD,QAAA,EACrD/D,MAAM,CAACI,OAAO,IAAI;8BAAK;gCAAAsE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACd;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACNhI,OAAA,CAACzB,GAAG;8BAACgJ,EAAE,EAAE;gCAAEwD,KAAK,EAAE,OAAO;gCAAEG,EAAE,EAAE;8BAAE,CAAE;8BAAAhE,QAAA,eACjClH,OAAA,CAAC1B,UAAU;gCAACqJ,OAAO,EAAC,OAAO;gCAACJ,EAAE,EAAE;kCAAEqC,UAAU,EAAE,MAAM;kCAAEO,QAAQ,EAAE,SAAS;kCAAEd,KAAK,EAAElG,MAAM,CAACE,aAAa,GAAG4B,UAAU,CAACrE,QAAQ,CAACE,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;gCAAe,CAAE;gCAAAoG,QAAA,GAC/K/D,MAAM,CAACE,aAAa,IAAI,CAAC,EAAC,IAC7B;8BAAA;gCAAAwE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAY;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACV,CAAC,eACNhI,OAAA,CAACzB,GAAG;8BAACgJ,EAAE,EAAE;gCAAEqD,OAAO,EAAE,MAAM;gCAAEe,GAAG,EAAE;8BAAE,CAAE;8BAAAzE,QAAA,gBACnClH,OAAA,CAAChB,IAAI;gCACH+K,IAAI,EAAC,OAAO;gCACZjB,KAAK,EAAE3F,MAAM,CAACC,YAAY,IAAI,KAAM;gCACpCiG,KAAK,EAAEvJ,iBAAiB,CAACqD,MAAM,CAACC,YAAY,CAAE;gCAC9CuE,OAAO,EAAC,UAAU;gCAClBJ,EAAE,EAAE;kCAAEkD,MAAM,EAAE,EAAE;kCAAEN,QAAQ,EAAE,QAAQ;kCAAE,kBAAkB,EAAE;oCAAEc,EAAE,EAAE,CAAC;oCAAED,EAAE,EAAE;kCAAE;gCAAE;8BAAE;gCAAAnD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9E,CAAC,eACFhI,OAAA,CAAChB,IAAI;gCACH+K,IAAI,EAAC,OAAO;gCACZjB,KAAK,EAAC,WAAW;gCACjBO,KAAK,EAAC,SAAS;gCACf1B,OAAO,EAAC,UAAU;gCAClBJ,EAAE,EAAE;kCAAEkD,MAAM,EAAE,EAAE;kCAAEN,QAAQ,EAAE,QAAQ;kCAAE,kBAAkB,EAAE;oCAAEc,EAAE,EAAE,CAAC;oCAAED,EAAE,EAAE;kCAAE;gCAAE;8BAAE;gCAAAnD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9E,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC,GA5DZ7E,MAAM,CAACpC,SAAS;wBAAA8G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA6Db,CACX;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA,eACP,CAAC,gBAEHhI,OAAA,CAACtB,KAAK;oBAAC+M,QAAQ,EAAC,MAAM;oBAAClE,EAAE,EAAE;sBAAEmE,EAAE,EAAE;oBAAE,CAAE;oBAAAxE,QAAA,EAAC;kBAEtC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACR;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN,EAEA1G,MAAM,CAAC0B,MAAM,KAAK,CAAC,IAAI,CAACxB,aAAa,iBACpCxB,OAAA,CAACtB,KAAK;YAAC+M,QAAQ,EAAC,SAAS;YAAClE,EAAE,EAAE;cAAEmE,EAAE,EAAE;YAAE,CAAE;YAAAxE,QAAA,EAAC;UAEzC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR,EAEAhH,UAAU,CAACD,SAAS,iBACnBf,OAAA,CAAC1B,UAAU;YAACqJ,OAAO,EAAC,SAAS;YAAC0B,KAAK,EAAC,OAAO;YAAC9B,EAAE,EAAE;cAAEmE,EAAE,EAAE,CAAC;cAAEd,OAAO,EAAE;YAAQ,CAAE;YAAA1D,QAAA,EACzElG,UAAU,CAACD;UAAS;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEhBhI,OAAA,CAAC7B,aAAa;QAAA+I,QAAA,gBACZlH,OAAA,CAAC5B,MAAM;UACLgM,OAAO,EAAExE,WAAY;UACrB0D,QAAQ,EAAElI,MAAM,IAAIV,OAAQ;UAAAwG,QAAA,EAC7B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA,CAAC5B,MAAM;UACLgM,OAAO,EAAE5E,UAAW;UACpBmC,OAAO,EAAC,WAAW;UACnB2B,QAAQ,EAAElI,MAAM,IAAIV,OAAO,IAAI,CAACE,QAAQ,CAACE,YAAY,IAAI,CAACF,QAAQ,CAACG,SAAU;UAC7E6K,SAAS,EAAExK,MAAM,gBAAGpB,OAAA,CAACrB,gBAAgB;YAACoL,IAAI,EAAE;UAAG;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAK;UAAAd,QAAA,EAEzD9F,MAAM,GAAG,aAAa,GAAG;QAAO;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC,gBACT,CAAC;AAEP,CAAC;AAACrH,EAAA,CA/yBIR,4BAA4B;AAAA0L,EAAA,GAA5B1L,4BAA4B;AAizBlC,eAAeA,4BAA4B;AAAC,IAAA0L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}