/* 
 * Report Pages Enhanced Styling
 * Miglioramenti per l'esperienza utente delle pagine dei report
 */

/* Animazioni personalizzate */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Classi per animazioni */
.report-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.report-slide-in {
  animation: slideInUp 0.6s ease-out;
}

.report-scale-in {
  animation: scaleIn 0.4s ease-out;
}

/* Miglioramenti per le cards dei report */
.report-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px !important;
  overflow: hidden;
}

.report-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15) !important;
}

.report-card-selected {
  border: 2px solid #3498db !important;
  box-shadow: 0 8px 16px rgba(52, 152, 219, 0.2) !important;
}

/* Gradients personalizzati per le metriche */
.metric-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-gradient-success {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-gradient-info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-gradient-warning {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* Stili per le tabelle dei report */
.report-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.report-table-header {
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
  font-weight: 600;
  color: #2c3e50;
}

.report-table-row:hover {
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

/* Stili per i grafici */
.report-chart-container {
  border-radius: 12px;
  padding: 20px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.report-chart-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
  text-align: center;
}

/* Responsive design migliorato */
@media (max-width: 768px) {
  .report-card {
    margin-bottom: 16px;
  }

  .metric-card {
    min-height: 120px;
  }

  .report-navigation {
    flex-direction: column;
    gap: 12px;
  }

  .report-export-buttons {
    flex-direction: column;
    width: 100%;
  }

  .report-export-buttons button {
    width: 100%;
    margin-bottom: 8px;
  }

  .report-main-container {
    padding: 12px;
  }

  .report-chart-container {
    padding: 16px;
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .report-title {
    font-size: 1.5rem !important;
  }
  
  .metric-value {
    font-size: 1.8rem !important;
  }
  
  .report-section-padding {
    padding: 16px !important;
  }
}

/* Stili per gli stati di caricamento */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Stili per i badge e chip */
.report-badge {
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.report-badge-success {
  background-color: #d4edda;
  color: #155724;
}

.report-badge-warning {
  background-color: #fff3cd;
  color: #856404;
}

.report-badge-danger {
  background-color: #f8d7da;
  color: #721c24;
}

.report-badge-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

/* Stili per i tooltip personalizzati */
.report-tooltip {
  background-color: #2c3e50 !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 0.875rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Stili per le progress bar */
.report-progress {
  height: 8px;
  border-radius: 4px;
  background-color: #e9ecef;
  overflow: hidden;
}

.report-progress-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s ease;
}

.report-progress-success {
  background: linear-gradient(90deg, #28a745, #20c997);
}

.report-progress-warning {
  background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.report-progress-danger {
  background: linear-gradient(90deg, #dc3545, #e83e8c);
}

/* Stili per le sezioni collassabili */
.report-collapsible-header {
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 8px;
  padding: 12px 16px;
}

.report-collapsible-header:hover {
  background-color: #f8f9fa;
}

.report-collapsible-content {
  padding: 16px;
  border-top: 1px solid #e9ecef;
}

/* Stili per i pulsanti di esportazione */
.export-button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.export-button {
  min-width: 80px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.export-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Stili per le icone */
.report-icon {
  transition: transform 0.2s ease;
}

.report-icon:hover {
  transform: scale(1.1);
}

/* Stili per il layout principale */
.report-main-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 16px;
  width: 100%;
}

.report-section {
  margin-bottom: 32px;
}

.report-section:last-child {
  margin-bottom: 0;
}

/* Stili per i grafici migliorati */
.report-chart-full-width {
  width: 100% !important;
  max-width: none !important;
}

.report-chart-responsive {
  overflow-x: auto;
  min-height: 400px;
}

/* Miglioramenti per le tabelle responsive */
.report-table-responsive {
  overflow-x: auto;
  width: 100%;
}

.report-table-responsive table {
  min-width: 1000px;
}

/* Stili per metriche più grandi */
.metric-card-large {
  min-height: 160px;
}

.metric-card-large .metric-value {
  font-size: 2.5rem !important;
}

.metric-card-large .metric-title {
  font-size: 1.2rem !important;
}
