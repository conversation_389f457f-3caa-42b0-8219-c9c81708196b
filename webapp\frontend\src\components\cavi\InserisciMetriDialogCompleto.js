import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Grid,
  Paper,
  Alert,
  CircularProgress,
  Chip,
  InputAdornment,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText
} from '@mui/material';
import {
  Search as SearchIcon,
  Cancel as CancelIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import caviService from '../../services/caviService';
import parcoCaviService from '../../services/parcoCaviService';
import IncompatibleReelDialog from './IncompatibleReelDialog';
import { getReelStateColor } from '../../utils/stateUtils';

/**
 * Dialog completo per inserire i metri posati di un cavo preselezionato
 * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato
 * 
 * @param {Object} props - Proprietà del componente
 * @param {boolean} props.open - Se il dialogo è aperto
 * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude
 * @param {Object} props.cavo - Cavo preselezionato
 * @param {string} props.cantiereId - ID del cantiere
 * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione
 * @param {Function} props.onError - Funzione chiamata in caso di errore
 * @param {boolean} props.loading - Indica se il salvataggio è in corso
 */
const InserisciMetriDialogCompleto = ({
  open = false,
  onClose = () => {},
  cavo = null,
  cantiereId,
  onSuccess = () => {},
  onError = () => {},
  loading = false
}) => {
  // Stati per il form
  const [formData, setFormData] = useState({
    metri_posati: '',
    id_bobina: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [formWarnings, setFormWarnings] = useState({});
  const [saving, setSaving] = useState(false);
  
  // Stati per bobine
  const [bobine, setBobine] = useState([]);
  const [bobineLoading, setBobineLoading] = useState(false);

  // Stati per la ricerca delle bobine
  const [searchText, setSearchText] = useState('');

  // Stati per dialoghi
  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);
  const [incompatibleReel, setIncompatibleReel] = useState(null);

  // Funzione per estrarre il numero della bobina dall'ID completo
  const getBobinaNumber = (idBobina) => {
    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA';
    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}
    if (idBobina && idBobina.includes('_B')) {
      return idBobina.split('_B')[1];
    }
    // Cerca nella lista bobine per ottenere il numero_bobina
    const bobina = bobine.find(b => b.id_bobina === idBobina);
    return bobina ? bobina.numero_bobina || idBobina : idBobina;
  };

  // Gestisce la selezione di una bobina compatibile
  const handleSelectBobinaCompatibile = (bobina) => {
    console.log('Bobina compatibile selezionata:', bobina);
    setFormData(prev => ({
      ...prev,
      id_bobina: bobina.id_bobina
    }));

    // Reset degli errori
    setFormErrors(prev => ({
      ...prev,
      id_bobina: ''
    }));
  };

  // Gestisce la selezione di una bobina incompatibile
  const handleSelectBobinaIncompatibile = (bobina) => {
    console.log('Bobina incompatibile selezionata:', bobina);
    setIncompatibleReel(bobina);
    setShowIncompatibleReelDialog(true);
  };

  // Gestisce l'uso di una bobina incompatibile
  const handleUseIncompatibleReel = async (bobina, cavo) => {
    try {
      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);

      // Seleziona la bobina incompatibile
      setFormData(prev => ({
        ...prev,
        id_bobina: bobina.id_bobina
      }));

      // Reset degli errori
      setFormErrors(prev => ({
        ...prev,
        id_bobina: ''
      }));

      setShowIncompatibleReelDialog(false);
    } catch (error) {
      console.error('Errore durante la selezione della bobina incompatibile:', error);
    }
  };

  // Gestisce la chiusura del dialog bobina incompatibile
  const handleCloseIncompatibleReelDialog = () => {
    setShowIncompatibleReelDialog(false);
    setIncompatibleReel(null);
  };

  // Gestisce la selezione di BOBINA_VUOTA
  const handleSelectBobinaVuota = () => {
    console.log('BOBINA_VUOTA selezionata');
    setFormData(prev => ({
      ...prev,
      id_bobina: 'BOBINA_VUOTA'
    }));

    // Reset degli errori
    setFormErrors(prev => ({
      ...prev,
      id_bobina: ''
    }));
  };

  // Gestisce il cambio del testo di ricerca
  const handleSearchTextChange = (event) => {
    setSearchText(event.target.value);
  };

  // Carica le bobine disponibili
  const loadBobine = useCallback(async () => {
    if (!cantiereId || !cavo) return;

    try {
      setBobineLoading(true);
      console.log('Caricamento bobine iniziato...');

      // Carica tutte le bobine disponibili
      const bobineData = await parcoCaviService.getBobine(cantiereId);
      console.log(`Bobine caricate: ${bobineData.length}`);

      // Filtra solo per stato (disponibile o in uso) e metri residui > 0
      const bobineUtilizzabili = bobineData.filter(bobina =>
        bobina.stato_bobina !== 'Terminata' &&
        bobina.stato_bobina !== 'Over' &&
        bobina.metri_residui > 0
      );

      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);

      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte
      if (cavo && cavo.tipologia && cavo.sezione) {
        console.log('Cavo selezionato, evidenziando bobine compatibili...');
        console.log('Dati cavo:', {
          id_cavo: cavo.id_cavo,
          tipologia: cavo.tipologia,
          sezione: cavo.sezione
        });

        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui
        const cavoTipologia = String(cavo.tipologia || '').trim().toLowerCase();
        const cavoSezione = String(cavo.sezione || '0').trim();

        // Identifica le bobine compatibili
        const bobineCompatibili = [];
        const bobineNonCompatibili = [];

        // Dividi le bobine in compatibili e non compatibili
        bobineUtilizzabili.forEach(bobina => {
          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();
          const bobinaSezione = String(bobina.sezione || '0').trim();

          // Verifica compatibilità
          const tipologiaMatch = bobinaTipologia === cavoTipologia;
          const sezioneMatch = bobinaSezione === cavoSezione;
          const isCompatible = tipologiaMatch && sezioneMatch;

          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {
            'Tipologia bobina': `"${bobina.tipologia}"`,
            'Tipologia cavo': `"${cavo.tipologia}"`,
            'Tipologie uguali?': tipologiaMatch,
            'Sezione bobina': `"${String(bobina.sezione)}"`,
            'Sezione cavo': `"${String(cavo.sezione)}"`,
            'Sezioni uguali?': sezioneMatch,
            'Stato bobina': bobina.stato_bobina,
            'Metri residui': bobina.metri_residui,
            'Compatibile?': isCompatible
          });

          if (isCompatible) {
            bobineCompatibili.push(bobina);
          } else {
            bobineNonCompatibili.push(bobina);
          }
        });

        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);
        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);

        // Log dettagliato delle bobine compatibili
        if (bobineCompatibili.length > 0) {
          console.log('Dettaglio bobine compatibili:');
          bobineCompatibili.forEach(bobina => {
            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);
          });
        } else {
          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');
        }

        // Ordina entrambi gli array per metri residui (decrescente)
        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);
        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);

        // Concatena gli array: prima le compatibili, poi le non compatibili
        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];

        // Imposta le bobine nel componente
        setBobine(bobineOrdinate);
      } else {
        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui
        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');
        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);
        setBobine(bobineUtilizzabili);
      }
    } catch (error) {
      console.error('Errore nel caricamento delle bobine:', error);
      setBobine([]);
    } finally {
      setBobineLoading(false);
    }
  }, [cantiereId, cavo]);

  // Reset quando si apre il dialogo
  useEffect(() => {
    if (open && cavo) {
      setFormData({
        metri_posati: cavo.metratura_reale?.toString() || '',
        id_bobina: ''
      });
      setFormErrors({});
      setFormWarnings({});
      setSaving(false);

      // Carica le bobine disponibili
      loadBobine();
    }
  }, [open, cavo, cantiereId, loadBobine]);

  // Gestisce i cambiamenti del form
  const handleFormChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Rimuovi errori quando l'utente inizia a digitare
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Validazione in tempo reale per metri posati
    if (name === 'metri_posati' && value && cavo) {
      const metri = parseFloat(value);
      if (!isNaN(metri) && metri > cavo.metri_teorici) {
        setFormWarnings(prev => ({
          ...prev,
          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`
        }));
      } else {
        setFormWarnings(prev => ({
          ...prev,
          metri_posati: ''
        }));
      }
    }
  };

  // Validazione del form
  const validateForm = () => {
    const errors = {};
    
    // Validazione metri posati
    if (!formData.metri_posati || formData.metri_posati.trim() === '') {
      errors.metri_posati = 'I metri posati sono obbligatori';
    } else {
      const metri = parseFloat(formData.metri_posati);
      if (isNaN(metri) || metri < 0) {
        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';
      }
    }
    
    // Validazione bobina
    if (!formData.id_bobina || formData.id_bobina === '') {
      errors.id_bobina = 'È necessario selezionare una bobina o utilizzare BOBINA VUOTA';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Gestisce il salvataggio
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch
    const metriPosati = parseFloat(formData.metri_posati);
    let idBobina = formData.id_bobina;

    try {
      setSaving(true);
      
      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');
      console.log('- cantiereId:', cantiereId);
      console.log('- id_cavo:', cavo.id_cavo);
      console.log('- metri_posati:', metriPosati);
      console.log('- id_bobina:', idBobina);
      
      // Chiamata API con la funzione originale completa
      await caviService.updateMetriPosati(
        cantiereId,
        cavo.id_cavo,
        metriPosati,
        idBobina,
        true // Forza sempre a true per evitare blocchi
      );
      
      // Successo
      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;
      onSuccess(successMessage);
      
      // Chiudi il dialog
      handleClose();
      
    } catch (error) {
      console.error('Errore durante l\'aggiornamento dei metri posati:', error);
      
      // Gestione speciale per BOBINA_VUOTA
      if (idBobina === 'BOBINA_VUOTA' && error.success) {
        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;
        onSuccess(successMessage);
        handleClose();
        return;
      }
      
      // Gestione errori
      let errorMessage = 'Errore durante l\'aggiornamento dei metri posati';
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      onError(errorMessage);
      
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    if (!saving && !loading) {
      setFormErrors({});
      setFormWarnings({});
      setFormData({ metri_posati: '', id_bobina: '' });
      onClose();
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {
      handleSave();
    }
  };

  if (!cavo) return null;

  // Filtra le bobine compatibili
  const getBobineCompatibili = () => {
    if (!cavo) return [];

    return bobine.filter(bobina => {
      const isCompatible = bobina.tipologia === cavo.tipologia &&
                          bobina.sezione === cavo.sezione;
      const matchesSearch = searchText === '' ||
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||
                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||
                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()));
      return isCompatible && matchesSearch && bobina.metri_residui > 0;
    });
  };

  // Filtra le bobine incompatibili
  const getBobineIncompatibili = () => {
    if (!cavo) return [];

    return bobine.filter(bobina => {
      const isIncompatible = bobina.tipologia !== cavo.tipologia ||
                            bobina.sezione !== cavo.sezione;
      const matchesSearch = searchText === '' ||
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||
                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||
                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()));
      return isIncompatible && matchesSearch && bobina.metri_residui > 0;
    });
  };

  const bobineCompatibili = getBobineCompatibili();
  const bobineIncompatibili = getBobineIncompatibili();

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="xl"
        fullWidth
        disableEscapeKeyDown={saving || loading}
        PaperProps={{
          sx: {
            minHeight: '700px',
            maxHeight: '90vh'
          }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h6" component="div">
            Inserisci Metri Posati - {cavo.id_cavo}
          </Typography>
        </DialogTitle>

        <DialogContent dividers sx={{ p: 3 }}>
          {/* Sezione informazioni cavo e metri posati */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {/* Informazioni cavo */}
            <Grid item xs={12} md={8}>
              <Paper sx={{ p: 2.5, height: '100%', bgcolor: '#f8f9fa', borderRadius: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>
                  Informazioni Cavo
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Sezione:</strong> {cavo.sezione || 'N/A'}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Già posati:</strong> {cavo.metratura_reale || 0} m
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            {/* Campo metri posati */}
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 2.5, height: '100%', borderRadius: 2, border: '2px solid #e3f2fd' }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>
                  Metri da Installare
                </Typography>
                <TextField
                  autoFocus
                  fullWidth
                  label="Metri Posati"
                  name="metri_posati"
                  type="number"
                  value={formData.metri_posati}
                  onChange={handleFormChange}
                  onKeyPress={handleKeyPress}
                  error={Boolean(formErrors.metri_posati)}
                  helperText={formErrors.metri_posati || formWarnings.metri_posati}
                  FormHelperTextProps={{
                    sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }
                  }}
                  disabled={saving || loading}
                  size="medium"
                  inputProps={{
                    max: 999999,
                    step: 0.1,
                    style: { fontSize: '1.1rem', fontWeight: 'bold', textAlign: 'center' }
                  }}
                  InputProps={{
                    endAdornment: <Typography variant="h6" color="primary" sx={{ fontWeight: 'bold' }}>m</Typography>
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: '#1976d2',
                        borderWidth: 2
                      },
                      '&:hover fieldset': {
                        borderColor: '#1565c0',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#0d47a1',
                      },
                    },
                  }}
                />
              </Paper>
            </Grid>
          </Grid>

          {/* Selezione bobina con doppia lista */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>
              Selezione Bobina
            </Typography>

            {/* Controlli di ricerca compatti */}
            <Paper sx={{ p: 2, mb: 3, bgcolor: '#fafafa', borderRadius: 2 }}>
              <Grid container spacing={2} alignItems="center">
                {/* Campo di ricerca */}
                <Grid item xs={12} sm={5}>
                  <TextField
                    size="small"
                    label="Cerca bobina"
                    variant="outlined"
                    value={searchText}
                    onChange={handleSearchTextChange}
                    placeholder="ID, tipologia, formazione..."
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon fontSize="small" />
                        </InputAdornment>
                      ),
                      endAdornment: searchText ? (
                        <InputAdornment position="end">
                          <IconButton
                            size="small"
                            aria-label="clear search"
                            onClick={() => setSearchText('')}
                            edge="end"
                          >
                            <CancelIcon fontSize="small" />
                          </IconButton>
                        </InputAdornment>
                      ) : null
                    }}
                  />
                </Grid>

                {/* Pulsante BOBINA VUOTA */}
                <Grid item xs={12} sm={7}>
                  <Button
                    variant={formData.id_bobina === 'BOBINA_VUOTA' ? 'contained' : 'outlined'}
                    size="medium"
                    onClick={handleSelectBobinaVuota}
                    fullWidth
                    color={formData.id_bobina === 'BOBINA_VUOTA' ? 'success' : 'primary'}
                    sx={{
                      height: '40px',
                      fontWeight: 'bold',
                      textTransform: 'none'
                    }}
                  >
                    BOBINA VUOTA
                  </Button>
                </Grid>
              </Grid>
            </Paper>

            {bobineLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>
                <CircularProgress size={24} />
              </Box>
            ) : (
              <Grid container spacing={3}>
                {/* Bobine compatibili */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom sx={{ color: 'success.main', fontWeight: 'medium' }}>
                    <CheckCircleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Bobine Compatibili ({bobineCompatibili.length})
                  </Typography>

                  <Box sx={{ maxHeight: 300, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    {bobineCompatibili.length === 0 ? (
                      <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                        Nessuna bobina compatibile trovata
                      </Box>
                    ) : (
                      <List dense>
                        {bobineCompatibili.map((bobina) => (
                          <ListItem
                            key={bobina.id_bobina}
                            disablePadding
                          >
                            <ListItemButton
                              selected={formData.id_bobina === bobina.id_bobina}
                              onClick={() => handleSelectBobinaCompatibile(bobina)}
                              sx={{
                                py: 1.5,
                                '&.Mui-selected': {
                                  backgroundColor: 'rgba(76, 175, 80, 0.1)',
                                  '&:hover': {
                                    backgroundColor: 'rgba(76, 175, 80, 0.2)',
                                  },
                                },
                              }}
                            >
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1, minWidth: 0 }}>
                                  <Typography variant="body1" fontWeight="bold" sx={{ fontSize: '1rem', minWidth: 'fit-content' }}>
                                    {bobina.numero_bobina || bobina.id_bobina}
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontSize: '0.95rem', color: 'text.secondary', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                    {bobina.tipologia} - {bobina.sezione}
                                  </Typography>
                                </Box>
                                <Chip
                                  size="small"
                                  label={`${bobina.metri_residui}m`}
                                  color="success"
                                  variant="outlined"
                                  sx={{ fontSize: '0.85rem', fontWeight: 'medium', minWidth: 'fit-content' }}
                                />
                              </Box>
                            </ListItemButton>
                          </ListItem>
                        ))}
                      </List>
                    )}
                  </Box>
                </Grid>

                {/* Bobine incompatibili */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom sx={{ color: 'warning.main', fontWeight: 'medium' }}>
                    <WarningIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Bobine Incompatibili ({bobineIncompatibili.length})
                  </Typography>

                  <Box sx={{ maxHeight: 300, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    {bobineIncompatibili.length === 0 ? (
                      <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                        Nessuna bobina incompatibile trovata
                      </Box>
                    ) : (
                      <List dense>
                        {bobineIncompatibili.map((bobina) => (
                          <ListItem
                            key={bobina.id_bobina}
                            disablePadding
                          >
                            <ListItemButton
                              onClick={() => handleSelectBobinaIncompatibile(bobina)}
                              sx={{
                                py: 1.5,
                                '&:hover': {
                                  backgroundColor: 'rgba(255, 152, 0, 0.1)',
                                },
                              }}
                            >
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1, minWidth: 0 }}>
                                  <Typography variant="body1" fontWeight="bold" sx={{ fontSize: '1rem', minWidth: 'fit-content' }}>
                                    {bobina.numero_bobina || bobina.id_bobina}
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontSize: '0.95rem', color: 'text.secondary', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                    {bobina.tipologia} - {bobina.sezione}
                                  </Typography>
                                </Box>
                                <Chip
                                  size="small"
                                  label={`${bobina.metri_residui}m`}
                                  color="warning"
                                  variant="outlined"
                                  sx={{ fontSize: '0.85rem', fontWeight: 'medium', minWidth: 'fit-content' }}
                                />
                              </Box>
                            </ListItemButton>
                          </ListItem>
                        ))}
                      </List>
                    )}
                  </Box>
                </Grid>
              </Grid>
            )}

            {bobine.length === 0 && !bobineLoading && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.
              </Alert>
            )}

            {formErrors.id_bobina && (
              <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
                {formErrors.id_bobina}
              </Typography>
            )}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleClose}
            disabled={saving || loading}
          >
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            variant="contained"
            disabled={saving || loading || !formData.metri_posati || !formData.id_bobina}
            startIcon={saving ? <CircularProgress size={20} /> : null}
          >
            {saving ? 'Salvando...' : 'Salva'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog bobina incompatibile */}
      <IncompatibleReelDialog
        open={showIncompatibleReelDialog}
        onClose={handleCloseIncompatibleReelDialog}
        bobina={incompatibleReel}
        cavo={cavo}
        onConfirm={handleUseIncompatibleReel}
      />
    </>
  );
};

export default InserisciMetriDialogCompleto;
